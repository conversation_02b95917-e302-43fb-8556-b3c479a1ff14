<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useCommonPageApi } from '#/api';
import { ToolsButton } from '#/components';
import Actions from '#/components/actions/index.vue';
import generaldetaildrawer from '#/components/general-detail-drawer/index.vue';
import generaleditingdrawer from '#/components/general-editing-drawer/index.vue';
import searchDrawerForm from '#/components/search-drawer-form/index.vue';
import { BUTTON_CONFIG } from '#/config/ui-config';
import { setGlobalPageEventHandler } from '#/utils/Actions/Actions';
import { createActionsProps } from '#/utils/Actions/Actionsmiddleware';
import { transformColumns } from '#/utils/Columns/GridColumns';
import { filterSearchData } from '#/utils/form/filterFormData';
import { processParams } from '#/utils/form/processParams';
import { transformBackendSearchToSchema } from '#/utils/search-schema/transform';

import { saveCustomSetting } from './data/datas';
// 使用新的 API 管理器
const { list, currentApi, getApiUrls, isApiReady } = useCommonPageApi({
  onLoaded: () => {
    // API 加载完成后手动触发数据加载
    setTimeout(() => {
      gridapi.query();
    }, 100);
  },
  onError: () => {
    // API 加载失败处理
  },
});

// 获取用户信息
const userStore = useUserStore();
const router = useRouter();

// 监听路由变化，清除表格状态
watch(
  () => router.currentRoute.value.fullPath,
  (newPath, oldPath) => {
    if (newPath !== oldPath) {
      // 清除搜索条件和表格状态
      Object.keys(_fromQuery).forEach((key) => delete _fromQuery[key]);
      rulesetManager.value = null;
      currentKey.value = 0;
      currentPage.value = 1;
    }
  },
);

const currentKey = ref(0);
const currentPage = ref(1);
const showCreateBtn = ref(false);
const rulesetManager = ref<any>(null);
// 将Drawer搜索条件保存起来
const _fromQuery: any = {};

const fieldMappingTimeArray = ref();

// 搜索表单抽屉
const [searchDrawerFrom, searchdrawerFromApi] = useVbenDrawer({
  connectedComponent: searchDrawerForm,
  class: 'w-[50%]',
  placement: 'right',
});

// 通用编辑表单抽屉
const [generalEditingDrawer, generalEditingDrawerApi] = useVbenDrawer({
  connectedComponent: generaleditingdrawer,
  class: 'w-[90%]',
  placement: 'right',
});

// 通用编辑表单抽屉
const [generalDetailDrawer, generalDetailDrawerApi] = useVbenDrawer({
  connectedComponent: generaldetaildrawer,
  class: 'w-[90%]',
  placement: 'right',
});

const [Grid, gridapi] = useVbenVxeGrid({
  formOptions: {
    showCollapseButton: false,
  },
  gridOptions: {
    // columns: transformColumns(columns),
    showOverflow: true, // 显示溢出内容
    rowConfig: {
      useKey: true, // 使用key作为行标识
      keyField: '_uniqueKey', // 使用处理后的唯一key字段
    },
    id: 'custom',
    height: 'auto',
    checkboxConfig: {
      labelField: 'keys',
      showReserveStatus: false,
      checkField: 'id',
    },

    proxyConfig: {
      autoLoad: false, // 禁用自动加载，手动控制
      ajax: {
        query: async (
          {
            page,
          }: {
            page: { currentPage: number; pageSize: number };
          },
          formValues: any,
        ) => {
          // 等待 API 准备就绪
          if (!isApiReady.value) {
            let retryCount = 0;
            while (!isApiReady.value && retryCount < 30) {
              await new Promise((resolve) => setTimeout(resolve, 100));
              retryCount++;
            }
            if (!isApiReady.value) {
              throw new Error('API 加载超时');
            }
          }

          const query = {
            ..._fromQuery,
            page: page.currentPage,
            pageSize: page.pageSize,
            tabIndex: currentKey.value,
            ...formValues,
          };

          // 更新页面搜索条件
          Object.assign(_fromQuery, query);

          const res = await list(query);

          if (!res) {
            throw new Error('API 返回数据为空');
          }

          const { nav, items, total } = res;

          // 如果是首次加载或配置发生变化，更新表格配置
          if (!rulesetManager.value || rulesetManager.value !== nav) {
            rulesetManager.value = nav;
            await updateGridConfiguration(nav, items || []);
          }

          // 为数据添加唯一 key，解决重复 key 问题
          const usedKeys = new Set<string>();
          let keyCounter = 0;
          const processedItems = (items || []).map(
            (item: any, index: number) => {
              let uniqueKey: string;

              // 如果 id 是有效值且不重复，使用 id
              if (
                item.id !== undefined &&
                item.id !== null &&
                item.id !== '' &&
                item.id !== false &&
                !usedKeys.has(String(item.id))
              ) {
                uniqueKey = String(item.id);
              } else {
                // 生成唯一 key，确保不重复
                do {
                  keyCounter++;
                  uniqueKey = `row_${Date.now()}_${keyCounter}_${index}_${Math.random().toString(36).slice(2, 9)}`;
                } while (usedKeys.has(uniqueKey));
              }

              usedKeys.add(uniqueKey);
              return { ...item, _uniqueKey: uniqueKey };
            },
          );

          return { items: processedItems, total: total || 0 };
        },
      },
    },
    toolbarConfig: {
      custom: true,
    },
    scrollY: {
      enabled: true,
      gt: 0,
    },
  },
});

/**
 * 更新表格配置（列、搜索表单等）
 */
async function updateGridConfiguration(nav: any, items: any[]) {
  try {
    // 转换列配置
    const transformedColumns = transformColumns(
      nav?.columns || [],
      items || [],
      {
        actionColumnConfig: {
          width: 300,
          title: '操作',
          fixed: 'right', // 固定在右侧
        },
      },
    );

    // 为 actions 类型的列添加点击处理函数
    transformedColumns.forEach((column: any) => {
      if (column.cellRender?.name === 'CellOperation') {
        column.cellRender.attrs = {
          ...column.cellRender.attrs,
          onClick: (params: { code: string; row: any }) => {
            // 构造按钮对象
            const button = {
              type: params.code,
              ...params,
            };

            // 调用页面级的按钮点击处理函数
            handleButtonClick(undefined, params.code, params.row, button);
          },
        };
      }
    });

    // 设置表格列配置
    gridapi.setGridOptions({
      columns: transformedColumns,
      customConfig: {
        mode: 'modal',
        storage: true,
        checkMethod({ column }) {
          return !['action', 'actions'].includes(column.field);
        },
        updateStore({ storeData }) {
          // 模拟异步，实现服务端保存
          // 获取当前 API URL 和用户 ID
          const apiUrls = getApiUrls();
          let apiUrl = apiUrls.listUrl || '';

          // 如果没有获取到 API URL，尝试从当前路由获取
          if (!apiUrl && currentApi.value?.Api?.getPageList) {
            apiUrl = currentApi.value.Api.getPageList.replace(/^\//, '');
          }

          // 如果还是没有，使用当前路由路径作为备用
          if (!apiUrl) {
            const currentRoute = window.location.pathname;
            apiUrl = currentRoute.replace(/^\//, '').replaceAll('/', '_');
          }

          const userId = userStore.userInfo?.userId || '';

          return saveCustomSetting(
            storeData,
            nav?.columns || [],
            apiUrl,
            userId,
          );
        },
      },
    });

    // 设置搜索表单 schema
    const schema = transformBackendSearchToSchema(nav?.topSearchForm ?? [], {
      enableGrouping: false,
    });

    // 查找 RangePicker 组件
    const rangePickerFields = schema.filter(
      (item) => item.component === 'RangePicker',
    );

    // 如果找到多个 RangePicker 字段，生成 fieldMappingTime 格式
    if (rangePickerFields.length > 0) {
      fieldMappingTimeArray.value = rangePickerFields.map((field) => {
        // 从配置中获取字段信息，或使用默认值
        const config = (field.componentProps as any) || {};
        const startField = config.startField || `${field.fieldName}_strat`;
        const endField = config.endField || `${field.fieldName}_end`;
        const formatstrat = config.formatstrat || 'YYYY-MM-DD 00:00:00';
        const formatend = config.formatend || 'YYYY-MM-DD 23:59:59';

        return [
          field.fieldName, // RangePicker 的字段名
          [startField, endField], // 起始和结束字段名
          [formatstrat, formatend], // 日期格式
        ];
      });
    }

    gridapi.formApi.setState({
      schema,
      showDefaultActions: true,
      fieldMappingTime: fieldMappingTimeArray.value,
    });

    // 判断是否需要显示多选列
    if (nav?.rowSelection) {
      gridapi.setGridOptions({
        checkboxConfig: {},
      });
    }

    // 设置新增按钮显示状态
    showCreateBtn.value = nav?.showCreateBtn || false;
  } catch {
    message.error('表格配置更新失败');
  }
}

/**
 * 手动刷新数据（保留原有的 initTab 功能，用于搜索等场景）
 */
async function initTab(params?: any) {
  try {
    // 更新搜索条件
    if (params) {
      Object.assign(_fromQuery, params);
    }

    // 重置到第一页
    currentPage.value = 1;

    // 触发表格重新查询
    gridapi.query();
  } catch {
    message.error('数据刷新失败，请重试');
  }
}

// 创建 Actions 组件的 props - 根据每一行的 actionBtnList 来显示按钮
function getActionsProps(row: any) {
  // 直接使用行级的 actionBtnList
  const rowActionButtons = row.actionBtnList || [];

  if (rowActionButtons.length === 0) {
    // No action buttons found for row
    return { primaryButtons: [], secondaryButtons: [] };
  }

  // 使用智能按钮分组：超过3个按钮时，后续按钮会自动放入下拉菜单
  return createActionsProps(rowActionButtons, row, {
    maxPrimaryButtons: 3, // 最多显示3个主要按钮
    maxWidth: '300px', // 操作列最大宽度
  });
}

// 设置全局页面事件处理函数
setGlobalPageEventHandler(handleButtonClick);

// 页面级别的按钮点击处理函数 - 支持多种调用方式
function handleButtonClick(...args: any[]) {
  let buttonKey: string | undefined;
  let buttonCode: string;
  let row: any;
  let button: any;

  if (args.length === 4) {
    // 来自ToolsButton组件或Actions组件: (buttonKey, buttonCode, row, button)
    [buttonKey, buttonCode, row, button] = args;
    // handleButtonClick: 来自ToolsButton或Actions组件
  } else if (args.length === 3 && typeof args[1] === 'string') {
    // 来自其他组件: (buttonKey, buttonCode, button)
    [buttonKey, buttonCode, button] = args;
    row = null;
    // handleButtonClick: 来自其他组件
  } else {
    return;
  }

  // 根据按钮代码执行不同的页面级逻辑
  switch (buttonCode) {
    case 'add': {
      // 处理新增模式的参数映射 - 基于 button.params
      const createParams = processParams(button.params || {}, {});

      generalEditingDrawerApi
        .setData({
          apiFn: currentApi.value,
          schema: buttonKey
            ? (rulesetManager.value?.form[buttonKey] ?? [])
            : [],
          row: {}, // 新增模式传入空对象
          params: createParams, // 基于 button.params 映射的参数
          mode: 'create', // 明确指定为新增模式
        })
        .setState({
          title: '新增',
        })
        .open();
      break;
    }
    case 'addSon': {
      // 处理新增子项的参数映射 - 基于 button.params 和当前行数据
      const createSonParams = processParams(button.params || {}, row);

      generalEditingDrawerApi
        .setData({
          apiFn: currentApi.value,
          schema: buttonKey
            ? (rulesetManager.value?.form[buttonKey] ?? [])
            : [],
          row: {}, // 新增模式传入空对象
          params: createSonParams, // 基于 button.params 映射的参数
          mode: 'create', // 明确指定为新增模式
        })
        .setState({
          title: '新增子项',
        })
        .open();
      break;
    }
    case 'advanced_search': {
      searchdrawerFromApi
        .setData({
          schema: buttonKey
            ? (rulesetManager.value?.form[buttonKey] ?? [])
            : [],
        })
        .setState({
          title: '高级搜索',
        })
        .open();
      break;
    }
    case 'delete': {
      // 页面处理删除操作
      // 可以显示确认对话框
      break;
    }
    case 'detail': {
      // 页面处理详情查看
      // 可以打开详情弹窗或跳转详情页
      generalDetailDrawerApi
        .setData({
          row,
          apiFn: currentApi.value,
          params: button.params,
        })
        .setState({
          title: '详情',
          footer: false,
        })
        .open();
      break;
    }
    case 'edit': {
      // 处理编辑模式的参数映射 - 基于 button.params 和当前行数据
      const editParams = processParams(button.params || {}, row);

      generalEditingDrawerApi
        .setData({
          apiFn: currentApi.value,
          schema: buttonKey
            ? (rulesetManager.value?.form[buttonKey] ?? [])
            : [],
          row, // 编辑模式传入行数据
          params: editParams, // 基于 button.params 映射的参数
          mode: 'edit', // 明确指定为编辑模式
        })
        .setState({
          title: '编辑',
        })
        .open();
      break;
    }
    default:
    // 未处理的按钮类型
  }
}

/**
 * 处理搜索条件提交
 * @param values 搜索表单值
 * @param type 操作类型：search 搜索，reset 重置
 */
function handleSubmitSearch(
  values: Record<string, any>,
  type?: 'reset' | 'search',
) {
  try {
    if (type === 'reset') {
      // 重置操作：清空所有搜索条件
      clearSearchConditions();
      message.success('搜索条件已重置');
    } else {
      // 搜索操作：筛选有效值
      const filteredValues = filterSearchData(values);

      // 先清除之前的搜索条件（保留分页参数）
      clearSearchConditions();

      // 设置新的搜索条件
      Object.assign(_fromQuery, filteredValues);

      if (Object.keys(filteredValues).length > 0) {
        message.success(
          `已应用 ${Object.keys(filteredValues).length} 个搜索条件`,
        );
      }
    }

    // 重置到第一页并重新加载数据
    currentPage.value = 1;
    initTab();
  } catch {
    message.error('搜索操作失败，请重试');
  }
}

/**
 * 清空搜索条件（保留分页参数）
 */
function clearSearchConditions() {
  Object.keys(_fromQuery).forEach((key) => {
    if (!['pageIndex', 'pageSize', 'tabIndex'].includes(key)) {
      delete _fromQuery[key];
    }
  });
}
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Actions
          v-bind="getActionsProps(row)"
          :show-tooltip="BUTTON_CONFIG.SHOW_TOOLTIP"
        />
      </template>
      <template #toolbar-tools>
        <ToolsButton
          :buttons="rulesetManager?.topBtnList"
          @click="handleButtonClick"
        />
      </template>
    </Grid>
    <searchDrawerFrom @confirm="handleSubmitSearch" />
    <generalEditingDrawer @confirm="gridapi.query()" />
    <generalDetailDrawer />
  </Page>
</template>
