/**
 * 审批组件类型定义
 */

/** 附件信息 */
export interface ApprovalAttachment {
  /** 文件名 */
  name: string;
  /** 文件URL */
  url: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type?: string;
}

/** 审批人信息 */
export interface ApprovalUser {
  /** 用户ID */
  userId: string;
  /** 用户名 */
  userName: string;
  /** 头像 */
  avatar?: string;
  /** 部门 */
  department?: string;
  /** 职位 */
  position?: string;
}

/** 审批节点 */
export interface ApprovalNode {
  /** 节点ID */
  id?: string;
  /** 节点名称 */
  name: string;
  /** 节点描述 */
  description?: string;
  /** 节点状态 */
  status: 'approved' | 'draft' | 'pending' | 'rejected';
  /** 审批人列表 */
  approvers?: ApprovalUser[];
  /** 审批时间 */
  approveTime?: string;
  /** 审批意见 */
  comment?: string;
  /** 附件列表 */
  attachments?: ApprovalAttachment[];
  /** 节点类型 */
  type?: 'approve' | 'end' | 'start';
  /** 是否必须 */
  required?: boolean;
  /** 审批方式 */
  approveType?: 'all' | 'any' | 'sequence';
  /** 超时时间（小时） */
  timeout?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/** 审批记录 */
export interface ApprovalRecord {
  /** 记录ID */
  id?: string;
  /** 标题 */
  title?: string;
  /** 申请人 */
  applicant?: ApprovalUser;
  /** 申请时间 */
  applyTime?: string;
  /** 整体状态 */
  status: 'approved' | 'draft' | 'pending' | 'rejected';
  /** 审批节点列表 */
  nodes: ApprovalNode[];
  /** 关联的业务ID */
  businessId?: string;
  /** 关联的业务类型 */
  businessType?: string;
  /** 流程模板ID */
  templateId?: string;
  /** 流程模板名称 */
  templateName?: string;
  /** 紧急程度 */
  urgency?: 'high' | 'low' | 'medium' | 'urgent';
  /** 备注 */
  remark?: string;
}

/** 审批组件属性 */
export interface ApprovalProps {
  /** 审批数据 */
  data?: ApprovalRecord;
  /** 是否可以审批 */
  canApprove?: boolean;
  /** 当前用户ID */
  currentUserId?: string;
  /** 标题 */
  title?: string;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 是否显示详情链接 */
  showDetailLink?: boolean;
  /** 步骤条方向 */
  stepsDirection?: 'horizontal' | 'vertical';
}

/** 审批组件事件 */
export interface ApprovalEvents {
  /** 审批操作 */
  approve: (data: {
    attachments?: ApprovalAttachment[];
    comment: string;
    result: 'approve' | 'reject';
  }) => void;
  /** 查看节点详情 */
  view: (node: ApprovalNode) => void;
  /** 撤回申请 */
  withdraw: () => void;
  /** 转交审批 */
  transfer: (targetUser: ApprovalUser, comment: string) => void;
  /** 刷新数据 */
  refresh: () => void;
}

/** 审批操作结果 */
export interface ApprovalResult {
  /** 操作结果 */
  result: 'approve' | 'reject';
  /** 审批意见 */
  comment: string;
  /** 附件列表 */
  attachments?: ApprovalAttachment[];
  /** 操作时间 */
  operateTime?: string;
  /** 操作人 */
  operator?: ApprovalUser;
}

/** 审批统计信息 */
export interface ApprovalStats {
  /** 总数 */
  total: number;
  /** 待审批数量 */
  pending: number;
  /** 已通过数量 */
  approved: number;
  /** 已拒绝数量 */
  rejected: number;
  /** 草稿数量 */
  draft: number;
}

/** 审批筛选条件 */
export interface ApprovalFilter {
  /** 状态筛选 */
  status?: string[];
  /** 申请人筛选 */
  applicant?: string[];
  /** 时间范围筛选 */
  timeRange?: [string, string];
  /** 紧急程度筛选 */
  urgency?: string[];
  /** 关键词搜索 */
  keyword?: string;
}
