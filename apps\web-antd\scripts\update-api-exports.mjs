/**
 * 批量更新 API 文件，为所有包含 enum Api 的文件添加 export { Api }
 * 使用方法：node scripts/update-api-exports.mjs
 */

import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// API 文件目录
const API_DIR = path.join(__dirname, '../src/api/common');

/**
 * 递归获取所有 .ts 文件
 */
function getAllTsFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.ts')) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * 更新单个文件
 */
function updateFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // 检查是否包含 enum Api
    if (!content.includes('enum Api')) {
      return false;
    }

    // 检查是否已经导出了 Api
    if (content.includes('export { Api }')) {
      return false;
    }

    // 在文件末尾添加导出
    const lines = content.split('\n');
    const lastImportIndex = lines.findIndex((line) =>
      line.startsWith('enum Api'),
    );

    if (lastImportIndex !== -1) {
      // 找到 enum Api 的结束位置
      let enumEndIndex = lastImportIndex;
      let braceCount = 0;
      let foundStart = false;

      for (let i = lastImportIndex; i < lines.length; i++) {
        const line = lines[i];
        if (line.includes('{')) {
          foundStart = true;
          braceCount += (line.match(/\{/g) || []).length;
        }
        if (foundStart) {
          braceCount -= (line.match(/\}/g) || []).length;
          if (braceCount === 0) {
            enumEndIndex = i;
            break;
          }
        }
      }

      // 在 enum 结束后添加导出
      lines.splice(
        enumEndIndex + 1,
        0,
        '',
        '// 暴露 Api enum 供中间件使用',
        'export { Api };',
      );

      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`更新文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.warn('开始批量更新 API 文件...');

  const tsFiles = getAllTsFiles(API_DIR);
  let updatedCount = 0;

  for (const file of tsFiles) {
    const relativePath = path.relative(process.cwd(), file);
    const updated = updateFile(file);

    if (updated) {
      console.warn(`✅ 已更新: ${relativePath}`);
      updatedCount++;
    } else {
      console.warn(`⏭️  跳过: ${relativePath}`);
    }
  }

  console.warn(`\n完成！共更新了 ${updatedCount} 个文件。`);
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { getAllTsFiles, updateFile };
