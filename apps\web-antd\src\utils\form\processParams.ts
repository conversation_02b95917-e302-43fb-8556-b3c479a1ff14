/**
 * 参数处理工具
 * 用于处理 API 调用时的参数映射
 */

/**
 * 处理 params 参数映射
 * 将 params 中的字段映射为 row 中对应的值
 *
 * @example
 * ```typescript
 * const params = { "_id": "id", "_userId": "userId" };
 * const row = { id: "123", userId: "456", name: "test" };
 * const result = processParams(params, row);
 * // 结果: { "_id": "123", "_userId": "456" }
 * ```
 *
 * @param params 参数配置，键为目标参数名，值为源字段名
 * @param row 行数据，包含源字段的值
 * @returns 处理后的参数对象
 */
export function processParams(
  params: Record<string, any> | undefined,
  row: Record<string, any> | undefined,
): Record<string, any> {
  if (!params || !row) {
    return {};
  }

  const processedParams: Record<string, any> = {};

  Object.keys(params).forEach((key) => {
    const sourceField = params[key];

    // 如果 sourceField 是字符串，从 row 中获取对应的值
    processedParams[key] =
      typeof sourceField === 'string' && row[sourceField] !== undefined
        ? row[sourceField]
        : sourceField; // 如果不是字符串或者在 row 中找不到，直接使用原值
  });

  return processedParams;
}

/**
 * 批量处理多个 params 配置
 *
 * @example
 * ```typescript
 * const paramsConfigs = [
 *   { "_id": "id" },
 *   { "_status": "status" }
 * ];
 * const row = { id: "123", status: "active", name: "test" };
 * const result = batchProcessParams(paramsConfigs, row);
 * // 结果: { "_id": "123", "_status": "active" }
 * ```
 */
export function batchProcessParams(
  paramsConfigs: Record<string, any>[],
  row: Record<string, any> | undefined,
): Record<string, any> {
  if (!paramsConfigs || !row) {
    return {};
  }

  let result: Record<string, any> = {};

  paramsConfigs.forEach((params) => {
    const processed = processParams(params, row);
    result = { ...result, ...processed };
  });

  return result;
}

/**
 * 深度处理 params，支持嵌套对象
 *
 * @example
 * ```typescript
 * const params = {
 *   "_id": "id",
 *   "filter": {
 *     "userId": "userId",
 *     "status": "active" // 直接值
 *   }
 * };
 * const row = { id: "123", userId: "456" };
 * const result = processParamsDeep(params, row);
 * // 结果: { "_id": "123", "filter": { "userId": "456", "status": "active" } }
 * ```
 */
export function processParamsDeep(
  params: any,
  row: Record<string, any> | undefined,
): any {
  if (!params || !row) {
    return params;
  }

  if (typeof params === 'string') {
    // 如果是字符串，尝试从 row 中获取值
    return row[params] === undefined ? params : row[params];
  }

  if (Array.isArray(params)) {
    // 如果是数组，递归处理每个元素
    return params.map((item) => processParamsDeep(item, row));
  }

  if (typeof params === 'object' && params !== null) {
    // 如果是对象，递归处理每个属性
    const result: Record<string, any> = {};
    Object.keys(params).forEach((key) => {
      result[key] = processParamsDeep(params[key], row);
    });
    return result;
  }

  // 其他类型直接返回
  return params;
}

/**
 * 验证 params 配置是否有效
 * 检查所有映射的字段是否在 row 中存在
 */
export function validateParamsConfig(
  params: Record<string, any>,
  row: Record<string, any>,
): {
  isValid: boolean;
  missingFields: string[];
  validFields: string[];
} {
  const missingFields: string[] = [];
  const validFields: string[] = [];

  Object.keys(params).forEach((key) => {
    const sourceField = params[key];

    if (typeof sourceField === 'string') {
      if (row[sourceField] === undefined) {
        missingFields.push(sourceField);
      } else {
        validFields.push(sourceField);
      }
    }
  });

  return {
    isValid: missingFields.length === 0,
    missingFields,
    validFields,
  };
}

/**
 * 创建参数处理器
 * 返回一个函数，可以重复使用相同的 params 配置
 */
export function createParamsProcessor(params: Record<string, any>) {
  return (row: Record<string, any> | undefined) => processParams(params, row);
}

/**
 * 合并表单数据和处理后的 params
 * 这是一个便捷函数，常用于 API 提交时
 */
export function mergeFormDataWithParams(
  formData: Record<string, any>,
  params: Record<string, any> | undefined,
  row: Record<string, any> | undefined,
): Record<string, any> {
  const processedParams = processParams(params, row);

  return {
    ...formData,
    ...processedParams,
  };
}

/**
 * 参数映射的常用配置
 */
export const COMMON_PARAMS_CONFIGS = {
  /** 标准 ID 映射 */
  ID_MAPPING: { _id: 'id' },

  /** 用户 ID 映射 */
  USER_ID_MAPPING: { _userId: 'userId' },

  /** 父级 ID 映射 */
  PARENT_ID_MAPPING: { _parentId: 'parentId' },

  /** 组织 ID 映射 */
  ORG_ID_MAPPING: { _orgId: 'orgId' },

  /** 部门 ID 映射 */
  DEPT_ID_MAPPING: { _deptId: 'deptId' },
} as const;

/**
 * 使用示例
 *
 * @example
 * ```typescript
 * // 基本用法
 * const params = { "_id": "id" };
 * const row = { id: "123", name: "test" };
 * const result = processParams(params, row);
 * // 结果: { "_id": "123" }
 *
 * // 在组件中使用
 * const processedParams = processParams(params, row);
 * const submitData = {
 *   ...formData,
 *   ...processedParams,
 * };
 * await api.create(submitData);
 *
 * // 使用常用配置
 * const idParams = processParams(COMMON_PARAMS_CONFIGS.ID_MAPPING, row);
 *
 * // 批量处理
 * const multiParams = batchProcessParams([
 *   { "_id": "id" },
 *   { "_userId": "userId" }
 * ], row);
 * ```
 */
