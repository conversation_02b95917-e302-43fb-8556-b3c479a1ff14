<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  /** 分组标题文本 */
  title?: string;
  /** 标题文本（兼容性） */
  value?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  value: '',
});

// 优先使用 title，如果没有则使用 value
const displayTitle = computed(() => props.title || props.value || '');
</script>

<template>
  <div class="group-title">
    <span class="group-title__text">{{ displayTitle }}</span>
  </div>
</template>

<style scoped>
.group-title {
  width: 100%;
  padding: 8px 12px;
  margin: 16px 0 8px;
  background: linear-gradient(
    90deg,
    hsl(var(--primary) / 10%) 0%,
    hsl(var(--primary) / 5%) 50%,
    transparent 100%
  );
  border-radius: 4px;
}

.group-title__text {
  font-size: 14px;
  font-weight: bold;
  line-height: 1.4;
  color: hsl(var(--primary));
}
</style>
