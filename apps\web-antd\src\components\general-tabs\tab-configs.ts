/**
 * Tab 组件配置定义
 * 每种 Tab 类型对应不同的组件和规则
 */

import type { BackendTabItem, TabComponentConfig, TabConfigMap } from './types';

import {
  BarChartOutlined,
  FileTextOutlined,
  FormOutlined,
  TableOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons-vue';

/**
 * 默认 Tab 组件配置
 */
export const DEFAULT_TAB_CONFIGS: TabConfigMap = {
  // 详情信息 Tab
  main: {
    component: 'DetailDescriptions',
    icon: FileTextOutlined,
    defaultProps: {
      column: 3,
      bordered: true,
      size: 'default',
    },
    propsTransform: (
      tabFormData: any,
      tabData: BackendTabItem,
      commonData?: any,
    ) => ({
      data: tabFormData.data || commonData || {},
      rules: transformFormToDescriptionRules(tabFormData.form || []),
      title: tabData.tab || '详情信息',
      topBtnList: tabFormData.topBtnList || [],
    }),
    rulesGenerator: (tabFormData: any) =>
      transformFormToDescriptionRules(tabFormData.form || []),
  },

  // 数据列表 Tab
  list: {
    component: 'DataTable',
    icon: TableOutlined,
    defaultProps: {
      bordered: true,
      size: 'small',
      pagination: { pageSize: 10 },
    },
    propsTransform: (data: any, tabData: BackendTabItem) => ({
      dataSource: data.listData || [],
      columns: generateTableColumns(data, tabData),
      loading: data.loading || false,
    }),
    rulesGenerator: generateTableColumns,
  },

  // 图表分析 Tab
  chart: {
    component: 'ChartView',
    icon: BarChartOutlined,
    defaultProps: {
      height: 400,
    },
    propsTransform: (data: any, tabData: BackendTabItem) => ({
      data: data.chartData || data.statisticsData || {},
      type: tabData.chartType || 'line',
      title: tabData.tab || '数据图表',
    }),
  },

  // 表单编辑 Tab
  form: {
    component: 'FormView',
    icon: FormOutlined,
    defaultProps: {
      layout: 'vertical',
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
    },
    propsTransform: (data: any, tabData: BackendTabItem) => ({
      formData: data.formData || data.detailData || {},
      schema: generateFormSchema(data, tabData),
      readonly: tabData.readonly || false,
    }),
    rulesGenerator: generateFormSchema,
  },

  // 操作记录 Tab
  logs: {
    component: 'LogsList',
    icon: UnorderedListOutlined,
    defaultProps: {
      size: 'small',
    },
    propsTransform: (data: any, _tabData: BackendTabItem) => ({
      logs: data.logs || [],
      loading: data.logsLoading || false,
    }),
  },

  // 关联数据 Tab
  relations: {
    component: 'RelationsList',
    icon: TableOutlined,
    defaultProps: {
      bordered: true,
    },
    propsTransform: (data: any, tabData: BackendTabItem) => ({
      relations: data.relations || [],
      type: tabData.relationType || 'default',
    }),
  },
};

/**
 * 将后端 form 规则转换为 DetailDescriptions 规则
 */
function transformFormToDescriptionRules(formRules: any[]) {
  if (!Array.isArray(formRules)) {
    return [];
  }

  const rules: any[] = [];

  // 处理嵌套的表单结构
  formRules.forEach((section) => {
    if (section.dataItem && Array.isArray(section.dataItem)) {
      // 处理嵌套的 dataItem 结构
      section.dataItem.forEach((item: any) => {
        // 跳过 ifShow 为 false 的字段（DetailDescriptions 应该显示所有字段）
        // if (item.ifShow === false) return;

        const rule = {
          field: item.field || item.dataIndex || item.key,
          label: item.label || item.title || item.name,
          type: getDescriptionType(item.type),
          span: item.span,
          options: transformOptions(item.options, item.config?.options),
          transform: item.transform,
          condition: item.condition,
        };

        if (rule.field && rule.label) {
          rules.push(rule);
        }
      });
    } else {
      // 处理直接的规则结构
      const rule = {
        field: section.field || section.dataIndex || section.key,
        label: section.label || section.title || section.name,
        type: getDescriptionType(section.type),
        span: section.span,
        options: transformOptions(section.options, section.config?.options),
        transform: section.transform,
        condition: section.condition,
      };

      if (rule.field && rule.label) {
        rules.push(rule);
      }
    }
  });

  return rules;
}

/**
 * 转换选项格式
 */
function transformOptions(options: any, configOptions: any) {
  // 优先使用 options，如果没有则使用 config.options
  const sourceOptions = options || configOptions;

  if (!sourceOptions) {
    return undefined;
  }

  // 如果是对象格式，转换为 mapping 格式
  if (typeof sourceOptions === 'object' && !Array.isArray(sourceOptions)) {
    const mapping: Record<string, any> = {};
    for (const [key, value] of Object.entries(sourceOptions)) {
      mapping[key] = {
        label: String(value),
        value: key,
      };
    }
    return {
      mapping,
    };
  }

  // 如果是数组格式，保持原样
  if (Array.isArray(sourceOptions)) {
    return {
      list: sourceOptions,
    };
  }

  return sourceOptions;
}

/**
 * 根据表单字段类型转换为描述组件类型
 */
function getDescriptionType(formType: string): string {
  const typeMapping: Record<string, string> = {
    input: 'text',
    textarea: 'text',
    select: 'tag',
    multiselect: 'tag', // 多选使用 tag 显示
    radio: 'tag', // 单选框使用 tag 显示
    checkbox: 'tag', // 复选框使用 tag 显示
    dept: 'tag', // 部门类型使用 tag 显示
    person: 'tag', // 人员类型也使用 tag 显示
    date: 'date',
    datetime: 'date',
    image: 'image',
    file: 'file',
    rate: 'rate',
    link: 'link',
    badge: 'badge',
    text: 'text',
  };

  return typeMapping[formType] || 'text';
}

/**
 * 生成表格列配置
 */
function generateTableColumns(_data: any, tabData: BackendTabItem) {
  // 根据不同的列表类型生成不同的列配置
  const baseColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
  ];

  // 可以根据 tabData 的配置添加操作列
  if (tabData.createBtn) {
    baseColumns.push({
      title: '操作',
      key: 'actions',
      width: 120,
      // 这里可以添加操作按钮的配置
    });
  }

  return baseColumns;
}

/**
 * 生成表单 Schema
 */
function generateFormSchema(_data: any, _tabData: BackendTabItem) {
  // 根据不同的表单类型生成不同的 Schema
  const baseSchema = [
    { field: 'name', label: '名称', component: 'Input', required: true },
    { field: 'status', label: '状态', component: 'Select', required: true },
    { field: 'description', label: '描述', component: 'TextArea' },
  ];

  return baseSchema;
}

/**
 * 获取 Tab 配置
 */
export function getTabConfig(key: string): TabComponentConfig {
  return DEFAULT_TAB_CONFIGS[key] || DEFAULT_TAB_CONFIGS.main;
}

/**
 * 注册自定义 Tab 配置
 */
export function registerTabConfig(key: string, config: TabComponentConfig) {
  DEFAULT_TAB_CONFIGS[key] = config;
}

/**
 * 批量注册 Tab 配置
 */
export function registerTabConfigs(configs: TabConfigMap) {
  Object.assign(DEFAULT_TAB_CONFIGS, configs);
}

/**
 * 转换后端 Tab 数据为 TabItem 格式
 * 支持新的数据结构：{ tabs: [...], formdatas: { key: { form, data, topBtnList } } }
 */
export function transformBackendTabsToItems(
  backendTabs: BackendTabItem[],
  formdatas: Record<string, any>,
  commonData?: any,
  customConfigs?: TabConfigMap,
): any[] {
  // transformBackendTabsToItems 调用

  // 合并自定义配置
  const configs = { ...DEFAULT_TAB_CONFIGS, ...customConfigs };

  const result = backendTabs.map((tabData) => {
    const config = configs[tabData.key] || configs.main;
    const tabFormData = formdatas[tabData.key] || {};

    // 生成 props
    const props = {
      ...config.defaultProps,
      ...(config.propsTransform
        ? config.propsTransform(tabFormData, tabData, commonData)
        : {
            data: tabFormData.data || {},
            form: tabFormData.form || [],
            topBtnList: tabFormData.topBtnList || [],
            tabData,
          }),
    };

    const item = {
      key: tabData.key,
      label: tabData.tab,
      component: config.component,
      icon: config.icon,
      props,
      disabled: tabData.disabled || false,
      closable: tabData.closable || false,
    };

    // 转换单个 tab
    return item;
  });

  // transformBackendTabsToItems 结果
  return result;
}

/**
 * 获取所有可用的 Tab 配置键
 */
export function getAvailableTabKeys(): string[] {
  return Object.keys(DEFAULT_TAB_CONFIGS);
}
