/**
 * 表格自定义设置相关方法
 */
import { saveTableSettings } from '#/api/common/table-settings';

/**
 * 转换表格列配置数据结构
 * @param storeData 包含用户自定义设置的对象 {resizableData, sortData, visibleData, fixedData}
 * @param originalColumns 原始列配置数组 (nav?.columns)
 * @returns 转换后的列配置数组，应用了用户自定义设置
 */
export function transformTableSettings(storeData: any, originalColumns: any[]) {
  // 如果没有原始列数据，返回空数组
  if (!originalColumns || !Array.isArray(originalColumns)) {
    return [];
  }

  // 如果没有自定义设置数据，返回原始列
  if (!storeData) {
    return originalColumns;
  }

  const {
    resizableData = {},
    sortData = {},
    visibleData = {},
    fixedData = {},
  } = storeData;

  // 创建列配置映射
  const columnMap = new Map();
  originalColumns.forEach((column) => {
    const fieldName = column.field || column.key;
    if (fieldName) {
      columnMap.set(fieldName, { ...column });
    }
  });

  // 应用用户自定义设置
  columnMap.forEach((column, fieldName) => {
    // 应用宽度设置
    if (resizableData[fieldName] !== undefined) {
      column.width = resizableData[fieldName];
    }

    // 应用可见性设置
    if (visibleData[fieldName] !== undefined) {
      column.visible = visibleData[fieldName];
    }

    // 应用冻结设置
    if (fixedData[fieldName] !== undefined) {
      column.fixed = fixedData[fieldName];
    }
  });

  // 根据排序数据重新排序列
  const sortedColumns: any[] = [];
  const sortEntries = Object.entries(sortData).sort(
    ([, a], [, b]) => (a as number) - (b as number),
  );

  // 按排序顺序添加列
  sortEntries.forEach(([fieldName]) => {
    if (columnMap.has(fieldName)) {
      sortedColumns.push(columnMap.get(fieldName));
      columnMap.delete(fieldName);
    }
  });

  // 添加未在排序数据中的列（保持原有顺序）
  columnMap.forEach((column) => {
    sortedColumns.push(column);
  });

  return sortedColumns;
}

/**
 * 保存表格自定义设置
 * @param storeData 包含用户自定义设置的对象 {resizableData, sortData, visibleData, fixedData}
 * @param originalColumns 原始列配置数组 (nav?.columns)
 * @param apiUrl 当前表格请求的API URL
 * @param userId 用户ID
 * @returns Promise<any[]> 返回转换后的列配置数据
 */
export async function saveCustomSetting(
  storeData: any,
  originalColumns: any[],
  apiUrl?: string,
  userId?: number | string,
): Promise<any[]> {
  // 转换数据结构，应用用户自定义设置到原始列配置
  const transformedData = transformTableSettings(storeData, originalColumns);
  try {
    // 如果提供了API URL和用户ID，则保存到服务器
    if (apiUrl && userId) {
      await saveTableSettings({
        account_id: userId,
        data: transformedData, // 保存原始的用户设置数据
        type: apiUrl,
      });
    } else {
      console.warn('跳过保存表格设置:', {
        apiUrlEmpty: !apiUrl,
        userIdEmpty: !userId,
        apiUrl,
        userId,
      });
    }

    // 返回转换后的数据，格式与transformColumns转换前的数据结构相同
    return transformedData;
  } catch (error) {
    console.error('保存表格设置失败:', error);
    // 即使保存失败，也返回转换后的数据，确保前端功能正常
    return transformedData;
  }
}
