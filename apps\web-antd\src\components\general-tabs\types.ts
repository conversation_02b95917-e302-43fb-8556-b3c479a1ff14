/**
 * 通用 Tabs 组件类型定义
 */

import type { Component } from 'vue';

/** 后端 Tab 数据格式 */
export interface BackendTabItem {
  /** Tab 标识 */
  key: string;
  /** Tab 标题 */
  tab: string;
  /** 创建按钮标识 */
  createBtn?: number;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可关闭 */
  closable?: boolean;
  /** 扩展数据 */
  [key: string]: any;
}

/** Tab 项配置 */
export interface TabItem {
  /** Tab 的唯一标识 */
  key: string;
  /** Tab 标题 */
  label: string;
  /** 要渲染的组件 */
  component: Component | string;
  /** 传递给组件的 props */
  props?: Record<string, any>;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可关闭 */
  closable?: boolean;
  /** 是否强制渲染（默认懒加载） */
  forceRender?: boolean;
  /** 自定义图标 */
  icon?: Component | string;
  /** 条件显示 */
  condition?: ((data?: any) => boolean) | boolean;
}

/** Tab 组件配置 */
export interface TabsConfig {
  /** Tab 项列表 */
  items: TabItem[];
  /** 默认激活的 Tab */
  defaultActiveKey?: string;
  /** 当前激活的 Tab */
  activeKey?: string;
  /** Tab 位置 */
  tabPosition?: 'bottom' | 'left' | 'right' | 'top';
  /** Tab 类型 */
  type?: 'card' | 'editable-card' | 'line';
  /** Tab 大小 */
  size?: 'large' | 'middle' | 'small';
  /** 是否居中显示 */
  centered?: boolean;
  /** 是否隐藏新增按钮 */
  hideAdd?: boolean;
  /** 是否显示边框 */
  bordered?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 传递给所有 Tab 组件的公共数据 */
  commonData?: Record<string, any>;
}

/** Tab 事件 */
export interface TabsEvents {
  /** Tab 切换事件 */
  onChange?: (activeKey: string) => void;
  /** Tab 编辑事件（新增/删除） */
  onEdit?: (targetKey: string, action: 'add' | 'remove') => void;
  /** Tab 点击事件 */
  onTabClick?: (key: string, event: MouseEvent) => void;
  /** Tab 滚动事件 */
  onTabScroll?: (info: {
    direction: 'bottom' | 'left' | 'right' | 'top';
  }) => void;
}

/** 组件注册映射 */
export interface ComponentMap {
  [key: string]: Component;
}

/** 预设组件类型 */
export type PresetComponentType =
  | 'chart'
  | 'custom'
  | 'descriptions'
  | 'form'
  | 'list'
  | 'table'
  | 'tree';

/** Tab 组件配置 */
export interface TabComponentConfig {
  /** 组件名称或组件实例 */
  component: Component | string;
  /** 默认 props */
  defaultProps?: Record<string, any>;
  /** props 转换函数 */
  propsTransform?: (data: any, tabData: BackendTabItem) => Record<string, any>;
  /** 显示规则生成函数 */
  rulesGenerator?: (data: any, tabData: BackendTabItem) => any[];
  /** 图标 */
  icon?: Component | string;
}

/** Tab 配置映射 */
export interface TabConfigMap {
  [key: string]: TabComponentConfig;
}

/** 预设 Tab 配置 */
export interface PresetTabConfig {
  /** 预设类型 */
  type: PresetComponentType;
  /** Tab 标题 */
  label: string;
  /** 组件配置 */
  config?: Record<string, any>;
  /** 是否禁用 */
  disabled?: boolean;
  /** 条件显示 */
  condition?: ((data?: any) => boolean) | boolean;
}
