import type { VxeGridInstance } from 'vxe-table';

import type { TableConfigApi } from '#/api/system/table-config';

import { message } from 'ant-design-vue';

/**
 * 表格配置管理器
 * 用于管理表格列的显示/隐藏、排序等配置的后端存储
 */
export class TableConfigManager {
  private defaultColumns: any[] = [];
  private gridInstance: null | VxeGridInstance = null;
  private pageKey: string;
  private tableKey: string;

  constructor(pageKey: string, tableKey: string) {
    this.pageKey = pageKey;
    this.tableKey = tableKey;
  }

  /**
   * 应用配置到表格
   */
  applyConfig(columns: TableConfigApi.ColumnConfig[]) {
    if (!this.gridInstance || !columns) return;

    try {
      // 根据配置重新设置列
      const newColumns = columns
        .sort((a, b) => a.sortIndex - b.sortIndex)
        .filter((col) => col.visible)
        .map((col) => ({
          field: col.field,
          title: col.title,
          width: col.width,
          fixed: col.fixed,
        }));

      // 更新表格列配置
      this.gridInstance.loadColumn(newColumns);

      // 配置已应用到表格
    } catch (error) {
      console.error('应用配置失败:', error);
    }
  }

  /**
   * 获取配置摘要信息
   */
  getConfigSummary() {
    const config = this.getCurrentConfig();
    if (!config) return null;

    return {
      totalColumns: config.length,
      visibleColumns: config.filter((col) => col.visible).length,
      hiddenColumns: config.filter((col) => !col.visible).length,
      fixedColumns: config.filter((col) => col.fixed).length,
    };
  }

  /**
   * 获取当前表格的列配置
   */
  getCurrentConfig(): null | TableConfigApi.ColumnConfig[] {
    if (!this.gridInstance) {
      console.error('表格实例未设置');
      return null;
    }

    try {
      const columns = this.gridInstance.getColumns();

      return columns.map((column, index) => ({
        field: column.field,
        title: column.title,
        visible: column.visible !== false,
        width: column.width || column.renderWidth,
        sortIndex: index,
        fixed: column.fixed || undefined,
      }));
    } catch (error) {
      console.error('获取列配置失败:', error);
      return null;
    }
  }

  /**
   * 从后端加载配置
   */
  async loadConfig(): Promise<boolean> {
    try {
      // 实际项目中取消注释下面这行
      // const response = await getTableConfig({
      //   pageKey: this.pageKey,
      //   tableKey: this.tableKey
      // });

      // 模拟从本地存储加载
      const savedConfig = localStorage.getItem(
        `table-config-${this.pageKey}-${this.tableKey}`,
      );

      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        // 从后端加载配置成功

        this.applyConfig(config.columns);
        message.success('列配置加载成功');
        return true;
      } else {
        // 未找到保存的配置，使用默认配置
        return false;
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
      return false;
    }
  }

  /**
   * 重置为默认配置
   */
  async resetConfig(): Promise<boolean> {
    try {
      // 实际项目中取消注释下面这行
      // await resetTableConfig({
      //   pageKey: this.pageKey,
      //   tableKey: this.tableKey
      // });

      // 清除本地存储的配置
      localStorage.removeItem(`table-config-${this.pageKey}-${this.tableKey}`);

      // 重置表格列
      if (this.gridInstance && this.defaultColumns.length > 0) {
        this.gridInstance.loadColumn(this.defaultColumns);
      }

      message.success('已重置为默认配置');
      return true;
    } catch (error) {
      console.error('重置配置失败:', error);
      message.error('重置配置失败');
      return false;
    }
  }

  /**
   * 保存当前配置到后端
   */
  async saveConfig(): Promise<boolean> {
    const config = this.getCurrentConfig();
    if (!config) {
      message.error('获取列配置失败');
      return false;
    }

    try {
      const saveData: TableConfigApi.SaveConfigRequest = {
        pageKey: this.pageKey,
        tableKey: this.tableKey,
        columns: config,
      };

      // 准备保存配置到后端

      // 实际项目中取消注释下面这行
      // await saveTableConfig(saveData);

      // 模拟保存到本地存储
      localStorage.setItem(
        `table-config-${this.pageKey}-${this.tableKey}`,
        JSON.stringify(saveData),
      );

      message.success('列配置保存成功');
      return true;
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
      return false;
    }
  }

  /**
   * 设置默认列配置
   */
  setDefaultColumns(columns: any[]) {
    this.defaultColumns = columns;
  }

  /**
   * 设置表格实例
   */
  setGridInstance(grid: VxeGridInstance) {
    this.gridInstance = grid;
  }

  /**
   * 监听表格自定义列变化事件
   */
  setupCustomColumnListener() {
    if (!this.gridInstance) return;

    // 监听自定义列变化事件
    this.gridInstance.on('custom-column-change', () => {
      // 检测到列配置变化
      // 可以在这里自动保存配置，或者提示用户保存
    });
  }
}

/**
 * 创建表格配置管理器实例
 */
export function createTableConfigManager(pageKey: string, tableKey: string) {
  return new TableConfigManager(pageKey, tableKey);
}
