# ToolsButton 工具按钮组件

基于原生 div 实现的工具按钮组件，完全遵循 Vben 主题系统，支持动态配置按钮数组、自定义样式和事件处理。

## 功能特性

- ✅ **动态配置**: 根据传入数组动态生成按钮
- ✅ **主题适配**: 完全遵循 Vben 主题系统，支持所有内置主题
- ✅ **自动主题色**: 背景色自动跟随主题变化，无需手动配置
- ✅ **图标支持**: 内置常用图标，支持自动匹配
- ✅ **事件处理**: 点击事件携带完整按钮信息
- ✅ **条件显示**: 支持按钮的显示/隐藏控制
- ✅ **灵活布局**: 支持水平/垂直排列
- ✅ **多种形状**: 支持默认、圆形、圆角按钮

## 基础用法

```vue
<template>
  <ToolsButton :buttons="buttonList" @click="handleButtonClick" />
</template>

<script setup>
import { ToolsButton } from '@/components';

const buttonList = [
  {
    title: '创建',
    type: 'add',
    key: 'main',
    themeType: 'primary', // 使用主题主色
  },
  {
    title: '编辑',
    type: 'edit',
    key: 'edit',
    themeType: 'success', // 使用主题成功色
  },
  {
    title: '删除',
    type: 'delete',
    key: 'delete',
    themeType: 'danger', // 使用主题危险色
  },
];

function handleButtonClick(key, type, title, button) {
  console.log('按钮点击:', { key, type, title, button });

  // key 代表选择的表单/操作标识
  // type 代表对应的按钮类型
  // title 代表按钮显示的标题
  // button 代表完整的按钮配置对象

  // 可以根据 key 执行具体操作
  switch (key) {
    case 'main':
      // 打开主表单
      console.log('打开主表单，按钮类型:', type);
      break;
    case 'edit':
      // 打开编辑表单
      console.log('打开编辑表单，标题:', title);
      break;
    case 'delete':
      // 执行删除操作
      console.log('执行删除操作，完整配置:', button);
      break;
  }

  // 也可以根据 type 进行分类处理
  switch (type) {
    case 'add':
      // 所有添加类型的通用逻辑
      console.log('执行添加操作，key:', key);
      break;
    case 'edit':
      // 所有编辑类型的通用逻辑
      console.log('执行编辑操作，key:', key);
      break;
  }

  // 获取以往的数据输出（完整的按钮配置）
  if (button) {
    console.log('按钮的所有配置数据:', {
      key: button.key,
      type: button.type,
      title: button.title,
      icon: button.icon,
      // 其他自定义属性
      customData: button.customData,
      params: button.params,
    });
  }
}
</script>
```

## API

### Props

| 参数        | 说明         | 类型                             | 默认值     |
| ----------- | ------------ | -------------------------------- | ---------- |
| buttons     | 按钮配置数组 | `ToolsButtonItem[]`              | `[]`       |
| gap         | 按钮间距     | `number`                         | `8`        |
| defaultSize | 默认按钮大小 | `'large' \| 'middle' \| 'small'` | `'middle'` |
| vertical    | 是否垂直排列 | `boolean`                        | `false`    |

### ToolsButtonItem

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 按钮标题 | `string` | - |
| type | 按钮类型/代码 | `string` | - |
| key | 按钮唯一标识 | `string` | - |
| textColor | 文字颜色 | `string` | - |
| size | 按钮大小 | `'large' \| 'middle' \| 'small'` | - |
| disabled | 是否禁用 | `boolean` | `false` |
| visible | 是否显示 | `boolean` | `true` |
| icon | 自定义图标 | `string` | - |
| shape | 按钮形状 | `'default' \| 'circle' \| 'round'` | - |
| themeType | 主题色类型 | `'primary' \| 'secondary' \| 'success' \| 'warning' \| 'danger'` | - |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| click | 按钮点击事件 | `(key: string, type: string, title: string, button: ToolsButtonItem) => void` |

### 内置图标

组件内置了常用图标，会根据 `type` 或 `icon` 字段自动匹配：

- `add` - 加号图标
- `edit` - 编辑图标
- `delete` - 删除图标
- `view` - 查看图标
- `setting` - 设置图标

## 测试示例

项目中提供了两个完整的测试示例文件：

### 1. 基础功能测试

```bash
# ToolsButton 组件基础功能测试
apps/web-antd/src/components/tools-button/test-example.vue
```

### 2. 集成测试 (推荐)

```bash
# ToolsButton 与 Actions 组件集成测试
apps/web-antd/src/components/tools-button/test-actions-integration.vue
```

测试示例包含：

- ✅ ToolsButton 和 Actions 组件的同时使用
- ✅ 统一的事件处理逻辑演示
- ✅ key 和 type 参数分离展示
- ✅ 实时的点击日志显示
- ✅ 参数解析和数据输出演示
- ✅ 不同来源按钮的区分处理

## 更新说明

### v2.1 更新内容 (最新)

1. **Actions组件集成**: transformColumns 转换 actionBtnList 时现在像 ToolsButton 组件一样分别传递 key 和 type：
   - Actions组件调用: `(buttonKey, buttonCode, buttonTitle, row, button)` - 5个参数
   - ToolsButton组件调用: `(key, type, title, button)` - 4个参数
   - 统一的 handleButtonClick 方法自动识别调用来源

2. **参数分离**: key 和 type 现在完全分离：
   - `key`: 按钮的唯一标识符，用于区分具体操作
   - `type`/`code`: 按钮的类型，用于通用逻辑处理

3. **完整的数据获取**: 可以获取到所有以往的数据输出，包括自定义属性

### v2.0 更新内容

1. **增强的事件处理**: handleButtonClick 方法现在可以获取到：
   - `key`: 按钮的唯一标识
   - `type`: 按钮的类型/代码
   - `title`: 按钮显示的标题
   - `button`: 完整的按钮配置对象（包含所有以往的数据）

2. **统一的接口**: 同一个 handleButtonClick 方法可以同时处理 ToolsButton 和 Actions 组件的事件

3. **向下兼容**: 保持与现有代码的完全兼容性

## 高级用法

### 主题色适配

组件会自动跟随系统主题色，支持多种主题色类型：

```vue
<script setup>
const buttonList = [
  {
    title: '主要操作',
    type: 'primary',
    key: 'primary',
    themeType: 'primary', // 主题主色
  },
  {
    title: '次要操作',
    type: 'secondary',
    key: 'secondary',
    themeType: 'secondary', // 主题次要色
  },
  {
    title: '成功操作',
    type: 'success',
    key: 'success',
    themeType: 'success', // 主题成功色
  },
  {
    title: '警告操作',
    type: 'warning',
    key: 'warning',
    themeType: 'warning', // 主题警告色
  },
  {
    title: '危险操作',
    type: 'danger',
    key: 'danger',
    themeType: 'danger', // 主题危险色
  },
];
</script>
```

### 主题色自动适配

```vue
<script setup>
// 所有按钮都会自动跟随主题色变化
const buttonList = [
  {
    title: '主题主色',
    type: 'primary',
    key: 'primary',
    themeType: 'primary', // 自动跟随主题主色
  },
  {
    title: '主题成功色',
    type: 'success',
    key: 'success',
    themeType: 'success', // 自动跟随主题成功色
  },
];
</script>
```

### 条件显示

```vue
<script setup>
const buttonList = [
  {
    title: '管理员功能',
    type: 'admin',
    key: 'admin',
    visible: user.role === 'admin', // 只有管理员可见
  },
];
</script>
```

### 垂直布局

```vue
<template>
  <ToolsButton
    :buttons="buttonList"
    vertical
    :gap="12"
    @click="handleButtonClick"
  />
</template>
```

### 实际项目中的使用示例

在实际项目中，handleButtonClick方法已经被优化，可以同时处理ToolsButton和Actions组件的事件：

```vue
<template>
  <div>
    <!-- 工具栏按钮 -->
    <ToolsButton :buttons="toolButtons" @click="handleButtonClick" />

    <!-- 表格行操作按钮 -->
    <Actions v-bind="getActionsProps(row)" />
  </div>
</template>

<script setup>
import { ToolsButton } from '@/components';

const toolButtons = [
  {
    title: '新增用户',
    type: 'add',
    key: 'user_add',
    themeType: 'primary',
  },
  {
    title: '批量导入',
    type: 'import',
    key: 'user_import',
    themeType: 'success',
  },
];

// 统一的按钮点击处理函数 - 自动识别来源
function handleButtonClick(keyOrCode, typeOrTitle, titleOrRow, buttonOrButton) {
  // 函数会自动判断调用来源：
  // - 来自ToolsButton: (key, type, title, button)
  // - 来自Actions: (buttonCode, buttonTitle, row, button)

  console.log('按钮点击事件:', {
    keyOrCode, // ToolsButton的key 或 Actions的buttonCode
    typeOrTitle, // ToolsButton的type 或 Actions的buttonTitle
    titleOrRow, // ToolsButton的title 或 Actions的row
    buttonOrButton, // 完整的按钮配置对象
  });

  // 可以通过第三个参数类型判断来源
  const isFromToolsButton = typeof titleOrRow === 'string';

  if (isFromToolsButton) {
    // 来自ToolsButton组件
    const key = keyOrCode;
    const type = typeOrTitle;
    const title = titleOrRow;
    const button = buttonOrButton;

    console.log('工具栏按钮点击:', { key, type, title, button });

    // 根据key处理不同操作
    switch (key) {
      case 'user_add':
        openAddUserDialog();
        break;
      case 'user_import':
        openImportDialog();
        break;
    }
  } else {
    // 来自Actions组件
    const buttonCode = keyOrCode;
    const buttonTitle = typeOrTitle;
    const row = titleOrRow;
    const button = buttonOrButton;

    console.log('行操作按钮点击:', { buttonCode, buttonTitle, row, button });

    // 根据buttonCode处理不同操作
    switch (buttonCode) {
      case 'edit':
        editUser(row);
        break;
      case 'delete':
        deleteUser(row);
        break;
    }
  }
}

function openAddUserDialog() {
  console.log('打开新增用户对话框');
}

function openImportDialog() {
  console.log('打开批量导入对话框');
}

function editUser(row) {
  console.log('编辑用户:', row);
}

function deleteUser(row) {
  console.log('删除用户:', row);
}
</script>
```

### 自定义文字颜色

```vue
<script setup>
const buttonList = [
  {
    title: '自定义文字色',
    type: 'custom',
    key: 'custom',
    textColor: '#ff6b6b', // 自定义文字颜色
    themeType: 'secondary',
    shape: 'round',
    size: 'large',
  },
];
</script>
```

### 按钮形状

```vue
<script setup>
const buttonList = [
  {
    title: '默认形状',
    type: 'default',
    key: 'default',
    shape: 'default',
    themeType: 'primary',
  },
  {
    title: '圆角按钮',
    type: 'round',
    key: 'round',
    shape: 'round',
    themeType: 'success',
  },
  {
    title: '', // 圆形按钮不显示文字
    type: 'circle',
    key: 'circle',
    shape: 'circle',
    themeType: 'warning',
    icon: 'add',
  },
];
</script>
```

### 按钮大小

```vue
<script setup>
const buttonList = [
  {
    title: '小按钮',
    type: 'small',
    key: 'small',
    size: 'small',
    themeType: 'primary',
  },
  {
    title: '中等按钮',
    type: 'middle',
    key: 'middle',
    size: 'middle',
    themeType: 'primary',
  },
  {
    title: '大按钮',
    type: 'large',
    key: 'large',
    size: 'large',
    themeType: 'primary',
  },
];
</script>
```

## 注意事项

1. 组件基于原生 div 实现，完全控制样式，不受 Ant Design 样式限制
2. 完全遵循 Vben 主题系统，背景色自动跟随主题变化
3. 圆形按钮会自动隐藏文字，只显示图标
4. 建议为按钮设置合适的 `themeType` 以获得最佳视觉效果
5. 支持主题切换和深色模式，无需额外配置
