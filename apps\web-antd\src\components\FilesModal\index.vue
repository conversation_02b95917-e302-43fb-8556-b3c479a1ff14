<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Image, ImagePreviewGroup, message } from 'ant-design-vue';

import FIlesMoal from '../PreviewFile/xlsxFile.vue';

// 定义原始附件数据接口（支持url+name格式）
interface RawFileItem {
  url: string; // 文件URL
  name: string; // 文件名
  [key: string]: any; // 允许其他字段
}

// 定义标准化后的附件类型接口
interface FileItem {
  path: string; // 文件路径（标准化后的字段）
  name: string; // 文件名
  type: 'archive' | 'audio' | 'document' | 'image' | 'other' | 'video';
}

const rawData = ref<RawFileItem[]>([]); // 原始数据
const data = computed<FileItem[]>(() => {
  // 数据标准化：将url+name格式转换为path+name格式
  return rawData.value.map((item) => ({
    path: item.url,
    name: item.name,
    type: getFileType(item.name),
  }));
});

const [filesmodal, filesmodalApi] = useVbenModal({
  connectedComponent: FIlesMoal,
});

const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen) {
    if (isOpen) {
      const dataFromApi = modalApi.getData() as Record<string, any>;
      const fileList = dataFromApi.fileList;

      if (Array.isArray(fileList)) {
        // 支持多种数据格式
        rawData.value = fileList.map((item) => {
          if (typeof item === 'string') {
            // 字符串格式：直接作为URL
            return { url: item, name: item.split('/').pop() || '未知文件' };
          } else if (item.url && item.name) {
            // url+name格式：直接使用
            return item;
          } else if (item.path) {
            // path格式：转换为url+name格式
            return {
              url: item.path,
              name: item.name || item.path.split('/').pop() || '未知文件',
            };
          } else {
            // 其他格式：尝试兼容
            return {
              url: item.url || item.path || '',
              name: item.name || '未知文件',
            };
          }
        });
      } else {
        rawData.value = [];
        message.error('文件列表数据格式错误');
      }
    }
  },
});

// 根据文件名获取文件类型
function getFileType(fileName: string): FileItem['type'] {
  const ext = fileName.split('.').pop()?.toLowerCase() || '';

  const typeMap = {
    document: [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
      'rtf',
    ],
    image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
    video: ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'],
    audio: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
    archive: ['zip', 'rar', '7z', 'tar', 'gz'],
  };

  for (const [type, extensions] of Object.entries(typeMap)) {
    if (extensions.includes(ext)) {
      return type as FileItem['type'];
    }
  }

  return 'other';
}

// 获取文件类型图标
function getFileIcon(type: FileItem['type']): string {
  const iconMap = {
    document: '📄',
    image: '🖼️',
    video: '🎬',
    audio: '🎵',
    archive: '📦',
    other: '📁',
  };
  return iconMap[type] || iconMap.other;
}

// 下载文件 - 真正的直接下载，不打开任何页面
async function downloadFile(file: FileItem) {
  if (!file?.path) {
    message.error('文件路径无效');
    return;
  }

  try {
    // 方法1：使用fetch + blob下载，确保不打开新页面
    const response = await fetch(file.path, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    });

    if (!response.ok) {
      throw new Error('网络请求失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    link.style.display = 'none';
    link.style.visibility = 'hidden';

    document.body.append(link);
    link.click();
    link.remove();

    // 清理blob URL
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
    }, 100);

    message.success(`下载完成：${file.name}`);
  } catch (error) {
    console.error('Fetch下载失败:', error);

    // 如果fetch失败，使用强制下载方法
    try {
      const link = document.createElement('a');
      link.href = file.path;
      link.download = file.name;
      link.setAttribute('download', file.name);
      link.setAttribute('target', '_self'); // 强制在当前窗口
      link.style.display = 'none';
      link.style.visibility = 'hidden';

      // 阻止默认的打开行为
      link.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
      });

      document.body.append(link);

      // 使用dispatchEvent触发下载
      const clickEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
      });

      link.dispatchEvent(clickEvent);
      link.remove();

      message.success(`开始下载：${file.name}`);
    } catch (fallbackError) {
      console.error('回退下载也失败:', fallbackError);

      // 最后的备选方案：使用iframe下载
      try {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.visibility = 'hidden';
        iframe.src = file.path;
        document.body.append(iframe);

        setTimeout(() => {
          iframe.remove();
        }, 5000);

        message.success(`开始下载：${file.name}`);
      } catch (iframeError) {
        console.error('iframe下载也失败:', iframeError);
        message.error('下载失败，请检查文件链接是否有效');
      }
    }
  }
}

// 预览文件 - 专门的预览功能
function previewFile(file: FileItem) {
  if (!file?.path) {
    message.error('文件路径无效');
    return;
  }

  const reg = /\.([0-9a-z]+)(?:[?#]|$)/i;
  const prefix = file.path.match(reg)?.[1]?.toLowerCase();

  if (
    prefix &&
    ['bmp', 'gif', 'jpeg', 'jpg', 'png', 'svg', 'webp'].includes(prefix)
  ) {
    // 图片类型：触发 Image 组件的预览功能
    // 通过程序化方式触发图片预览
    const imageElement = document.querySelector(
      `img[src="${file.path}"]`,
    ) as HTMLImageElement;
    if (imageElement) {
      imageElement.click();
    } else {
      // 如果找不到图片元素，则在新窗口打开
      window.open(file.path, '_blank');
    }
  } else if (prefix && ['pdf'].includes(prefix)) {
    // PDF：在新窗口打开
    window.open(file.path, '_blank');
  } else if (
    prefix &&
    ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(prefix)
  ) {
    // Office文档：使用预览组件
    filesmodalApi.setData({ url: file.path, fileType: prefix }).open();
  } else {
    // 其他类型：提示用户下载查看
    message.info('该文件类型不支持在线预览，请下载后查看');
  }
}

// 处理文件项点击事件 - 图片使用组件预览，其他文件执行预览功能
function handleFileClick(file: FileItem, e: Event) {
  // 如果是图片类型，不阻止默认行为，让 Image 组件处理预览
  if (file.type === 'image') {
    return; // 让 Image 组件的预览功能自然触发
  }

  // 非图片类型，阻止默认行为并执行自定义预览
  e.preventDefault();
  previewFile(file);
}
</script>

<template>
  <Modal
    title="附件列表"
    destroy-on-close
    draggable
    class="attachment-modal"
    :width="1000"
  >
    <template #extra>
      <div class="modal-extra">
        <span class="file-count">共 {{ data.length }} 个附件</span>
      </div>
    </template>

    <div class="file-modal-content">
      <div v-if="!data?.length" class="empty-tip">
        <div class="empty-icon">📁</div>
        <div class="empty-text">暂无附件</div>
      </div>
      <div v-else class="file-list-container">
        <!-- 使用 ImagePreviewGroup 包裹图片，实现图片预览组功能 -->
        <ImagePreviewGroup>
          <div class="file-grid">
            <div
              v-for="(file, index) in data"
              :key="index"
              class="file-card"
              @click="handleFileClick(file, $event)"
            >
              <div class="file-preview">
                <!-- 图片类型使用 a-image 组件，启用预览 -->
                <Image
                  v-if="file.type === 'image'"
                  :src="file.path"
                  :width="80"
                  :height="80"
                  :preview="true"
                />
                <!-- 非图片类型显示文件类型块 -->
                <div
                  v-else
                  class="file-icon-block"
                  :class="`type-${file.type}`"
                >
                  <div class="icon-emoji">
                    {{ getFileIcon(file.type) }}
                  </div>
                  <div class="file-ext">
                    {{ file.name.split('.').pop()?.toUpperCase() || 'FILE' }}
                  </div>
                </div>
              </div>

              <div class="file-details">
                <div class="file-title" :title="file.name">
                  {{ file.name }}
                </div>
                <div class="file-operations">
                  <Button
                    class="op-btn download"
                    @click.stop="downloadFile(file)"
                    title="下载"
                  >
                    ⬇️
                  </Button>
                  <Button
                    class="op-btn preview"
                    @click.stop="previewFile(file)"
                    title="预览"
                  >
                    👁️
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </ImagePreviewGroup>
      </div>
    </div>
    <filesmodal />
  </Modal>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 1200px) {
  .file-card {
    flex: 0 0 calc(25% - 12px); /* 4列 */
  }
}

@media (max-width: 900px) {
  .file-card {
    flex: 0 0 calc(33.333% - 11px); /* 3列 */
  }
}

@media (max-width: 768px) {
  .attachment-modal :deep(.ant-modal) {
    width: 95vw !important;
  }

  .file-modal-content {
    padding: 16px;
  }

  .file-card {
    flex: 0 0 calc(50% - 8px); /* 2列 */
    min-width: 120px;
    padding: 12px;
  }

  .file-preview {
    width: 60px;
    height: 60px;
  }

  .icon-emoji {
    font-size: 20px;
  }

  .file-title {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .file-card {
    flex: 0 0 100%; /* 1列 */
    max-width: none;
  }

  .file-grid {
    gap: 8px;
  }
}

.attachment-modal :deep(.ant-modal) {
  width: 1000px !important;
  max-width: 90vw !important;
}

.attachment-modal :deep(.ant-modal-content) {
  width: 100% !important;
}

.attachment-modal :deep(.ant-modal-body) {
  padding: 0 !important;
}

.modal-extra {
  display: flex;
  gap: 12px;
  align-items: center;
}

.file-count {
  padding: 2px 8px;
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  border-radius: 10px;
}

/* 主容器样式 */
.file-modal-content {
  width: 100%;
  max-height: 600px;
  padding: 20px;
  overflow-y: auto;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  margin-bottom: 12px;
  font-size: 48px;
}

.empty-text {
  font-size: 14px;
}

/* 文件列表容器 */
.file-list-container {
  width: 100%;
}

/* 文件网格布局 */
.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
}

/* 文件卡片 */
.file-card {
  display: flex;
  flex: 0 0 calc(20% - 12px); /* 5列布局，减去gap */
  flex-direction: column;
  align-items: center;
  min-width: 140px;
  max-width: 180px;
  padding: 16px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.file-card:hover {
  background: #f8f9fa;
  border-color: #1677ff;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

/* 文件预览区域 */
.file-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
  overflow: hidden;
  border-radius: 6px;
}

/* 文件图标块 */
.file-icon-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.icon-emoji {
  margin-bottom: 4px;
  font-size: 24px;
}

.file-ext {
  font-size: 10px;
  font-weight: 600;
  text-align: center;
}

/* 文件详情区域 */
.file-details {
  width: 100%;
  text-align: center;
}

.file-title {
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: nowrap;
}

/* 操作按钮 */
.file-operations {
  display: flex;
  gap: 8px;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-card:hover .file-operations {
  opacity: 1;
}

.op-btn {
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  background: rgb(255 255 255 / 90%);
  border: none;
  border-radius: 4px;
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.op-btn:hover {
  color: white;
  background: #1677ff;
  transform: scale(1.05);
}

/* 不同类型文件色块颜色 */
.type-document {
  color: #1677ff;
  background: linear-gradient(135deg, #e6f4ff 0%, #bae0ff 100%);
}

.type-image {
  color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
}

.type-video {
  color: #ff4d4f;
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

.type-audio {
  color: #722ed1;
  background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
}

.type-archive {
  color: #fa8c16;
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.type-other {
  color: #faad14;
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
}

/* 滚动条样式 */
.file-modal-content::-webkit-scrollbar {
  width: 6px;
}

.file-modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.file-modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
