/**
 * 文件上传对象标准化工具函数
 * 用于确保Upload组件的文件对象具有必要的属性，防止Vue渲染错误
 */

/**
 * 生成唯一的文件ID
 * @param index 文件索引
 * @param prefix 前缀
 * @returns 唯一ID
 */
function generateUniqueFileId(index: number = 0, prefix: string = ''): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).slice(2, 9);
  return prefix
    ? `${prefix}_${timestamp}_${index}_${random}`
    : `${timestamp}_${index}_${random}`;
}

/**
 * 标准化单个文件对象
 * @param file 文件对象或字符串
 * @param index 文件索引
 * @param prefix ID前缀
 * @returns 标准化的文件对象
 */
export function normalizeFileObject(
  file: any,
  index: number = 0,
  prefix: string = '',
): any {
  if (typeof file === 'string') {
    // 字符串格式：转换为标准文件对象
    return {
      uid: generateUniqueFileId(index, prefix),
      name: file.split('/').pop() || '文件',
      status: 'done',
      url: file,
    };
  } else if (file && typeof file === 'object') {
    // 对象格式：确保有必要的属性
    return {
      uid: file.uid || generateUniqueFileId(index, prefix),
      name:
        file.name || (file.url ? file.url.split('/').pop() : '文件') || '文件',
      status: file.status || 'done',
      url: file.url || file.path || '',
      ...file, // 保留原有属性
    };
  }

  // 其他情况直接返回
  return file;
}

/**
 * 标准化文件列表
 * @param fileList 文件列表（可能是数组、字符串或单个对象）
 * @param prefix ID前缀
 * @returns 标准化的文件对象数组
 */
export function normalizeFileList(fileList: any, prefix: string = ''): any[] {
  if (!fileList) {
    return [];
  }

  if (Array.isArray(fileList)) {
    // 数组格式：标准化每个文件对象
    return fileList.map((file, index) =>
      normalizeFileObject(file, index, prefix),
    );
  } else if (typeof fileList === 'string') {
    // 字符串格式：转换为单元素数组
    return [normalizeFileObject(fileList, 0, prefix)];
  } else if (typeof fileList === 'object') {
    // 对象格式：转换为单元素数组
    return [normalizeFileObject(fileList, 0, prefix)];
  }

  // 其他情况返回空数组
  return [];
}

/**
 * 为Upload组件处理表单数据中的文件字段
 * @param formData 表单数据
 * @param schema 表单配置
 * @param prefix ID前缀
 */
export function normalizeUploadFieldsInFormData(
  formData: Record<string, any>,
  schema: any[],
  prefix: string = '',
): void {
  if (!Array.isArray(schema)) {
    return;
  }

  schema.forEach((schemaItem: any) => {
    if (schemaItem.component === 'Upload' && schemaItem.fieldName) {
      const fieldValue = formData[schemaItem.fieldName];
      formData[schemaItem.fieldName] = normalizeFileList(fieldValue, prefix);
    }
  });
}

/**
 * 为Upload组件处理表单配置中的文件字段（针对EditTable等组件）
 * @param formData 表单数据
 * @param formConfig 表单配置
 * @param prefix ID前缀
 */
export function normalizeUploadFieldsInFormConfig(
  formData: Record<string, any>,
  formConfig: any[],
  prefix: string = '',
): void {
  if (!Array.isArray(formConfig)) {
    return;
  }

  formConfig.forEach((formItem: any) => {
    if (formItem.type === 'Upload' && formItem.field) {
      const fieldValue = formData[formItem.field];
      formData[formItem.field] = normalizeFileList(fieldValue, prefix);
    }
  });
}

/**
 * 处理Upload组件的onChange事件中的文件列表
 * @param fileList 文件列表
 * @param prefix ID前缀
 * @returns 标准化的文件列表
 */
export function normalizeUploadOnChangeFileList(
  fileList: any[],
  prefix: string = '',
): any[] {
  if (!Array.isArray(fileList)) {
    return [];
  }

  return fileList.map((file: any, index: number) => {
    // 如果文件对象缺少 uid，生成一个唯一的 uid
    if (!file.uid) {
      file.uid = generateUniqueFileId(index, prefix);
    }

    // 确保文件对象有 name 属性
    if (!file.name && file.url) {
      file.name = file.url.split('/').pop() || '未知文件';
    }

    // 确保文件对象有基本的状态
    if (!file.status && file.url) {
      file.status = 'done';
    }

    return file;
  });
}
