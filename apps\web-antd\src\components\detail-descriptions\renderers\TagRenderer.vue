<script setup lang="ts">
import type { OptionConfig, OptionItem } from '../types';

import { computed } from 'vue';

import { Tag } from 'ant-design-vue';

interface Props {
  value: any;
  options?: OptionConfig;
  placeholder?: string;
  separator?: string;
  maxDisplay?: number;
  showCount?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  options: undefined,
  placeholder: '-',
  separator: ', ',
  maxDisplay: undefined,
  showCount: false,
});

// 处理多选数据
const processedData = computed(() => {
  if (props.value === null || props.value === undefined || props.value === '') {
    return { isMultiple: false, values: [], displayText: props.placeholder };
  }

  // 处理数组或逗号分隔字符串的情况（自动检测多值）
  let valueArray: any[] = [];
  if (Array.isArray(props.value)) {
    valueArray = props.value;
  } else if (typeof props.value === 'string' && props.value.includes(',')) {
    // 处理逗号分隔的字符串
    valueArray = props.value
      .split(',')
      .map((v) => v.trim())
      .filter((v) => v !== '');
  } else {
    // 单个值
    valueArray = [props.value];
  }

  const isMultiple = valueArray.length > 1;
  const matchedOptions: OptionItem[] = [];

  // 查找匹配的选项
  valueArray.forEach((val) => {
    let option: null | OptionItem = null;

    // 从映射中查找
    if (props.options?.mapping) {
      option = props.options.mapping[val] || null;
    }

    // 从列表中查找
    if (!option && props.options?.list) {
      option = props.options.list.find((item) => item.value === val) || null;
    }

    if (option) {
      matchedOptions.push(option);
    } else {
      // 如果没有找到匹配项，创建一个默认选项
      matchedOptions.push({
        label: String(val),
        value: val,
      });
    }
  });

  // 处理显示数量限制
  let displayOptions = matchedOptions;
  let hasMore = false;

  if (props.maxDisplay && matchedOptions.length > props.maxDisplay) {
    displayOptions = matchedOptions.slice(0, props.maxDisplay);
    hasMore = true;
  }

  // 生成显示文本
  const displayLabels = displayOptions.map((opt) => opt.label);
  let displayText = displayLabels.join(props.separator);

  // 添加更多提示
  if (hasMore) {
    const remainingCount = matchedOptions.length - props.maxDisplay!;
    displayText += ` 等${remainingCount}项`;
  }

  // 添加总数显示
  if (props.showCount && matchedOptions.length > 1) {
    displayText += ` (共${matchedOptions.length}项)`;
  }

  return {
    isMultiple,
    values: matchedOptions,
    displayOptions: displayOptions || [],
    displayText,
    hasMore: hasMore || false,
    allLabels: matchedOptions.map((opt) => opt.label).join(props.separator),
  };
});
</script>

<template>
  <div class="tag-renderer">
    <!-- 多选情况：显示多个标签或文本 -->
    <template v-if="processedData.isMultiple">
      <!-- 如果有匹配的选项且数量不多，显示为多个标签 -->
      <template
        v-if="
          processedData.displayOptions.length <= 5 && !processedData.hasMore
        "
      >
        <div class="tag-container">
          <Tag
            v-for="option in processedData.displayOptions"
            :key="option.value"
            :color="option.color"
            :icon="option.icon"
            class="tag-item"
          >
            <template v-if="option.icon" #icon>
              <component :is="option.icon" />
            </template>
            {{ option.label }}
          </Tag>
        </div>
      </template>
      <!-- 否则显示为文本 -->
      <div v-else class="tag-text-multiple" :title="processedData.allLabels">
        {{ processedData.displayText }}
      </div>
    </template>

    <!-- 单选情况：显示单个标签 -->
    <template v-else-if="processedData.values.length === 1">
      <Tag
        :color="processedData.values[0].color"
        :icon="processedData.values[0].icon"
        class="tag-item"
      >
        <template v-if="processedData.values[0].icon" #icon>
          <component :is="processedData.values[0].icon" />
        </template>
        {{ processedData.values[0].label }}
      </Tag>
    </template>

    <!-- 无数据情况 -->
    <span v-else class="tag-fallback">{{ processedData.displayText }}</span>
  </div>
</template>

<style scoped>
.tag-renderer {
  display: block;
  width: 100%;
  line-height: 1.4;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px; /* 垂直间距6px，水平间距8px */
  align-items: flex-start;
  width: 100%;
}

.tag-item {
  flex-shrink: 0; /* 防止标签被压缩 */
  max-width: 100%; /* 防止标签过宽 */
  margin: 0 !important; /* 重置默认margin，使用gap控制间距 */
  word-break: break-all; /* 长文本换行 */
}

.tag-fallback {
  color: #999;
}

.tag-text-multiple {
  padding: 2px 0;
  line-height: 1.6;
  color: #333;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: normal;
}
</style>
