<script setup lang="ts">
import type { FileInfo } from '../types';

import { computed } from 'vue';

import {
  FileExcelOutlined,
  FileImageOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileZipOutlined,
} from '@ant-design/icons-vue';

interface Props {
  value: any;
  placeholder?: string;
  downloadable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '暂无文件',
  downloadable: true,
});

const fileList = computed<FileInfo[]>(() => {
  if (!props.value) return [];

  // 如果是字符串，转换为文件对象
  if (typeof props.value === 'string') {
    const fileName = props.value.split('/').pop() || '未知文件';
    return [{ name: fileName, url: props.value }];
  }

  // 如果是数组
  if (Array.isArray(props.value)) {
    return props.value.map((item) => {
      if (typeof item === 'string') {
        const fileName = item.split('/').pop() || '未知文件';
        return { name: fileName, url: item };
      }
      return item as FileInfo;
    });
  }

  // 如果是对象
  if (typeof props.value === 'object') {
    return [props.value as FileInfo];
  }

  return [];
});

/**
 * 获取文件图标
 */
function getFileIcon(file: FileInfo) {
  if (file.icon) return file.icon;

  const ext = file.name.split('.').pop()?.toLowerCase();

  switch (ext) {
    case '7z':
    case 'rar':
    case 'zip': {
      return FileZipOutlined;
    }
    case 'bmp':
    case 'gif':
    case 'jpeg':
    case 'jpg':
    case 'png':
    case 'webp': {
      return FileImageOutlined;
    }
    case 'doc':
    case 'docx': {
      return FileWordOutlined;
    }
    case 'pdf': {
      return FilePdfOutlined;
    }
    case 'xls':
    case 'xlsx': {
      return FileExcelOutlined;
    }
    default: {
      return FileOutlined;
    }
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(size: number): string {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  if (size < 1024 * 1024 * 1024)
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
}

/**
 * 下载文件
 */
function downloadFile(file: FileInfo) {
  if (!props.downloadable) return;

  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.name;
  link.target = '_blank';
  document.body.append(link);
  link.click();
  link.remove();
}
</script>

<template>
  <div class="file-renderer">
    <template v-if="fileList.length > 0">
      <div
        v-for="(file, index) in fileList"
        :key="index"
        class="file-item"
        @click="downloadFile(file)"
      >
        <a-space>
          <component :is="getFileIcon(file)" class="file-icon" />
          <div class="file-info">
            <div class="file-name">{{ file.name }}</div>
            <div v-if="file.size > 0" class="file-size">
              {{ formatFileSize(file.size) }}
            </div>
          </div>
        </a-space>
      </div>
    </template>
    <span v-else class="file-placeholder">{{ placeholder }}</span>
  </div>
</template>

<style scoped>
.file-renderer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: inline-flex;
  align-items: center;
  max-width: 300px;
  padding: 8px 12px;
  cursor: pointer;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.2s;
}

.file-item:hover {
  background: #f0f0f0;
  border-color: #40a9ff;
}

.file-icon {
  font-size: 16px;
  color: #1890ff;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #8c8c8c;
}

.file-placeholder {
  color: #999;
}
</style>
