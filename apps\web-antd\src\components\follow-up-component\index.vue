<script setup lang="ts">
/**
 * 跟进组件
 *
 * 功能特性：
 * - 显示跟进记录列表
 * - 支持添加新的跟进记录
 * - 支持查看跟进详情
 * - 支持时间线展示
 * - 支持文件附件上传
 */

import type { FollowUpProps, FollowUpRecord } from './types';

import { computed, h, ref } from 'vue';

import {
  ClockCircleOutlined,
  FileTextOutlined,
  PlusOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Divider,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Space,
  Tag,
  Timeline,
  TimelineItem,
  Upload,
} from 'ant-design-vue';

interface Props extends FollowUpProps {
  /** 跟进记录数据 */
  data?: FollowUpRecord[];
  /** 是否可以添加跟进 */
  canAdd?: boolean;
  /** 是否显示时间线模式 */
  timelineMode?: boolean;
  /** 标题 */
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  canAdd: true,
  timelineMode: true,
  title: '跟进记录',
});

const emit = defineEmits<{
  add: [record: Partial<FollowUpRecord>];
  refresh: [];
  view: [record: FollowUpRecord];
}>();

// 表单相关
const showAddModal = ref(false);
const formRef = ref();
const formData = ref<Partial<FollowUpRecord>>({
  content: '',
  attachments: [],
});

// 表单规则
const formRules = {
  content: [
    {
      required: true,
      message: '请输入跟进内容',
      trigger: 'blur',
    } as any,
    { min: 5, message: '跟进内容至少5个字符', trigger: 'blur' } as any,
  ],
};

// 计算属性
const followUpList = computed(() => {
  return (
    props.data
      ?.slice()
      .sort(
        (a, b) =>
          new Date(b.createTime || '').getTime() -
          new Date(a.createTime || '').getTime(),
      ) || []
  );
});

// 方法
const handleAddFollowUp = () => {
  showAddModal.value = true;
  formData.value = {
    content: '',
    attachments: [],
  };
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    const record: Partial<FollowUpRecord> = {
      ...formData.value,
      createTime: new Date().toISOString(),
      creator: '当前用户', // 实际应该从用户信息获取
    };

    emit('add', record);
    showAddModal.value = false;
    message.success('跟进记录添加成功');

    // 重置表单
    formRef.value?.resetFields();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleCancel = () => {
  showAddModal.value = false;
  formRef.value?.resetFields();
};

const handleViewDetail = (record: FollowUpRecord) => {
  emit('view', record);
};

const formatTime = (time?: string) => {
  if (!time) return '';
  return new Date(time).toLocaleString('zh-CN');
};

const getStatusColor = (status?: string) => {
  const colorMap: Record<string, string> = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    cancelled: 'red',
  };
  return colorMap[status || 'pending'] || 'default';
};

const getStatusText = (status?: string) => {
  const textMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
  };
  return textMap[status || 'pending'] || '未知';
};

// 获取上传请求头
const getUploadHeaders = () => {
  const token =
    typeof window === 'undefined' ? '' : window.localStorage.getItem('token');
  return {
    authorization: `Bearer ${token}`,
  };
};

// 文件上传处理
const handleFileUpload = (info: any) => {
  if (info.file.status === 'done') {
    // 移除重复的成功提示，统一由upload store管理
    formData.value.attachments = formData.value.attachments || [];
    formData.value.attachments.push({
      name: info.file.name,
      url: info.file.response?.url || '',
      size: info.file.size,
    });
  }
  // 移除重复的错误提示，统一由upload store管理
};
</script>

<template>
  <div class="follow-up-component">
    <Card :title="title" class="follow-up-card">
      <!-- 操作栏 -->
      <template #extra>
        <Space>
          <Button
            v-if="canAdd"
            type="primary"
            :icon="h(PlusOutlined)"
            @click="handleAddFollowUp"
          >
            添加跟进
          </Button>
          <Button @click="emit('refresh')"> 刷新 </Button>
        </Space>
      </template>

      <!-- 跟进记录列表 -->
      <div v-if="followUpList.length > 0" class="follow-up-list">
        <!-- 时间线模式 -->
        <Timeline v-if="timelineMode" class="follow-up-timeline">
          <TimelineItem
            v-for="(record, index) in followUpList"
            :key="record.id || index"
            :color="getStatusColor(record.status)"
          >
            <template #dot>
              <ClockCircleOutlined style="font-size: 16px" />
            </template>

            <div class="timeline-content">
              <div class="timeline-header">
                <Space>
                  <Tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </Tag>
                  <span class="creator">
                    <UserOutlined />
                    {{ record.creator }}
                  </span>
                  <span class="time">{{ formatTime(record.createTime) }}</span>
                </Space>
              </div>

              <div class="timeline-body">
                <p class="content">{{ record.content }}</p>

                <!-- 附件列表 -->
                <div v-if="record.attachments?.length" class="attachments">
                  <Divider orientation="left" plain>附件</Divider>
                  <Space wrap>
                    <Tag
                      v-for="(file, fileIndex) in record.attachments"
                      :key="fileIndex"
                      color="blue"
                      class="attachment-tag"
                    >
                      <FileTextOutlined />
                      {{ file.name }}
                    </Tag>
                  </Space>
                </div>
              </div>

              <div class="timeline-actions">
                <Button
                  type="link"
                  size="small"
                  @click="handleViewDetail(record)"
                >
                  查看详情
                </Button>
              </div>
            </div>
          </TimelineItem>
        </Timeline>

        <!-- 列表模式 -->
        <div v-else class="follow-up-cards">
          <Card
            v-for="(record, index) in followUpList"
            :key="record.id || index"
            size="small"
            class="follow-up-item-card"
          >
            <template #title>
              <Space>
                <Tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </Tag>
                <span>{{ record.creator }}</span>
              </Space>
            </template>

            <template #extra>
              <span class="time">{{ formatTime(record.createTime) }}</span>
            </template>

            <p class="content">{{ record.content }}</p>

            <!-- 附件列表 -->
            <div v-if="record.attachments?.length" class="attachments">
              <Space wrap>
                <Tag
                  v-for="(file, fileIndex) in record.attachments"
                  :key="fileIndex"
                  color="blue"
                  size="small"
                >
                  <FileTextOutlined />
                  {{ file.name }}
                </Tag>
              </Space>
            </div>
          </Card>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-content">
          <FileTextOutlined class="empty-icon" />
          <p class="empty-text">暂无跟进记录</p>
          <Button
            v-if="canAdd"
            type="primary"
            :icon="h(PlusOutlined)"
            @click="handleAddFollowUp"
          >
            添加第一条跟进记录
          </Button>
        </div>
      </div>
    </Card>

    <!-- 添加跟进弹窗 -->
    <Modal
      v-model:open="showAddModal"
      title="添加跟进记录"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <FormItem label="跟进内容" name="content">
          <Input.TextArea
            v-model:value="formData.content"
            placeholder="请输入跟进内容..."
            :rows="4"
            show-count
            :maxlength="500"
          />
        </FormItem>

        <FormItem label="附件">
          <Upload
            action="/api/oss/putFile"
            :headers="getUploadHeaders()"
            @change="handleFileUpload"
          >
            <Button :icon="h(PlusOutlined)">上传附件</Button>
          </Upload>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
.follow-up-component {
  width: 100%;
}

.follow-up-card {
  width: 100%;
}

.follow-up-list {
  width: 100%;
}

.follow-up-timeline {
  padding: 16px 0;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-header {
  margin-bottom: 8px;
}

.creator {
  font-size: 14px;
  color: #666;
}

.time {
  font-size: 12px;
  color: #999;
}

.timeline-body {
  margin: 8px 0;
}

.content {
  margin: 8px 0;
  line-height: 1.6;
  color: #333;
}

.attachments {
  margin-top: 8px;
}

.attachment-tag {
  cursor: pointer;
}

.timeline-actions {
  margin-top: 8px;
}

.follow-up-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.follow-up-item-card {
  width: 100%;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px;
}

.empty-content {
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #d9d9d9;
}

.empty-text {
  margin-bottom: 16px;
  font-size: 14px;
  color: #999;
}
</style>
