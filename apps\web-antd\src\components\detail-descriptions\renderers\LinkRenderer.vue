<script setup lang="ts">
import type { LinkInfo } from '../types';

import { computed } from 'vue';

import { LinkOutlined } from '@ant-design/icons-vue';

interface Props {
  value: any;
  placeholder?: string;
  defaultIcon?: any;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '暂无链接',
  defaultIcon: LinkOutlined,
});

const linkInfo = computed<LinkInfo | null>(() => {
  if (!props.value) return null;

  // 如果是字符串，转换为链接对象
  if (typeof props.value === 'string') {
    return {
      href: props.value,
      text: props.value,
      target: '_blank',
      icon: props.defaultIcon,
    };
  }

  // 如果是对象
  if (typeof props.value === 'object') {
    const link = props.value as LinkInfo;
    return {
      ...link,
      icon: link.icon || props.defaultIcon,
    };
  }

  return null;
});
</script>

<template>
  <div class="link-renderer">
    <template v-if="linkInfo">
      <a
        :href="linkInfo.href"
        :target="linkInfo.target || '_blank'"
        class="link-item"
      >
        <a-space>
          <component
            v-if="linkInfo.icon"
            :is="linkInfo.icon"
            class="link-icon"
          />
          <span>{{ linkInfo.text }}</span>
        </a-space>
      </a>
    </template>
    <span v-else class="link-placeholder">{{ placeholder }}</span>
  </div>
</template>

<style scoped>
.link-renderer {
  display: inline-block;
}

.link-item {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.2s;
}

.link-item:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.link-icon {
  font-size: 14px;
}

.link-placeholder {
  color: #999;
}
</style>
