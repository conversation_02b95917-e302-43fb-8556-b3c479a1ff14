<script setup lang="ts">
/**
 * 通用详情抽屉组件
 *
 * 功能特性：
 * - 支持详情描述展示（基于 DetailDescriptions 组件）
 * - 支持多标签页内容展示（基于 GeneralTabs 组件）
 * - 统一的错误处理和重试机制
 * - 响应式布局和加载状态管理
 * - 支持空状态展示
 *
 * 使用场景：
 * - 数据详情查看
 * - 多维度信息展示
 * - 复杂数据结构的分组展示
 */

import type { BackendTabItem } from '#/components/general-tabs';

import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button, Empty, message, Result, Typography } from 'ant-design-vue';

import DetailDescriptions from '#/components/detail-descriptions';
import GeneralTabs from '#/components/general-tabs';
import { handleError } from '#/utils/error-handler';

// ==================== 数据状态管理 ====================

/** 详情数据 - 存储从接口获取的详情信息 */
const detailData = ref<Record<string, any>>({});

/** 头部表单数据 - 用于详情描述组件的字段配置 */
const headerFormdatas = ref<any[]>([]);

/** 标签页配置 - 存储后端返回的标签页配置信息 */
const tabItems = ref<BackendTabItem[]>([]);

/** 表单数据 - 存储各个标签页需要的数据 */
const formdatas = ref<Record<string, any>>({});

/** 当前激活的标签页 - 始终默认为 main */
const currentActiveKey = ref<string>('main');

// ==================== 渲染状态管理 ====================

/** 标签页渲染状态 - 跟踪各个标签页的渲染完成状态 */
const tabsRenderingStatus = ref<Record<string, boolean>>({});

/** 所有标签页是否渲染完成 */
const allTabsRendered = ref(false);

// ==================== 错误处理状态 ====================

/** 是否有错误 */
const hasError = ref(false);

/** 用户友好的错误信息 */
const errorMessage = ref('');

/** 技术错误详情 - 用于问题反馈和调试 */
const technicalError = ref('');

/** 是否正在重试 */
const isRetrying = ref(false);

// ==================== 核心业务逻辑 ====================

/**
 * 加载详情数据
 *
 * 主要功能：
 * 1. 从抽屉 API 获取行数据和接口函数
 * 2. 调用详情接口获取完整数据
 * 3. 处理数据并设置到各个响应式变量
 * 4. 初始化标签页渲染状态
 * 5. 统一的错误处理和用户提示
 */
async function loadDetailData() {
  // 获取抽屉传入的数据和 API 函数
  const { row, apiFn } = drawerApi.getData<Record<string, any>>();

  try {
    // 重置错误状态，确保每次加载都是干净的状态
    resetErrorState();

    // 设置加载状态
    drawerApi.setState({ loading: true });

    // 构建 API 请求参数 - 使用行数据的 id 作为查询参数
    const apiParams = { _id: row.id };

    // 调用详情接口获取数据
    const {
      headerFormdatas: responseHeaderFormdatas,
      tabs,
      formdatas: responseFormdatas,
    } = await apiFn.detail(apiParams);

    // 设置详情数据
    // 注意：这里使用 row 数据作为基础，因为它包含了列表页面的基本信息
    detailData.value = row;

    // 设置头部表单数据 - 用于详情描述组件
    headerFormdatas.value = responseHeaderFormdatas || [];

    // 设置标签页配置和数据
    tabItems.value = tabs || [];
    formdatas.value = responseFormdatas || {};

    // 确保 main 标签页始终是激活状态
    ensureMainTabActive();

    // 初始化标签页渲染状态管理
    initTabsRenderingStatus();
  } catch (error: any) {
    // 统一错误处理
    handleLoadError(error);
  } finally {
    // 重置重试状态
    // 注意：不在这里取消 loading 状态，等待所有标签页渲染完成后再取消
    isRetrying.value = false;
  }
}

// ==================== 辅助函数 ====================

/**
 * 重置错误状态
 * 在每次加载数据前调用，确保状态干净
 */
function resetErrorState() {
  hasError.value = false;
  errorMessage.value = '';
  technicalError.value = '';
}

/**
 * 处理加载错误
 * 统一的错误处理逻辑，包含用户提示和状态设置
 */
function handleLoadError(error: any) {
  const errorInfo = handleError(error, '获取详情数据');
  hasError.value = true;
  errorMessage.value = errorInfo.message;
  technicalError.value = errorInfo.technicalDetails;

  // 取消 loading 状态
  drawerApi.setState({ loading: false });

  // 显示错误提示
  message.error(errorInfo.message);
}

/**
 * 初始化标签页渲染状态
 * 为每个标签页创建渲染状态跟踪
 */
function initTabsRenderingStatus() {
  const status: Record<string, boolean> = {};
  tabItems.value.forEach((tab) => {
    status[tab.key] = false; // 初始状态为未渲染
  });
  tabsRenderingStatus.value = status;
  allTabsRendered.value = false;
}

/**
 * 检查所有标签页是否都已渲染完成
 * 如果全部完成，则取消加载状态
 */
function checkAllTabsRendered() {
  const allRendered = Object.values(tabsRenderingStatus.value).every(
    (status) => status === true,
  );
  allTabsRendered.value = allRendered;

  if (allRendered) {
    // 所有标签页渲染完成，取消加载状态
    drawerApi.setState({ loading: false });
  }
}

/**
 * 处理单个标签页渲染完成事件
 * 更新对应标签页的渲染状态，并检查是否全部完成
 */
function handleTabRendered(tabKey: string) {
  tabsRenderingStatus.value[tabKey] = true;
  checkAllTabsRendered();
}

/**
 * 重试加载数据
 * 用户点击重试按钮时调用
 */
async function handleRetry() {
  isRetrying.value = true;
  await loadDetailData();
}

// ==================== 抽屉配置和事件处理 ====================

/**
 * 抽屉配置
 * 使用 Vben 抽屉组件，配置打开/关闭事件处理
 */
const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      // 重置标签页为 main
      currentActiveKey.value = 'main';
      // 抽屉打开时自动加载详情数据
      await loadDetailData();
    }
  },
  onConfirm: async () => {
    // 确认按钮处理（当前未使用）
  },
  onCancel: async () => {
    // 取消按钮处理（当前未使用）
  },
});

/**
 * 确保 main 标签页始终是激活状态
 * 如果存在 main 标签页，则将其设置为激活状态
 */
function ensureMainTabActive() {
  // 检查是否存在 main 标签页
  const hasMainTab = tabItems.value.some((tab) => tab.key === 'main');
  if (hasMainTab) {
    currentActiveKey.value = 'main';
  } else if (tabItems.value.length > 0) {
    // 如果没有 main 标签页，选择第一个可用的标签页
    const firstTab = tabItems.value[0];
    if (firstTab?.key) {
      currentActiveKey.value = firstTab.key;
    }
  }
}

/**
 * 标签页切换事件处理
 * 当用户切换标签页时触发
 */
function handleTabChange(activeKey: string) {
  // 更新当前激活的标签页
  currentActiveKey.value = activeKey;
  // 可以在这里添加其他标签页切换时的逻辑
}
</script>
<template>
  <!-- 详情抽屉容器 -->
  <Drawer>
    <div class="detail-content">
      <!-- ==================== 错误状态显示 ==================== -->
      <div v-if="hasError" class="error-section">
        <Result status="warning" title="数据加载失败" :sub-title="errorMessage">
          <template #extra>
            <!-- 重试按钮 -->
            <div class="error-actions">
              <Button type="primary" :loading="isRetrying" @click="handleRetry">
                {{ isRetrying ? '重新加载...' : '重新加载' }}
              </Button>
            </div>

            <!-- 技术错误详情 - 用于问题反馈和调试 -->
            <div v-if="technicalError" class="technical-error">
              <Typography.Title
                :level="5"
                style="margin-top: 16px; margin-bottom: 8px"
              >
                技术详情（用于问题反馈）：
              </Typography.Title>
              <Typography.Paragraph
                :code="true"
                :copyable="{ text: technicalError }"
                class="technical-error-content"
              >
                {{ technicalError }}
              </Typography.Paragraph>
            </div>
          </template>
        </Result>
      </div>

      <!-- ==================== 正常内容显示 ==================== -->
      <template v-else>
        <!-- 详情描述部分 - 显示基本信息字段 -->
        <div v-if="headerFormdatas.length > 0" class="descriptions-section">
          <DetailDescriptions
            :data="detailData"
            :raw-fields="headerFormdatas"
            :bordered="false"
            :column="4"
          />
        </div>

        <!-- 空状态显示 - 当没有详情数据且不在加载中时显示 -->
        <div
          v-else-if="!drawerApi?.store?.state?.loading"
          class="empty-section"
        >
          <Empty
            description="暂无详情数据"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          >
            <Button type="primary" @click="handleRetry"> 刷新数据 </Button>
          </Empty>
        </div>

        <!-- 标签页部分 - 显示多维度详细信息 -->
        <div class="tabs-section">
          <div v-if="tabItems.length > 0">
            <GeneralTabs
              :backend-tabs="tabItems"
              :formdatas="formdatas"
              type="card"
              default-active-key="main"
              v-model:active-key="currentActiveKey"
              @change="handleTabChange"
              @tab-rendered="handleTabRendered"
            />
          </div>
        </div>
      </template>
    </div>
  </Drawer>
</template>

<style scoped>
/* ==================== 布局样式 ==================== */

/** 详情内容容器 */
.detail-content {
  padding: 16px;
}

/** 详情描述部分 - 基本信息展示区域 */
.descriptions-section {
  margin-bottom: 24px;
}

/** 标签页部分 - 多维度信息展示区域 */
.tabs-section {
  margin-top: 16px;
}

/* ==================== 错误状态样式 ==================== */

/** 错误状态容器 - 居中显示错误信息 */
.error-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

/** 错误操作按钮区域 */
.error-actions {
  margin-bottom: 8px;
}

/** 技术错误详情容器 */
.technical-error {
  max-width: 600px;
  margin: 0 auto;
  text-align: left;
}

/** 技术错误内容样式 - 可滚动的代码块 */
.technical-error-content {
  max-height: 120px;
  padding: 8px 12px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
  word-break: break-all;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

/* ==================== 空状态样式 ==================== */

/** 空状态容器 - 居中显示空状态信息 */
.empty-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
}

/* ==================== 其他样式 ==================== */

/** 无标签页时的提示样式（备用） */
.no-tabs {
  padding: 40px 20px;
  font-size: 14px;
  color: #999;
  text-align: center;
}

/** 带主题色的分组标题样式（备用） */
.group-title-with-theme {
  padding-left: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-primary-color, #1890ff);
  border-left: 4px solid var(--ant-primary-color, #1890ff);
}
</style>
