/**
 * GeneralEditingDrawer 辅助工具函数
 * 用于处理外部参数映射和模式管理
 */

export type EditingMode = 'create' | 'edit';

export interface EditingDrawerData {
  schema: any[];
  row: Record<string, any>;
  apiFn: any;
  params: Record<string, any>;
  mode: EditingMode;
}

export interface ParamsMappingContext {
  currentUser?: {
    [key: string]: any;
    id: number | string;
    name?: string;
  };
  department?: {
    [key: string]: any;
    id: number | string;
    name?: string;
  };
  parent?: {
    [key: string]: any;
    id: number | string;
  };
  [key: string]: any;
}

export type ParamsMappingFunction = (
  mode: EditingMode,
  row?: Record<string, any>,
  context?: ParamsMappingContext,
) => Record<string, any>;

/**
 * 默认的参数映射函数
 * @param mode 编辑模式
 * @param row 行数据（编辑模式时使用）
 * @param context 上下文信息
 * @returns 处理后的参数对象
 */
export const defaultParamsMapping: ParamsMappingFunction = (
  mode: EditingMode,
  row?: Record<string, any>,
  context?: ParamsMappingContext,
) => {
  const params: Record<string, any> = {};
  const currentTime = new Date().toISOString();

  // 添加操作者信息
  if (context?.currentUser?.id) {
    params.operator_id = context.currentUser.id;
  }

  if (mode === 'create') {
    // 新增模式参数
    params.created_at = currentTime;
    if (context?.currentUser?.id) {
      params.created_by = context.currentUser.id;
    }

    // 设置默认状态
    params.status = 1;

    // 添加上下文相关的默认值
    if (context?.department?.id) {
      params.department_id = context.department.id;
    }
    if (context?.parent?.id) {
      params.parent_id = context.parent.id;
    }
  } else {
    // 编辑模式参数
    if (row?.id) {
      params.id = row.id;
    }
    params.updated_at = currentTime;
    if (context?.currentUser?.id) {
      params.updated_by = context.currentUser.id;
    }

    // 版本控制
    if (row?.version !== undefined) {
      params.version = (row.version || 0) + 1;
    }
  }

  return params;
};

/**
 * 创建自定义参数映射函数
 * @param customMapping 自定义映射逻辑
 * @param customMapping.common 通用映射函数，适用于所有模式
 * @param customMapping.create 新增模式专用映射函数
 * @param customMapping.edit 编辑模式专用映射函数
 * @returns 参数映射函数
 */
export const createParamsMapping = (customMapping: {
  common?: (
    mode: EditingMode,
    row?: Record<string, any>,
    context?: ParamsMappingContext,
  ) => Record<string, any>;
  create?: (
    row?: Record<string, any>,
    context?: ParamsMappingContext,
  ) => Record<string, any>;
  edit?: (
    row?: Record<string, any>,
    context?: ParamsMappingContext,
  ) => Record<string, any>;
}): ParamsMappingFunction => {
  return (
    mode: EditingMode,
    row?: Record<string, any>,
    context?: ParamsMappingContext,
  ) => {
    let params: Record<string, any> = {};

    // 应用通用映射
    if (customMapping.common) {
      params = { ...params, ...customMapping.common(mode, row, context) };
    }

    // 应用模式特定映射
    if (mode === 'create' && customMapping.create) {
      params = { ...params, ...customMapping.create(row, context) };
    } else if (mode === 'edit' && customMapping.edit) {
      params = { ...params, ...customMapping.edit(row, context) };
    }

    return params;
  };
};

/**
 * 编辑抽屉辅助类
 */
export class EditingDrawerHelper {
  private context: ParamsMappingContext;
  private paramsMapping: ParamsMappingFunction;

  constructor(
    paramsMapping: ParamsMappingFunction = defaultParamsMapping,
    context: ParamsMappingContext = {},
  ) {
    this.paramsMapping = paramsMapping;
    this.context = context;
  }

  /**
   * 准备新增模式的数据
   * @param schema 表单配置
   * @param apiFn API 函数
   * @param additionalParams 额外参数
   * @returns 编辑抽屉数据
   */
  prepareCreateData(
    schema: any[],
    apiFn: any,
    additionalParams: Record<string, any> = {},
  ): EditingDrawerData {
    const params = {
      ...this.paramsMapping('create', {}, this.context),
      ...additionalParams,
    };

    return {
      schema,
      row: {},
      apiFn,
      params,
      mode: 'create',
    };
  }

  /**
   * 通用的准备数据方法
   * @param mode 编辑模式
   * @param schema 表单配置
   * @param apiFn API 函数
   * @param row 行数据（编辑模式时必需）
   * @param additionalParams 额外参数
   * @returns 编辑抽屉数据
   */
  prepareData(
    mode: EditingMode,
    schema: any[],
    apiFn: any,
    row: Record<string, any> = {},
    additionalParams: Record<string, any> = {},
  ): EditingDrawerData {
    return mode === 'create'
      ? this.prepareCreateData(schema, apiFn, additionalParams)
      : this.prepareEditData(schema, row, apiFn, additionalParams);
  }

  /**
   * 准备编辑模式的数据
   * @param schema 表单配置
   * @param row 要编辑的行数据
   * @param apiFn API 函数
   * @param additionalParams 额外参数
   * @returns 编辑抽屉数据
   */
  prepareEditData(
    schema: any[],
    row: Record<string, any>,
    apiFn: any,
    additionalParams: Record<string, any> = {},
  ): EditingDrawerData {
    const params = {
      ...this.paramsMapping('edit', row, this.context),
      ...additionalParams,
    };

    return {
      schema,
      row,
      apiFn,
      params,
      mode: 'edit',
    };
  }

  /**
   * 设置参数映射函数
   * @param mapping 新的参数映射函数
   */
  setParamsMapping(mapping: ParamsMappingFunction) {
    this.paramsMapping = mapping;
  }

  /**
   * 更新上下文
   * @param newContext 新的上下文信息
   */
  updateContext(newContext: Partial<ParamsMappingContext>) {
    this.context = { ...this.context, ...newContext };
  }
}

/**
 * 创建编辑抽屉辅助实例
 * @param context 初始上下文
 * @param paramsMapping 参数映射函数
 * @returns 编辑抽屉辅助实例
 */
export const createEditingDrawerHelper = (
  context: ParamsMappingContext = {},
  paramsMapping: ParamsMappingFunction = defaultParamsMapping,
) => {
  return new EditingDrawerHelper(paramsMapping, context);
};

/**
 * 常用的参数映射预设
 */
export const commonParamsMappings = {
  /**
   * 用户管理的参数映射
   */
  userManagement: createParamsMapping({
    create: (row, context) => ({
      status: 'active',
      role: 'user',
      created_by: context?.currentUser?.id,
    }),
    edit: (row, context) => ({
      id: row?.id,
      updated_by: context?.currentUser?.id,
      last_login_ip: context?.clientIp,
    }),
  }),

  /**
   * 部门管理的参数映射
   */
  departmentManagement: createParamsMapping({
    create: (row, context) => ({
      status: 1,
      sort_order: 999,
      parent_id: context?.parent?.id || 0,
    }),
    edit: (row, context) => ({
      id: row?.id,
      updated_by: context?.currentUser?.id,
    }),
  }),

  /**
   * 文档管理的参数映射
   */
  documentManagement: createParamsMapping({
    create: (row, context) => ({
      status: 'draft',
      author_id: context?.currentUser?.id,
      category_id: context?.category?.id,
    }),
    edit: (row, context) => ({
      id: row?.id,
      editor_id: context?.currentUser?.id,
      edit_count: (row?.edit_count || 0) + 1,
    }),
  }),
};
