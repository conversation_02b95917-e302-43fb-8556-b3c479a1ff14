/**
 * 错误处理工具函数
 * 将技术性错误转换为用户友好的错误信息
 */

export interface ErrorInfo {
  /** 用户友好的错误信息 */
  message: string;
  /** 错误类型 */
  type:
    | 'network'
    | 'notfound'
    | 'permission'
    | 'server'
    | 'timeout'
    | 'unknown';
  /** 是否可以重试 */
  retryable: boolean;
  /** 技术详情（用于问题反馈） */
  technicalDetails: string;
}

/**
 * 获取技术详情信息
 * @param error 原始错误对象
 * @returns 格式化的技术详情
 */
export function getTechnicalDetails(error: any): string {
  if (!error) return '未知错误';

  // 尝试获取最有用的错误信息
  const details = [];

  // 错误消息
  if (error.message) {
    details.push(`错误信息: ${error.message}`);
  }

  // HTTP状态和响应
  if (error.response) {
    details.push(
      `HTTP状态: ${error.response.status} ${error.response.statusText || ''}`,
    );
    if (error.response.data?.message) {
      details.push(`服务器消息: ${error.response.data.message}`);
    }
  }

  // 错误代码
  if (error.code) {
    details.push(`错误代码: ${error.code}`);
  }

  // 请求URL（如果有）
  if (error.config?.url) {
    details.push(`请求地址: ${error.config.url}`);
  }

  // 如果没有有用的信息，返回JSON字符串
  if (details.length === 0) {
    try {
      return JSON.stringify(error, null, 2);
    } catch {
      return String(error);
    }
  }

  return details.join('\n');
}

/**
 * 将错误对象转换为用户友好的错误信息
 * @param error 原始错误对象
 * @returns 用户友好的错误信息
 */
export function getUserFriendlyError(error: any): ErrorInfo {
  const technicalDetails = getTechnicalDetails(error);

  // 网络错误
  if (
    error?.code === 'NETWORK_ERROR' ||
    error?.message?.includes('fetch') ||
    error?.message?.includes('Network Error') ||
    error?.name === 'NetworkError'
  ) {
    return {
      message: '网络连接异常，请检查网络后重试',
      type: 'network',
      retryable: true,
      technicalDetails,
    };
  }

  // HTTP状态码错误
  if (error?.response?.status) {
    const status = error.response.status;

    if (status === 404) {
      return {
        message: '请求的数据不存在或已被删除',
        type: 'notfound',
        retryable: false,
        technicalDetails,
      };
    }

    if (status === 403) {
      return {
        message: '没有权限访问该数据',
        type: 'permission',
        retryable: false,
        technicalDetails,
      };
    }

    if (status === 401) {
      return {
        message: '登录已过期，请重新登录',
        type: 'permission',
        retryable: false,
        technicalDetails,
      };
    }

    if (status >= 500) {
      return {
        message: '服务器暂时不可用，请稍后重试',
        type: 'server',
        retryable: true,
        technicalDetails,
      };
    }

    if (status >= 400) {
      return {
        message: '请求参数错误',
        type: 'unknown',
        retryable: false,
        technicalDetails,
      };
    }
  }

  // 超时错误
  if (
    error?.message?.includes('timeout') ||
    error?.code === 'TIMEOUT' ||
    error?.name === 'TimeoutError'
  ) {
    return {
      message: '请求超时，请稍后重试',
      type: 'timeout',
      retryable: true,
      technicalDetails,
    };
  }

  // 解析错误
  if (error?.message?.includes('JSON') || error?.message?.includes('parse')) {
    return {
      message: '数据格式错误',
      type: 'unknown',
      retryable: true,
      technicalDetails,
    };
  }

  // 默认错误
  return {
    message: '数据加载失败，请稍后重试',
    type: 'unknown',
    retryable: true,
    technicalDetails,
  };
}

/**
 * 根据错误类型获取建议的操作
 * @param errorType 错误类型
 * @returns 建议的操作文本
 */
export function getErrorSuggestion(errorType: ErrorInfo['type']): string {
  const suggestions = {
    network: '请检查网络连接后重试',
    permission: '请联系管理员获取权限',
    server: '服务器正在维护，请稍后重试',
    timeout: '网络较慢，请稍后重试',
    notfound: '数据可能已被删除',
    unknown: '请稍后重试或联系技术支持',
  };

  return suggestions[errorType] || suggestions.unknown;
}

/**
 * 错误日志记录（开发环境下详细记录，生产环境下简化记录）
 * @param _error 错误对象
 * @param _context 错误上下文
 */
export function logError(_error: any, _context: string = '') {
  const isDev = import.meta.env.DEV;

  if (isDev) {
    // 开发环境可以保留详细错误信息用于调试
    // 但在生产环境中应该移除或使用专门的日志系统
  } else {
    // 生产环境应该使用专门的错误监控服务
    // 而不是直接使用 console.error
  }
}

/**
 * 通用错误处理函数
 * @param error 错误对象
 * @param context 错误上下文
 * @returns 处理后的错误信息
 */
export function handleError(error: any, context: string = ''): ErrorInfo {
  // 记录错误日志
  logError(error, context);

  // 返回用户友好的错误信息
  return getUserFriendlyError(error);
}

/**
 * 创建重试函数
 * @param originalFunction 原始函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟（毫秒）
 * @returns 带重试功能的函数
 */
export function createRetryFunction<T extends (...args: any[]) => Promise<any>>(
  originalFunction: T,
  maxRetries: number = 3,
  delay: number = 1000,
): T {
  return (async (...args: any[]) => {
    let lastError: any;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await originalFunction(...args);
      } catch (error) {
        lastError = error;

        // 如果是最后一次尝试，直接抛出错误
        if (i === maxRetries) {
          throw error;
        }

        // 检查是否可以重试
        const errorInfo = getUserFriendlyError(error);
        if (!errorInfo.retryable) {
          throw error;
        }

        // 等待后重试
        await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)));
      }
    }

    throw lastError;
  }) as T;
}
