# transformToGroupedDescriptions PHP 后端数据规范文档

## 概述

本文档为 PHP 后端开发者提供 `transformToGroupedDescriptions` 函数所需的数据格式规范。该函数负责将后端返回的详情字段配置转换为分组描述列表格式，专门用于详情页面的数据展示，支持字段分组、特殊字段渲染、布局控制等功能。

## 函数签名

```typescript
transformToGroupedDescriptions(
  groupsData: any
): Array<{ rules: DescriptionRule[]; title: string }>
```

## 核心数据结构

### 1. 基础字段配置 (DescriptionRule)

```php
<?php
// PHP 数组格式
$descriptionRule = [
    'field' => 'username',              // 必填：字段名，支持嵌套路径如 'user.name'
    'title' => '用户名',                // 必填：显示标签
    'type' => 'text',                   // 可选：组件类型，默认为 text
    'span' => 1,                        // 可选：跨列数
    'options' => [                      // 可选：选项配置（用于 tag、badge 等）
        'mapping' => [
            '1' => ['label' => '启用', 'color' => 'green'],
            '0' => ['label' => '禁用', 'color' => 'red'],
        ],
    ],
    'transform' => null,                // 可选：数据转换函数
    'condition' => true,                // 可选：显示条件
    'themePrefix' => 'primary',         // 可选：主题色前缀
    'labelStyle' => [],                 // 可选：自定义标签样式
    'contentStyle' => [],               // 可选：自定义内容样式
    'componentProps' => [],             // 可选：组件属性
];
```

### 2. 支持的组件类型 (type)

| 类型          | 说明         | 适用场景           |
| ------------- | ------------ | ------------------ |
| `text`        | 普通文本     | 默认文本显示       |
| `input`       | 输入框文本   | 表单输入字段显示   |
| `tag`         | 标签显示     | 状态、分类等       |
| `text_tag`    | 文本标签     | 列表页面的文本标签 |
| `badge`       | 徽章显示     | 数量、状态等       |
| `date`        | 日期显示     | 日期格式化显示     |
| `datetime`    | 日期时间显示 | 日期时间格式化显示 |
| `image`       | 图片显示     | 头像、图片等       |
| `file`        | 文件显示     | 附件、文档等       |
| `files`       | 文件列表显示 | 多个附件、文档     |
| `link`        | 链接显示     | 外部链接           |
| `rate`        | 星级评分     | 评分显示           |
| `person`      | 人员显示     | 用户信息           |
| `dept`        | 部门显示     | 部门信息           |
| `multiselect` | 多选显示     | 多个选项           |
| `checkbox`    | 复选框显示   | 多选状态           |
| `radio`       | 单选显示     | 单选状态           |
| `edit_radio`  | 编辑单选显示 | 可编辑的单选状态   |
| `select`      | 选择框显示   | 下拉选择结果       |
| `celltag`     | 单元格标签   | 远程选择标签       |
| `entity`      | 实体显示     | 业务实体信息       |
| `action`      | 操作显示     | 操作按钮信息       |
| `group-title` | 分组标题     | 分组分隔           |

### 3. 数据格式支持

#### 3.1 简单数组格式（无分组）

```php
<?php
$detailFields = [
    [
        'field' => 'name',
        'title' => '姓名',
        'type' => 'text',
    ],
    [
        'field' => 'status',
        'title' => '状态',
        'type' => 'tag',
        'options' => [
            ['label' => '启用', 'value' => 1, 'color' => 'green'],
            ['label' => '禁用', 'value' => 0, 'color' => 'red'],
        ],
    ],
    [
        'field' => 'avatar',
        'title' => '头像',
        'type' => 'image',
    ],
];
```

#### 3.2 分组数组格式

```php
<?php
$groupedDetailFields = [
    [
        'label' => '基本信息',
        'dataItem' => [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'text',
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'text',
            ],
            [
                'field' => 'phone',
                'title' => '电话',
                'type' => 'text',
            ],
        ],
    ],
    [
        'label' => '状态信息',
        'dataItem' => [
            [
                'field' => 'status',
                'title' => '状态',
                'type' => 'tag',
                'options' => [
                    ['label' => '在职', 'value' => 1, 'color' => 'green'],
                    ['label' => '离职', 'value' => 0, 'color' => 'red'],
                ],
            ],
            [
                'field' => 'level',
                'title' => '等级',
                'type' => 'badge',
                'options' => [
                    ['label' => 'VIP', 'value' => 'vip', 'color' => 'gold'],
                    ['label' => '普通', 'value' => 'normal', 'color' => 'blue'],
                ],
            ],
        ],
    ],
];
```

#### 3.3 对象包装格式

```php
<?php
// 方式一：从 main 键提取
$wrappedData = [
    'main' => [
        [
            'label' => '基本信息',
            'dataItem' => [
                // 字段配置...
            ],
        ],
    ],
];

// 方式二：从 data 键提取
$wrappedData = [
    'data' => [
        [
            'label' => '基本信息',
            'dataItem' => [
                // 字段配置...
            ],
        ],
    ],
];

// 方式三：从 items 键提取
$wrappedData = [
    'items' => [
        [
            'label' => '基本信息',
            'dataItem' => [
                // 字段配置...
            ],
        ],
    ],
];
```

## 4. 详细组件配置示例

### 4.1 文本类型 (text)

```php
<?php
$textField = [
    'field' => 'description',
    'title' => '描述',
    'type' => 'text',
    'span' => 2,                        // 跨2列显示
    'contentStyle' => [
        'color' => '#666',
        'fontSize' => '14px',
    ],
];
```

### 4.2 标签类型 (tag)

```php
<?php
$tagField = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'tag',
    'options' => [
        // 数组格式
        ['label' => '启用', 'value' => 1, 'color' => 'green'],
        ['label' => '禁用', 'value' => 0, 'color' => 'red'],
        ['label' => '待审核', 'value' => 2, 'color' => 'orange'],
    ],
    // 或者映射格式
    'options' => [
        'mapping' => [
            '1' => ['label' => '启用', 'color' => 'green'],
            '0' => ['label' => '禁用', 'color' => 'red'],
            '2' => ['label' => '待审核', 'color' => 'orange'],
        ],
    ],
];
```

### 4.3 徽章类型 (badge)

```php
<?php
$badgeField = [
    'field' => 'message_count',
    'title' => '消息数量',
    'type' => 'badge',
    'options' => [
        'mapping' => [
            '0' => ['label' => '无消息', 'color' => 'default'],
            '1-10' => ['label' => '少量', 'color' => 'blue'],
            '11-50' => ['label' => '较多', 'color' => 'orange'],
            '50+' => ['label' => '很多', 'color' => 'red'],
        ],
    ],
];
```

### 4.4 日期类型 (date)

```php
<?php
$dateField = [
    'field' => 'created_at',
    'title' => '创建时间',
    'type' => 'date',
    'componentProps' => [
        'format' => 'YYYY-MM-DD HH:mm:ss',
        'showTime' => true,
    ],
];
```

### 4.5 图片类型 (image)

```php
<?php
$imageField = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'image',
    'componentProps' => [
        'width' => 80,
        'height' => 80,
        'preview' => true,              // 是否支持预览
        'fallback' => '/default-avatar.png', // 默认图片
    ],
];

// 对应的数据格式
$userData = [
    'avatar' => [
        'url' => '/uploads/avatar.jpg',
        'alt' => '用户头像',
        'thumbnail' => '/uploads/avatar_thumb.jpg',
        'width' => 200,
        'height' => 200,
    ],
    // 或简单字符串格式
    'avatar' => '/uploads/avatar.jpg',
];
```

### 4.6 文件类型 (file)

```php
<?php
$fileField = [
    'field' => 'attachments',
    'title' => '附件',
    'type' => 'file',
    'span' => 2,
];

// 对应的数据格式
$userData = [
    'attachments' => [
        [
            'name' => '合同文档.pdf',
            'size' => 1024000,
            'type' => 'application/pdf',
            'url' => '/uploads/contract.pdf',
            'icon' => 'file-pdf',
        ],
        [
            'name' => '图片.jpg',
            'size' => 512000,
            'type' => 'image/jpeg',
            'url' => '/uploads/image.jpg',
            'icon' => 'file-image',
        ],
    ],
];
```

### 4.7 链接类型 (link)

```php
<?php
$linkField = [
    'field' => 'website',
    'title' => '个人网站',
    'type' => 'link',
];

// 对应的数据格式
$userData = [
    'website' => [
        'href' => 'https://example.com',
        'text' => '访问网站',
        'target' => '_blank',
        'icon' => 'link',
    ],
    // 或简单字符串格式
    'website' => 'https://example.com',
];
```

### 4.8 评分类型 (rate)

```php
<?php
$rateField = [
    'field' => 'rating',
    'title' => '评分',
    'type' => 'rate',
    'componentProps' => [
        'count' => 5,                   // 总星数
        'allowHalf' => true,            // 允许半星
        'disabled' => true,             // 只读模式
    ],
];
```

### 4.9 人员类型 (person)

```php
<?php
$personField = [
    'field' => 'creator',
    'title' => '创建人',
    'type' => 'person',
    'options' => [
        'mapping' => [
            '1' => [
                'label' => '张三',
                'avatar' => '/avatars/zhangsan.jpg',
            ],
            '2' => [
                'label' => '李四',
                'avatar' => '/avatars/lisi.jpg',
            ],
        ],
    ],
];
```

### 4.10 部门类型 (dept)

```php
<?php
$deptField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'dept',
    'options' => [
        'mapping' => [
            '1' => ['label' => '技术部'],
            '11' => ['label' => '技术部/前端组'],
            '12' => ['label' => '技术部/后端组'],
            '2' => ['label' => '销售部'],
        ],
    ],
];
```

### 4.11 多选类型 (multiselect)

```php
<?php
$multiselectField = [
    'field' => 'skills',
    'title' => '技能',
    'type' => 'multiselect',
    'options' => [
        'mapping' => [
            'php' => ['label' => 'PHP', 'color' => 'blue'],
            'javascript' => ['label' => 'JavaScript', 'color' => 'yellow'],
            'python' => ['label' => 'Python', 'color' => 'green'],
            'java' => ['label' => 'Java', 'color' => 'red'],
        ],
    ],
];

// 对应的数据格式
$userData = [
    'skills' => ['php', 'javascript', 'python'], // 数组格式
    // 或字符串格式（逗号分隔）
    'skills' => 'php,javascript,python',
];
```

### 4.12 复选框类型 (checkbox)

```php
<?php
$checkboxField = [
    'field' => 'permissions',
    'title' => '权限',
    'type' => 'checkbox',
    'options' => [
        'mapping' => [
            'read' => ['label' => '查看', 'color' => 'blue'],
            'write' => ['label' => '编辑', 'color' => 'orange'],
            'delete' => ['label' => '删除', 'color' => 'red'],
        ],
    ],
];
```

### 4.13 单选类型 (radio)

```php
<?php
$radioField = [
    'field' => 'gender',
    'title' => '性别',
    'type' => 'radio',
    'options' => [
        'mapping' => [
            'male' => ['label' => '男', 'color' => 'blue'],
            'female' => ['label' => '女', 'color' => 'pink'],
            'other' => ['label' => '其他', 'color' => 'gray'],
        ],
    ],
];
```

### 4.14 评分类型 (rate)

```php
<?php
$rateField = [
    'field' => 'rating',
    'title' => '评分',
    'type' => 'rate',
    'componentProps' => [
        'count' => 5,                   // 总星数
        'allowHalf' => true,            // 允许半星
        'disabled' => true,             // 只读模式
    ],
];
```

## 5. 高级配置选项

### 5.1 跨列显示 (span)

```php
<?php
$spanField = [
    'field' => 'long_description',
    'title' => '详细描述',
    'type' => 'text',
    'span' => 3,                        // 跨3列显示
];
```

### 5.2 自定义样式

```php
<?php
$styledField = [
    'field' => 'important_note',
    'title' => '重要提示',
    'type' => 'text',
    'labelStyle' => [
        'color' => '#ff4d4f',
        'fontWeight' => 'bold',
    ],
    'contentStyle' => [
        'backgroundColor' => '#fff2f0',
        'padding' => '8px',
        'borderRadius' => '4px',
        'border' => '1px solid #ffccc7',
    ],
];
```

### 5.3 主题色配置

```php
<?php
$themedField = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'tag',
    'themePrefix' => 'success',         // 主题色前缀
    'options' => [
        'mapping' => [
            '1' => ['label' => '正常'],
            '0' => ['label' => '异常'],
        ],
    ],
];
```

### 5.4 条件显示

```php
<?php
$conditionalField = [
    'field' => 'admin_note',
    'title' => '管理员备注',
    'type' => 'text',
    'condition' => function($data) {
        // 只有管理员才能看到
        return isset($data['user_role']) && $data['user_role'] === 'admin';
    },
    // 或简单布尔值
    'condition' => true,
];
```

## 6. 选项配置详解

### 6.1 选项映射格式

```php
<?php
// 标准映射格式
$options = [
    'mapping' => [
        'key1' => [
            'label' => '显示文本',
            'color' => 'green',             // 颜色
            'icon' => 'check-circle',       // 图标
            'status' => 'success',          // 状态
        ],
        'key2' => [
            'label' => '另一个选项',
            'color' => 'red',
            'icon' => 'close-circle',
            'status' => 'error',
        ],
    ],
];

// 简化映射格式
$options = [
    'mapping' => [
        'key1' => ['label' => '选项1'],
        'key2' => ['label' => '选项2'],
    ],
];
```

### 6.2 选项数组格式

```php
<?php
$options = [
    ['label' => '选项1', 'value' => 'key1', 'color' => 'green'],
    ['label' => '选项2', 'value' => 'key2', 'color' => 'red'],
];
```

### 6.3 颜色配置

```php
<?php
// 在字段配置中直接指定颜色
$fieldWithColors = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'tag',
    'config' => [
        'options' => [
            ['label' => '启用', 'value' => 1],
            ['label' => '禁用', 'value' => 0],
        ],
        'optionsColor' => [
            '1' => 'green',
            '0' => 'red',
        ],
    ],
];
```

## 7. 完整的控制器示例

```php
<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Department;

class UserDetailController extends Controller
{
    /**
     * 获取用户详情配置
     */
    public function getUserDetail($id)
    {
        $user = User::with(['department', 'roles'])->findOrFail($id);

        // 构建详情字段配置
        $headerFormdatas = [
            [
                'label' => '基本信息',
                'dataItem' => [
                    [
                        'field' => 'id',
                        'title' => 'ID',
                        'type' => 'text',
                    ],
                    [
                        'field' => 'name',
                        'title' => '姓名',
                        'type' => 'text',
                    ],
                    [
                        'field' => 'email',
                        'title' => '邮箱',
                        'type' => 'text',
                    ],
                    [
                        'field' => 'phone',
                        'title' => '电话',
                        'type' => 'text',
                    ],
                    [
                        'field' => 'avatar',
                        'title' => '头像',
                        'type' => 'image',
                        'componentProps' => [
                            'width' => 80,
                            'height' => 80,
                            'preview' => true,
                        ],
                    ],
                    [
                        'field' => 'gender',
                        'title' => '性别',
                        'type' => 'tag',
                        'options' => [
                            'mapping' => [
                                'male' => ['label' => '男', 'color' => 'blue'],
                                'female' => ['label' => '女', 'color' => 'pink'],
                                'other' => ['label' => '其他', 'color' => 'gray'],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'label' => '工作信息',
                'dataItem' => [
                    [
                        'field' => 'department_id',
                        'title' => '部门',
                        'type' => 'dept',
                        'options' => [
                            'mapping' => $this->getDepartmentMapping(),
                        ],
                    ],
                    [
                        'field' => 'position',
                        'title' => '职位',
                        'type' => 'text',
                    ],
                    [
                        'field' => 'status',
                        'title' => '状态',
                        'type' => 'tag',
                        'options' => [
                            'mapping' => [
                                '1' => ['label' => '在职', 'color' => 'green'],
                                '0' => ['label' => '离职', 'color' => 'red'],
                                '2' => ['label' => '试用期', 'color' => 'orange'],
                            ],
                        ],
                    ],
                    [
                        'field' => 'hire_date',
                        'title' => '入职时间',
                        'type' => 'date',
                        'componentProps' => [
                            'format' => 'YYYY-MM-DD',
                        ],
                    ],
                    [
                        'field' => 'salary_level',
                        'title' => '薪资等级',
                        'type' => 'rate',
                        'componentProps' => [
                            'count' => 5,
                            'disabled' => true,
                        ],
                    ],
                ],
            ],
            [
                'label' => '权限信息',
                'dataItem' => [
                    [
                        'field' => 'roles',
                        'title' => '角色',
                        'type' => 'multiselect',
                        'options' => [
                            'mapping' => $this->getRoleMapping(),
                        ],
                    ],
                    [
                        'field' => 'permissions',
                        'title' => '权限',
                        'type' => 'checkbox',
                        'span' => 2,
                        'options' => [
                            'mapping' => [
                                'user.view' => ['label' => '查看用户', 'color' => 'blue'],
                                'user.edit' => ['label' => '编辑用户', 'color' => 'orange'],
                                'user.delete' => ['label' => '删除用户', 'color' => 'red'],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'label' => '附加信息',
                'dataItem' => [
                    [
                        'field' => 'resume',
                        'title' => '简历',
                        'type' => 'file',
                    ],
                    [
                        'field' => 'personal_website',
                        'title' => '个人网站',
                        'type' => 'link',
                    ],
                    [
                        'field' => 'bio',
                        'title' => '个人简介',
                        'type' => 'text',
                        'span' => 2,
                    ],
                    [
                        'field' => 'created_at',
                        'title' => '创建时间',
                        'type' => 'date',
                        'componentProps' => [
                            'format' => 'YYYY-MM-DD HH:mm:ss',
                        ],
                    ],
                    [
                        'field' => 'updated_at',
                        'title' => '更新时间',
                        'type' => 'date',
                        'componentProps' => [
                            'format' => 'YYYY-MM-DD HH:mm:ss',
                        ],
                    ],
                ],
            ],
        ];

        return response()->json([
            'headerFormdatas' => $headerFormdatas,
            'formdatas' => $user->toArray(),
            'tabs' => [], // 如果有标签页配置
        ]);
    }

    /**
     * 获取部门映射
     */
    private function getDepartmentMapping()
    {
        return Department::all()->mapWithKeys(function ($dept) {
            return [$dept->id => ['label' => $dept->name]];
        })->toArray();
    }

    /**
     * 获取角色映射
     */
    private function getRoleMapping()
    {
        return [
            'admin' => ['label' => '管理员', 'color' => 'red'],
            'manager' => ['label' => '经理', 'color' => 'orange'],
            'employee' => ['label' => '员工', 'color' => 'blue'],
            'guest' => ['label' => '访客', 'color' => 'gray'],
        ];
    }
}
```

## 8. 数据验证规则

### 8.1 必填字段验证

```php
<?php
function validateDescriptionRule($rule) {
    $required = ['field', 'title'];

    foreach ($required as $field) {
        if (!isset($rule[$field]) || empty($rule[$field])) {
            throw new InvalidArgumentException("Rule field '{$field}' is required");
        }
    }

    // 类型验证
    $validTypes = [
        'text', 'tag', 'badge', 'date', 'image', 'file', 'link',
        'rate', 'person', 'dept', 'multiselect', 'checkbox', 'radio', 'group-title'
    ];

    if (isset($rule['type']) && !in_array($rule['type'], $validTypes)) {
        throw new InvalidArgumentException("Invalid rule type: {$rule['type']}");
    }

    return true;
}

// 分组数据验证
function validateGroupedData($groupData) {
    if (!isset($groupData['label']) || empty($groupData['label'])) {
        throw new InvalidArgumentException("Group label is required");
    }

    if (!isset($groupData['dataItem']) || !is_array($groupData['dataItem'])) {
        throw new InvalidArgumentException("Group dataItem must be an array");
    }

    foreach ($groupData['dataItem'] as $rule) {
        validateDescriptionRule($rule);
    }

    return true;
}
```

## 9. 常见问题与解决方案

### 9.1 选项数据不显示

**问题**：配置了选项但标签不显示正确的文本

**解决方案**：

```php
<?php
// 确保选项格式正确
$options = [
    'mapping' => [
        '1' => ['label' => '启用'],  // 确保有 label 字段
        '0' => ['label' => '禁用'],
    ],
];

// 确保数据值与选项键匹配
$userData = [
    'status' => '1',  // 字符串类型
    // 或
    'status' => 1,    // 数字类型
];
```

### 9.2 图片不显示

**问题**：配置了图片类型但图片不显示

**解决方案**：

```php
<?php
// 确保图片数据格式正确
$userData = [
    // 对象格式（推荐）
    'avatar' => [
        'url' => '/uploads/avatar.jpg',
        'alt' => '用户头像',
    ],

    // 或简单字符串格式
    'avatar' => '/uploads/avatar.jpg',
];

// 字段配置
$imageField = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'image',
    'componentProps' => [
        'fallback' => '/default-avatar.png',  // 设置默认图片
    ],
];
```

### 9.3 文件列表不显示

**问题**：配置了文件类型但文件列表不显示

**解决方案**：

```php
<?php
// 确保文件数据格式正确
$userData = [
    'attachments' => [
        [
            'name' => '文档.pdf',      // 必填：文件名
            'url' => '/uploads/doc.pdf', // 必填：下载链接
            'size' => 1024000,         // 可选：文件大小
            'type' => 'application/pdf', // 可选：文件类型
        ],
    ],
];
```

### 9.4 分组不显示

**问题**：配置了分组但分组标题不显示

**解决方案**：

```php
<?php
// 确保分组格式正确
$headerFormdatas = [
    [
        'label' => '基本信息',    // 必填：分组标题
        'dataItem' => [          // 必填：字段数组
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'text',
            ],
        ],
    ],
];

// 确保不是空分组
foreach ($headerFormdatas as $group) {
    if (empty($group['dataItem'])) {
        // 移除空分组或添加字段
    }
}
```

### 9.5 跨列显示不生效

**问题**：设置了 span 但跨列显示不生效

**解决方案**：

```php
<?php
$spanField = [
    'field' => 'description',
    'title' => '描述',
    'type' => 'text',
    'span' => 2,  // 确保 span 是数字类型
];

// 注意：span 值不能超过表格的总列数
```

## 10. 性能优化建议

### 10.1 选项数据缓存

```php
<?php
class DetailOptionsCache
{
    public static function getDepartmentMapping()
    {
        return Cache::remember('detail_dept_mapping', 3600, function () {
            return Department::all()->mapWithKeys(function ($dept) {
                return [$dept->id => ['label' => $dept->name]];
            })->toArray();
        });
    }

    public static function getStatusMapping()
    {
        return Cache::remember('detail_status_mapping', 1800, function () {
            return [
                '1' => ['label' => '启用', 'color' => 'green'],
                '0' => ['label' => '禁用', 'color' => 'red'],
                '2' => ['label' => '待审核', 'color' => 'orange'],
            ];
        });
    }
}
```

### 10.2 数据预处理

```php
<?php
class DetailDataProcessor
{
    public static function processUserData($user)
    {
        // 预处理文件数据
        if (isset($user['attachments']) && is_string($user['attachments'])) {
            $user['attachments'] = json_decode($user['attachments'], true) ?: [];
        }

        // 预处理多选数据
        if (isset($user['skills']) && is_string($user['skills'])) {
            $user['skills'] = explode(',', $user['skills']);
        }

        // 预处理图片数据
        if (isset($user['avatar']) && is_string($user['avatar'])) {
            $user['avatar'] = [
                'url' => $user['avatar'],
                'alt' => $user['name'] . '的头像',
            ];
        }

        return $user;
    }
}
```

## 11. 总结

本文档提供了 `transformToGroupedDescriptions` 函数所需的完整数据格式规范，包括：

1. **基础字段配置**：支持 14 种不同的显示组件类型
2. **分组功能**：支持详情字段分组显示，提升信息组织性
3. **丰富的组件类型**：从基础文本到复杂的文件、图片、评分等组件
4. **灵活的选项配置**：支持多种选项格式和颜色配置
5. **高级功能**：跨列显示、自定义样式、条件显示等
6. **数据验证**：提供完整的数据格式验证规则
7. **性能优化**：提供缓存和数据预处理建议

### 关键要点

- **headerFormdatas** 是数组结构，包含多个分组对象
- 每个分组必须有 `label`（标题）和 `dataItem`（字段数组）
- 每个字段必须有 `field`（字段名）和 `title`（显示标题）
- 支持多种数据包装格式，函数会自动识别和提取
- 选项配置支持映射格式和数组格式
- 所有组件都支持自定义样式和属性配置

遵循本规范可以确保详情页面的数据展示效果，提供良好的用户体验，并保证系统的稳定性和可维护性。
