# API 标准规范

## 概述

为了确保 API 管理器能够正常工作，所有 API 模块都应该遵循统一的标准规范。

## 文件命名规范

### 目录结构

```
src/api/common/
├── archivesmanagement/
│   ├── customermanagement.ts
│   ├── suppliermanagement.ts
│   └── productmanagement.ts
├── systemmanagement/
│   ├── usermanagement.ts
│   ├── rolemanagement.ts
│   └── permissionmanagement.ts
└── ...
```

### 命名规则

- **目录名**: 使用小写字母，多个单词用驼峰命名
- **文件名**: 使用小写字母，多个单词用驼峰命名，以 `.ts` 结尾
- **路由匹配**: 文件路径应与前端路由路径一致

## 标准接口定义

### 基础接口

每个 API 模块必须实现以下标准接口：

```typescript
export interface StandardApiFunction {
  /** 列表查询 - 必须实现 */
  listAction: (params: ListParams) => Promise<ListResponse>;

  /** 创建 - 可选 */
  create?: (data: CreateData) => Promise<CreateResponse>;

  /** 更新 - 可选 */
  update?: (id: string | number, data: UpdateData) => Promise<UpdateResponse>;

  /** 删除 - 可选 */
  delete?: (id: string | number) => Promise<DeleteResponse>;

  /** 详情 - 可选 */
  detail?: (id: string | number) => Promise<DetailResponse>;

  /** 批量删除 - 可选 */
  batchDelete?: (ids: (string | number)[]) => Promise<BatchDeleteResponse>;

  /** 其他自定义方法 */
  [key: string]: any;
}
```

### 参数类型定义

```typescript
// 列表查询参数
interface ListParams {
  page?: number;
  pageSize?: number;
  tabIndex?: number;
  [key: string]: any; // 其他搜索条件
}

// 列表响应
interface ListResponse {
  nav: {
    columns: any[]; // 表格列配置
    form?: any[]; // 表单配置
    search_columns?: any[]; // 搜索配置
    rowSelection?: boolean; // 是否支持多选
    showCreateBtn?: boolean; // 是否显示新增按钮
    title?: string; // 页面标题
  };
  items: any[]; // 数据列表
  total: number; // 总数
}
```

## 标准实现模板

### 基础模板

```typescript
// src/api/common/example/demo.ts
import { requestClient } from '#/api/request';

/**
 * 列表查询 - 必须实现
 */
export async function listAction(params: any) {
  return requestClient.post('/api/demo/list', params);
}

/**
 * 创建
 */
export async function create(data: any) {
  return requestClient.post('/api/demo/create', data);
}

/**
 * 更新
 */
export async function update(id: string | number, data: any) {
  return requestClient.put(`/api/demo/${id}`, data);
}

/**
 * 删除
 */
export async function delete(id: string | number) {
  return requestClient.delete(`/api/demo/${id}`);
}

/**
 * 详情
 */
export async function detail(id: string | number) {
  return requestClient.get(`/api/demo/${id}`);
}

/**
 * 批量删除
 */
export async function batchDelete(ids: (string | number)[]) {
  return requestClient.post('/api/demo/batch-delete', { ids });
}
```

### 带类型定义的模板

```typescript
// src/api/common/example/typed-demo.ts
import { requestClient } from '#/api/request';

// 数据类型定义
interface DemoItem {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

interface DemoListParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: 'active' | 'inactive';
}

interface DemoCreateData {
  name: string;
  status: 'active' | 'inactive';
}

interface DemoUpdateData extends Partial<DemoCreateData> {}

interface DemoListResponse {
  nav: {
    columns: any[];
    form?: any[];
    search_columns?: any[];
    rowSelection?: boolean;
    showCreateBtn?: boolean;
    title?: string;
  };
  items: DemoItem[];
  total: number;
}

/**
 * 列表查询
 */
export async function listAction(params: DemoListParams): Promise<DemoListResponse> {
  return requestClient.post('/api/demo/list', params);
}

/**
 * 创建
 */
export async function create(data: DemoCreateData): Promise<{ id: string }> {
  return requestClient.post('/api/demo/create', data);
}

/**
 * 更新
 */
export async function update(id: string, data: DemoUpdateData): Promise<void> {
  return requestClient.put(`/api/demo/${id}`, data);
}

/**
 * 删除
 */
export async function delete(id: string): Promise<void> {
  return requestClient.delete(`/api/demo/${id}`);
}

/**
 * 详情
 */
export async function detail(id: string): Promise<DemoItem> {
  return requestClient.get(`/api/demo/${id}`);
}
```

## 响应数据规范

### listAction 响应格式

```typescript
{
  "nav": {
    "columns": [
      {
        "field": "name",
        "title": "名称",
        "width": 150
      },
      {
        "field": "status",
        "title": "状态",
        "width": 100
      }
    ],
    "form": [
      {
        "field": "name",
        "title": "名称",
        "type": "input",
        "required": true,
        "config": {
          "default": "",
          "placeholder": "请输入名称"
        }
      }
    ],
    "search_columns": [
      {
        "field": "name",
        "title": "名称",
        "type": "input"
      }
    ],
    "rowSelection": true,
    "showCreateBtn": true,
    "title": "示例管理"
  },
  "items": [
    {
      "id": "1",
      "name": "示例项目",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100
}
```

### 错误响应格式

```typescript
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

## 最佳实践

### 1. 错误处理

```typescript
export async function listAction(params: any) {
  try {
    return await requestClient.post('/api/demo/list', params);
  } catch (error) {
    console.error('列表查询失败:', error);
    throw error; // 重新抛出，让上层处理
  }
}
```

### 2. 参数验证

```typescript
export async function create(data: any) {
  if (!data.name) {
    throw new Error('名称不能为空');
  }

  return requestClient.post('/api/demo/create', data);
}
```

### 3. 数据转换

```typescript
export async function listAction(params: any) {
  // 转换前端参数为后端格式
  const backendParams = {
    pageNum: params.page,
    pageSize: params.pageSize,
    keyword: params.name,
  };

  const response = await requestClient.post('/api/demo/list', backendParams);

  // 转换后端响应为前端格式
  return {
    nav: response.config,
    items: response.data,
    total: response.total,
  };
}
```

### 4. 缓存处理

```typescript
// 对于不经常变化的数据，可以添加缓存
const cache = new Map();

export async function getOptions() {
  const cacheKey = 'demo-options';

  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }

  const result = await requestClient.get('/api/demo/options');
  cache.set(cacheKey, result);

  return result;
}
```

## 自定义方法规范

### 命名规范

- 使用动词开头，如 `export`, `import`, `sync`
- 使用驼峰命名法
- 方法名应该清晰表达功能

### 示例

```typescript
/**
 * 导出数据
 */
export async function exportData(params: any) {
  return requestClient.post('/api/demo/export', params);
}

/**
 * 导入数据
 */
export async function importData(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post('/api/demo/import', formData);
}

/**
 * 同步数据
 */
export async function syncData() {
  return requestClient.post('/api/demo/sync');
}

/**
 * 获取统计信息
 */
export async function getStatistics(params: any) {
  return requestClient.get('/api/demo/statistics', { params });
}
```

## 测试规范

### 单元测试示例

```typescript
// tests/api/demo.test.ts
import { describe, it, expect, vi } from 'vitest';
import { listAction, create } from '@/api/common/example/demo';

describe('Demo API', () => {
  it('should fetch list data', async () => {
    const params = { page: 1, pageSize: 20 };
    const result = await listAction(params);

    expect(result).toHaveProperty('nav');
    expect(result).toHaveProperty('items');
    expect(result).toHaveProperty('total');
  });

  it('should create item', async () => {
    const data = { name: 'Test Item', status: 'active' };
    const result = await create(data);

    expect(result).toHaveProperty('id');
  });
});
```

## 检查清单

在创建新的 API 模块时，请确保：

- [ ] 文件路径与路由路径匹配
- [ ] 实现了 `listAction` 方法
- [ ] 方法命名符合规范
- [ ] 添加了适当的类型定义
- [ ] 包含了错误处理
- [ ] 响应格式符合标准
- [ ] 添加了必要的注释
- [ ] 进行了基本测试

## 常见问题

### Q: 如何处理不同的响应格式？

A: 在 API 方法中进行数据转换，确保返回标准格式。

### Q: 如何添加自定义方法？

A: 直接在模块中导出新方法，API 管理器会自动识别。

### Q: 如何处理文件上传？

A: 使用 FormData 格式，参考导入数据的示例。

---

遵循这些规范将确保 API 管理器能够正常工作，并提供一致的开发体验。
