/**
 * 表单数据筛选工具
 * 用于过滤掉空值、undefined、null、空字符串、空数组等无效数据
 */

/**
 * 筛选有效的表单数据
 * @param data 原始表单数据
 * @param options 筛选选项
 * @param options.customFilter 自定义筛选函数
 * @param options.keepEmptyArray 是否保留空数组，默认 false
 * @param options.keepEmptyString 是否保留空字符串，默认 false
 * @param options.keepFalse 是否保留 false 值，默认 true
 * @param options.keepZero 是否保留 0 值，默认 true
 * @returns 筛选后的有效数据
 */
export function filterValidFormData(
  data: any,
  options: {
    /** 自定义筛选函数 */
    customFilter?: (key: string, value: any) => boolean;
    /** 是否保留空数组，默认 false */
    keepEmptyArray?: boolean;
    /** 是否保留空字符串，默认 false */
    keepEmptyString?: boolean;
    /** 是否保留 false 值，默认 true */
    keepFalse?: boolean;
    /** 是否保留 0 值，默认 true */
    keepZero?: boolean;
  } = {},
): Record<string, any> {
  const {
    keepEmptyString = false,
    keepEmptyArray = false,
    keepZero = true,
    keepFalse = true,
    customFilter,
  } = options;

  if (!data || typeof data !== 'object') {
    return {};
  }

  const filteredData: Record<string, any> = {};

  Object.keys(data).forEach((key) => {
    const value = data[key];

    // 自定义筛选函数优先
    if (customFilter && !customFilter(key, value)) {
      return;
    }

    // 基本的 null 和 undefined 检查
    if (value === null || value === undefined) {
      return;
    }

    // 空字符串检查
    if (!keepEmptyString && value === '') {
      return;
    }

    // 空数组检查
    if (!keepEmptyArray && Array.isArray(value) && value.length === 0) {
      return;
    }

    // 0 值检查
    if (!keepZero && value === 0) {
      return;
    }

    // false 值检查
    if (!keepFalse && value === false) {
      return;
    }

    // 通过所有检查，添加到结果中
    filteredData[key] = value;
  });

  return filteredData;
}

/**
 * 默认的表单数据筛选（最常用的配置）
 * 过滤掉 null、undefined、空字符串、空数组
 * 保留 0 和 false 值
 */
export function filterFormData(data: any): Record<string, any> {
  return filterValidFormData(data, {
    keepEmptyString: false,
    keepEmptyArray: false,
    keepZero: true,
    keepFalse: true,
  });
}

/**
 * 严格的表单数据筛选
 * 过滤掉所有"空值"，包括 0 和 false
 */
export function filterFormDataStrict(data: any): Record<string, any> {
  return filterValidFormData(data, {
    keepEmptyString: false,
    keepEmptyArray: false,
    keepZero: false,
    keepFalse: false,
  });
}

/**
 * 宽松的表单数据筛选
 * 只过滤掉 null 和 undefined
 */
export function filterFormDataLoose(data: any): Record<string, any> {
  return filterValidFormData(data, {
    keepEmptyString: true,
    keepEmptyArray: true,
    keepZero: true,
    keepFalse: true,
  });
}

/**
 * 搜索条件专用筛选
 * 适用于搜索表单，过滤掉无意义的搜索条件
 */
export function filterSearchData(data: any): Record<string, any> {
  return filterValidFormData(data, {
    keepEmptyString: false,
    keepEmptyArray: false,
    keepZero: true,
    keepFalse: true,
    customFilter: (key, value) => {
      // 特殊处理：如果是日期范围且只有一个值，也过滤掉
      if (Array.isArray(value) && value.length === 1 && !value[0]) {
        return false;
      }
      return true;
    },
  });
}

/**
 * 检查对象是否为空（经过筛选后）
 */
export function isEmptyFormData(data: any): boolean {
  const filtered = filterFormData(data);
  return Object.keys(filtered).length === 0;
}

/**
 * 获取有效字段的数量
 */
export function getValidFieldCount(data: any): number {
  const filtered = filterFormData(data);
  return Object.keys(filtered).length;
}

/**
 * 比较两个表单数据对象的有效字段是否相同
 */
export function compareFormData(data1: any, data2: any): boolean {
  const filtered1 = filterFormData(data1);
  const filtered2 = filterFormData(data2);

  const keys1 = Object.keys(filtered1).sort();
  const keys2 = Object.keys(filtered2).sort();

  if (keys1.length !== keys2.length) {
    return false;
  }

  return keys1.every((key, index) => {
    return key === keys2[index] && filtered1[key] === filtered2[key];
  });
}
