<script setup lang="ts">
/**
 * 详情描述组件
 *
 * 功能特性：
 * - 支持多种数据类型的渲染（文本、标签、日期、文件、图片等）
 * - 支持分组显示，自动处理后端数据结构
 * - 支持响应式布局和自定义样式
 * - 内置多种渲染器，支持扩展
 * - 支持条件显示和数据转换
 *
 * 使用场景：
 * - 数据详情展示
 * - 表单只读模式
 * - 信息概览页面
 */

import type { ComponentType, DescriptionRule } from './types';

import { computed } from 'vue';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

// 导入各种渲染器组件
import BadgeRenderer from './renderers/BadgeRenderer.vue';
import DateRenderer from './renderers/DateRenderer.vue';
import FileRenderer from './renderers/FileRenderer.vue';
import GroupTitleRenderer from './renderers/GroupTitleRenderer.vue';
import ImageRenderer from './renderers/ImageRenderer.vue';
import LinkRenderer from './renderers/LinkRenderer.vue';
import RateRenderer from './renderers/RateRenderer.vue';
import TagRenderer from './renderers/TagRenderer.vue';
import TextRenderer from './renderers/TextRenderer.vue';
// 导入数据转换工具函数
import { transformToGroupedDescriptions } from './types';

// 导出类型供外部使用
export type { ComponentType, DescriptionItem, DescriptionRule } from './types';

interface Props {
  /** 数据源 */
  data: Record<string, any>;

  // 新的使用方式：支持原始后端数据
  /** 原始后端字段配置数据 - 会自动转换为 rules */
  rawFields?: any[];
  /** 分组后端数据 - 会自动转换为 groupedDescriptions */
  groupedData?: any[];
  /** 已转换的分组描述数据 */
  groupedDescriptions?: Array<{ rules: DescriptionRule[]; title: string }>;

  // 传统使用方式：已转换的规则
  /** 显示规则 */
  rules?: DescriptionRule[];

  /** 标题 */
  title?: string;
  /** 是否显示边框 */
  bordered?: boolean;
  /** 一行的 DescriptionItems 数量，可以写成像素值或支持响应式的对象写法 */
  column?: number | Record<string, number>;
  /** 设置列表的大小 */
  size?: 'default' | 'middle' | 'small';
  /** 描述布局 */
  layout?: 'horizontal' | 'vertical';
  /** 配置 Descriptions.Item 的 colon 的默认值 */
  colon?: boolean;
  /** 自定义标签样式 */
  labelStyle?: Record<string, any>;
  /** 自定义内容样式 */
  contentStyle?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  bordered: false,
  column: 3,
  size: 'default',
  layout: 'horizontal',
  colon: true,
  labelStyle: () => ({}),
  contentStyle: () => ({}),
  rules: () => [],
  rawFields: () => [],
  groupedData: () => [],
  groupedDescriptions: () => [],
});

// ==================== 渲染器映射配置 ====================

/**
 * 组件类型到渲染器的映射
 * 根据字段类型自动选择合适的渲染器组件
 */
const componentMap: Record<ComponentType, any> = {
  // 基础类型
  text: TextRenderer, // 普通文本

  // 标签类型 - 都使用 TagRenderer 处理
  tag: TagRenderer, // 标签
  select: TagRenderer, // 下拉选择
  radio: TagRenderer, // 单选框
  checkbox: TagRenderer, // 复选框
  multiselect: TagRenderer, // 多选

  // 业务类型 - 使用 TagRenderer 显示
  dept: TagRenderer, // 部门
  person: TagRenderer, // 人员

  // 媒体类型
  image: ImageRenderer, // 图片
  file: FileRenderer, // 文件

  // 特殊类型
  rate: RateRenderer, // 评分
  date: DateRenderer, // 日期时间
  link: LinkRenderer, // 链接
  badge: BadgeRenderer, // 徽章
  'group-title': GroupTitleRenderer, // 分组标题
};

// ==================== 数据处理逻辑 ====================

/**
 * 内部分组描述数据处理
 *
 * 处理优先级：
 * 1. groupedDescriptions - 已转换的分组数据（直接使用）
 * 2. rawFields - 原始字段数据（转换处理）
 * 3. groupedData - 后端分组数据（转换处理）
 * 4. rules - 手动配置的规则（包装为单个分组）
 */
const internalGroupedDescriptions = computed<
  Array<{ rules: DescriptionRule[]; title: string }>
>(() => {
  // 优先级1: 使用已转换的分组描述数据
  if (props.groupedDescriptions && props.groupedDescriptions.length > 0) {
    return props.groupedDescriptions;
  }

  // 优先级2: 转换原始字段数据
  if (props.rawFields && props.rawFields.length > 0) {
    const groupedDescriptions = transformToGroupedDescriptions(props.rawFields);
    return groupedDescriptions;
  }

  // 优先级3: 转换后端分组数据
  if (props.groupedData && props.groupedData.length > 0) {
    const groupedDescriptions = transformToGroupedDescriptions(
      props.groupedData,
    );
    return groupedDescriptions;
  }

  // 优先级4: 包装手动配置的规则为单个分组
  if (props.rules && props.rules.length > 0) {
    return [
      {
        title: props.title || '详情信息',
        rules: props.rules,
      },
    ];
  }

  // 没有可用的数据，返回空分组
  return [];
});

/**
 * 显示模式控制
 * 统一使用分组显示模式，简化逻辑
 */
const useGroupedDisplay = computed(() => {
  const groups = internalGroupedDescriptions.value;
  return groups.length > 0;
});

// ==================== 工具函数 ====================

/**
 * 根据路径获取对象值
 * 支持嵌套路径如 'user.profile.name'
 *
 * @param obj 目标对象
 * @param path 属性路径，用点分隔
 * @returns 属性值或 undefined
 */
function getValueByPath(obj: any, path: string): any {
  if (!obj || !path) return undefined;

  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[key];
  }

  return current;
}

/**
 * 获取主题色值
 * 支持预定义主题色和自定义颜色值
 *
 * @param type 主题色类型或自定义颜色值
 * @returns CSS 颜色值
 */
function getThemeColor(type: string): string {
  const themeColors: Record<string, string> = {
    primary: 'var(--ant-primary-color, #1890ff)',
    success: 'var(--ant-success-color, #52c41a)',
    warning: 'var(--ant-warning-color, #faad14)',
    error: 'var(--ant-error-color, #ff4d4f)',
    info: 'var(--ant-info-color, #1890ff)',
  };

  // 如果是预定义主题色，返回对应的 CSS 变量
  // 否则直接返回传入的值（可能是自定义颜色）
  return themeColors[type] || type;
}
</script>

<template>
  <!-- ==================== 分组显示模式 ==================== -->
  <div v-if="useGroupedDisplay" class="grouped-descriptions">
    <template
      v-for="(group, index) in internalGroupedDescriptions"
      :key="index"
    >
      <!-- 分组标题 - 仅在有标题时显示 -->
      <div v-if="group.title" class="group-title">
        {{ group.title }}
      </div>

      <!-- 分组内容 - 使用 Ant Design 的 Descriptions 组件 -->
      <Descriptions
        :bordered="bordered"
        :column="column"
        :size="size"
        :layout="layout"
        :colon="colon"
        :label-style="labelStyle"
        :content-style="contentStyle"
        v-bind="$attrs"
      >
        <!-- 字段项渲染 -->
        <DescriptionsItem
          v-for="rule in group.rules"
          :key="rule.field"
          :label="rule.label"
          :span="rule.span"
          :label-style="rule.labelStyle"
          :content-style="rule.contentStyle"
        >
          <!-- 主题色前缀指示器 -->
          <span
            v-if="rule.themePrefix"
            class="theme-prefix"
            :style="{ backgroundColor: getThemeColor(rule.themePrefix) }"
          ></span>

          <!-- 动态渲染器 - 根据字段类型选择合适的渲染组件 -->
          <component
            :is="componentMap[rule.type || 'text']"
            :value="getValueByPath(data, rule.field)"
            :raw-value="getValueByPath(data, rule.field)"
            :options="rule.options"
            :theme-prefix="rule.themePrefix"
            :component-props="rule.componentProps"
          />
        </DescriptionsItem>
      </Descriptions>
    </template>
  </div>

  <!-- ==================== 空状态显示 ==================== -->
  <div v-else class="empty-state">
    <span>暂无数据</span>
  </div>
</template>

<style scoped>
/* ==================== 主题色前缀样式 ==================== */

/** 主题色前缀指示器 - 显示在字段值前的彩色条 */
.theme-prefix {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  border-radius: 2px;
}

/* ==================== 分组容器样式 ==================== */

/** 分组描述容器 */
.grouped-descriptions {
  /* 分组容器的基础样式，可根据需要扩展 */
}

/** 分组标题样式 - 带主题色的标题栏 */
.group-title {
  box-sizing: border-box;
  display: inline-block;
  min-width: 100%;
  padding: 8px 12px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: bold;
  color: hsl(var(--primary));
  background: linear-gradient(
    90deg,
    hsl(var(--primary) / 10%) 0%,
    hsl(var(--primary) / 5%) 50%,
    transparent 100%
  );
  border-left: 4px solid hsl(var(--primary));
  border-radius: 4px;
}

/** 分组标题后的描述组件间距 */
.group-title + .ant-descriptions {
  margin-bottom: 24px;
}

/* ==================== 空状态样式 ==================== */

/** 空状态显示样式 */
.empty-state {
  padding: 20px;
  font-size: 14px;
  color: #999;
  text-align: center;
}
</style>
