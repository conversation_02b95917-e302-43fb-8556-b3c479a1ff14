# DateRange 字段映射功能

## 功能说明

`dateRange` 类型的字段现在支持字段映射功能，可以将一个日期范围选择器的值自动映射到多个独立的字段中。这对于需要将日期范围拆分成开始时间和结束时间字段的场景非常有用。

## 配置格式

在 `dateRange` 类型字段的 `config` 中添加 `fieldMappingTime` 配置：

```typescript
{
  field: 'createTime',
  type: 'dateRange',
  title: '创建时间',
  config: {
    fieldMappingTime: [
      [
        'createTime',                           // 源字段名
        ['startTime', 'endTime'],              // 目标字段数组
        ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']  // 格式化数组
      ]
    ]
  }
}
```

## 配置参数说明

### fieldMappingTime

- **类型**: `Array<[string, string[], string[]]>`
- **说明**: 字段映射配置数组，支持多个映射规则

### 映射规则格式

每个映射规则是一个包含三个元素的数组：

1. **源字段名** (`string`)
   - 当前日期范围字段的字段名
   - 通常与外层的 `field` 相同

2. **目标字段数组** (`string[]`)
   - 要映射到的目标字段名数组
   - 第一个元素对应开始日期字段
   - 第二个元素对应结束日期字段

3. **格式化数组** (`string[]`)
   - 对应目标字段的日期格式化字符串
   - 第一个元素对应开始日期的格式
   - 第二个元素对应结束日期的格式
   - 默认格式: `['YYYY-MM-DD', 'YYYY-MM-DD']`

## 使用示例

### 基础示例

```typescript
{
  field: 'orderTime',
  type: 'dateRange',
  title: '订单时间',
  config: {
    fieldMappingTime: [
      [
        'orderTime',
        ['orderStartTime', 'orderEndTime'],
        ['YYYY-MM-DD', 'YYYY-MM-DD']
      ]
    ]
  }
}
```

### 多个映射规则

```typescript
{
  field: 'businessTime',
  type: 'dateRange',
  title: '业务时间',
  config: {
    fieldMappingTime: [
      [
        'businessTime',
        ['businessStartDate', 'businessEndDate'],
        ['YYYY-MM-DD', 'YYYY-MM-DD']
      ],
      [
        'businessTime',
        ['businessStartDateTime', 'businessEndDateTime'],
        ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']
      ]
    ]
  }
}
```

### 不同格式示例

```typescript
{
  field: 'reportTime',
  type: 'dateRange',
  title: '报告时间',
  config: {
    fieldMappingTime: [
      [
        'reportTime',
        ['reportStartTime', 'reportEndTime'],
        ['YYYY/MM/DD HH:mm', 'YYYY/MM/DD HH:mm']
      ]
    ]
  }
}
```

## 工作原理

1. **配置解析**: 转换方法会解析 `fieldMappingTime` 配置，为每个映射规则创建处理逻辑

2. **onChange 处理**: 当用户选择日期范围时，会触发自定义的 `onChange` 处理函数

3. **字段映射**: 处理函数会根据配置将日期范围值格式化并设置到对应的目标字段

4. **动态更新**: 目标字段会实时更新，支持表单的动态写入需求

## 注意事项

1. **字段顺序**: 目标字段数组中的顺序很重要，第一个对应开始日期，第二个对应结束日期

2. **格式化**: 确保格式化字符串与目标字段的预期格式一致

3. **字段存在**: 目标字段必须在表单schema中存在，否则映射会失败

4. **清空处理**: 当日期范围被清空时，对应的目标字段也会被清空

## 扩展性

该功能设计为可扩展的，未来可以支持：

- 更多的日期格式
- 自定义映射函数
- 条件映射规则
- 其他类型字段的映射
