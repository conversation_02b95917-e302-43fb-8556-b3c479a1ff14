/**
 * 跟进组件类型定义
 */

/** 附件信息 */
export interface FollowUpAttachment {
  /** 文件名 */
  name: string;
  /** 文件URL */
  url: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type?: string;
}

/** 跟进记录 */
export interface FollowUpRecord {
  /** 记录ID */
  id?: string;
  /** 跟进内容 */
  content: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建人 */
  creator?: string;
  /** 创建人ID */
  creatorId?: string;
  /** 状态 */
  status?: 'cancelled' | 'completed' | 'pending' | 'processing';
  /** 附件列表 */
  attachments?: FollowUpAttachment[];
  /** 跟进类型 */
  type?: string;
  /** 优先级 */
  priority?: 'high' | 'low' | 'medium' | 'urgent';
  /** 标签 */
  tags?: string[];
  /** 关联的业务ID */
  businessId?: string;
  /** 关联的业务类型 */
  businessType?: string;
  /** 备注 */
  remark?: string;
}

/** 跟进组件属性 */
export interface FollowUpProps {
  /** 跟进记录数据 */
  data?: FollowUpRecord[];
  /** 是否可以添加跟进 */
  canAdd?: boolean;
  /** 是否显示时间线模式 */
  timelineMode?: boolean;
  /** 标题 */
  title?: string;
  /** 是否显示状态筛选 */
  showStatusFilter?: boolean;
  /** 是否显示类型筛选 */
  showTypeFilter?: boolean;
  /** 每页显示数量 */
  pageSize?: number;
  /** 是否显示分页 */
  showPagination?: boolean;
}

/** 跟进组件事件 */
export interface FollowUpEvents {
  /** 添加跟进记录 */
  add: (record: Partial<FollowUpRecord>) => void;
  /** 查看跟进详情 */
  view: (record: FollowUpRecord) => void;
  /** 编辑跟进记录 */
  edit: (record: FollowUpRecord) => void;
  /** 删除跟进记录 */
  delete: (record: FollowUpRecord) => void;
  /** 刷新数据 */
  refresh: () => void;
  /** 状态变更 */
  statusChange: (record: FollowUpRecord, newStatus: string) => void;
}

/** 跟进筛选条件 */
export interface FollowUpFilter {
  /** 状态筛选 */
  status?: string[];
  /** 类型筛选 */
  type?: string[];
  /** 创建人筛选 */
  creator?: string[];
  /** 时间范围筛选 */
  timeRange?: [string, string];
  /** 关键词搜索 */
  keyword?: string;
}

/** 跟进统计信息 */
export interface FollowUpStats {
  /** 总数 */
  total: number;
  /** 待处理数量 */
  pending: number;
  /** 处理中数量 */
  processing: number;
  /** 已完成数量 */
  completed: number;
  /** 已取消数量 */
  cancelled: number;
}
