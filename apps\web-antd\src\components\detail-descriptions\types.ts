/**
 * 详情描述组件类型定义
 *
 * 本文件包含：
 * - 组件类型枚举和接口定义
 * - 数据转换工具函数
 * - 后端数据格式处理逻辑
 *
 * 主要功能：
 * - 支持多种渲染器类型
 * - 统一的选项配置处理
 * - 后端数据到前端组件的转换
 */

import {
  convertToDetailOptions,
  extractOptionsConfig,
  normalizeOptions,
} from '#/utils/options';

// ==================== 基础类型定义 ====================

/**
 * 支持的组件渲染类型
 * 每种类型对应一个专门的渲染器组件
 */
export type ComponentType =
  // 基础类型
  | 'badge' // 徽章

  // 选择类型 - 都使用 TagRenderer
  | 'checkbox' // 复选框
  | 'date' // 日期时间
  | 'dept' // 部门信息
  | 'file' // 文件/附件
  | 'group-title' // 分组标题

  // 业务类型 - 使用 TagRenderer 显示
  | 'image' // 图片显示
  | 'link' // 链接

  // 媒体类型
  | 'multiselect' // 多选
  | 'person' // 人员信息

  // 特殊类型
  | 'radio' // 单选框
  | 'rate' // 星级评分
  | 'select' // 下拉选择
  | 'tag' // 标签显示
  | 'text'; // 普通文本 - 默认渲染器

// ==================== 核心接口定义 ====================

/**
 * 描述字段规则配置
 * 定义单个字段的显示方式和行为
 */
export interface DescriptionRule {
  /** 字段名 - 支持嵌套路径如 'user.profile.name' */
  field: string;

  /** 显示标签 - 字段的显示名称 */
  label: string;

  /** 组件类型 - 决定使用哪个渲染器，默认为 'text' */
  type?: ComponentType;

  /** 跨列数 - 在描述列表中占用的列数 */
  span?: number;

  /** 选项配置 - 用于选择类型字段的选项数据 */
  options?: OptionConfig;

  /** 数据转换函数 - 在显示前对数据进行处理 */
  transform?: (value: any, data: Record<string, any>) => any;

  /** 显示条件 - 控制字段是否显示 */
  condition?: ((data: Record<string, any>) => boolean) | boolean;

  /** 主题色前缀 - 在字段值前显示的彩色指示器 */
  themePrefix?: 'error' | 'info' | 'primary' | 'success' | 'warning' | string;

  /** 自定义标签样式 - 应用到标签的 CSS 样式 */
  labelStyle?: Record<string, any>;

  /** 自定义内容样式 - 应用到内容的 CSS 样式 */
  contentStyle?: Record<string, any>;

  /** 组件属性 - 传递给渲染器组件的额外属性 */
  componentProps?: Record<string, any>;
}

/** 选项配置 */
export interface OptionConfig {
  /** 选项映射 */
  mapping?: Record<number | string, OptionItem>;
  /** 选项数组 */
  list?: OptionItem[];
  /** 默认选项 */
  default?: OptionItem;
}

/** 选项项 */
export interface OptionItem {
  /** 显示文本 */
  label: string;
  /** 值 */
  value: number | string;
  /** 颜色 */
  color?: string;
  /** 图标 */
  icon?: string;
  /** 状态 */
  status?: 'default' | 'error' | 'processing' | 'success' | 'warning';
}

/** 处理后的描述项 */
export interface DescriptionItem {
  /** 唯一键 */
  key: string;
  /** 显示标签 */
  label: string;
  /** 处理后的值 */
  value: any;
  /** 原始值 */
  rawValue: any;
  /** 组件类型 */
  type: ComponentType;
  /** 跨列数 */
  span?: number;
  /** 选项配置 */
  options?: OptionConfig;
  /** 主题色前缀 */
  themePrefix?: string;
  /** 自定义标签样式 */
  labelStyle?: Record<string, any>;
  /** 自定义内容样式 */
  contentStyle?: Record<string, any>;
  /** 组件属性 */
  componentProps?: Record<string, any>;
}

/** 文件信息 */
export interface FileInfo {
  /** 文件名 */
  name: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type?: string;
  /** 下载链接 */
  url: string;
  /** 图标 */
  icon?: string;
}

/** 图片信息 */
export interface ImageInfo {
  /** 图片链接 */
  url: string;
  /** 替代文本 */
  alt?: string;
  /** 缩略图链接 */
  thumbnail?: string;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
}

/** 链接信息 */
export interface LinkInfo {
  /** 链接地址 */
  href: string;
  /** 显示文本 */
  text: string;
  /** 是否在新窗口打开 */
  target?: '_blank' | '_self';
  /** 图标 */
  icon?: string;
}

/**
 * 将分组数据转换为多个独立的 Descriptions 组件配置
 */
export function transformToGroupedDescriptions(
  groupsData: any,
): Array<{ rules: DescriptionRule[]; title: string }> {
  // transformToGroupedDescriptions 输入数据处理

  // 处理不同的数据结构
  let dataArray: any[] = [];

  if (Array.isArray(groupsData)) {
    // 检查数组中的元素结构
    if (groupsData.length > 0) {
      const firstItem = groupsData[0];

      // 如果数组元素包含 label 和 dataItem，说明是分组格式
      dataArray =
        firstItem && typeof firstItem === 'object' && 'dataItem' in firstItem
          ? groupsData
          : [
              {
                label: '详情信息',
                dataItem: groupsData,
              },
            ];
    }
  } else if (groupsData && typeof groupsData === 'object') {
    // 如果是单个分组对象（包含 label 和 dataItem）
    if ('label' in groupsData && 'dataItem' in groupsData) {
      // 检测到单个分组对象，包装为数组
      dataArray = [groupsData];
    } else if (Array.isArray(groupsData.main)) {
      dataArray = groupsData.main;
    } else if (Array.isArray(groupsData.data)) {
      dataArray = groupsData.data;
    } else if (Array.isArray(groupsData.items)) {
      dataArray = groupsData.items;
    } else {
      // 如果对象的值是数组，取第一个数组值
      const arrayValues = Object.values(groupsData).filter((value) =>
        Array.isArray(value),
      );
      if (arrayValues.length > 0) {
        dataArray = arrayValues[0] as any[];
      }
    }
  }

  // 提取的数据数组

  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    // transformToGroupedDescriptions: 无法提取有效的数组数据
    return [];
  }

  const result = dataArray
    .filter((group: any) => {
      // 过滤掉空分组
      const isValid =
        group.dataItem &&
        Array.isArray(group.dataItem) &&
        group.dataItem.length > 0;
      // 过滤分组
      return isValid;
    })
    .map((group: any) => {
      // 处理分组

      const rules = group.dataItem
        .filter((item: any) => {
          // 过滤掉无效的字段项（必须有 field 和 title）
          const isValid = item && item.field && item.title;
          // 过滤字段
          return isValid;
        })
        .map((item: any) => {
          // 处理字段

          // 处理选项配置
          let options;
          if (item.config?.options || item.options) {
            const optionsConfig = extractOptionsConfig(item);
            // 提取的选项配置

            const standardOptions = normalizeOptions(optionsConfig.options, {
              fieldName: item.field,
              fieldType: item.type,
              componentName: 'DetailDescriptions',
            });
            // 标准化选项

            // 应用颜色配置到选项
            if (optionsConfig.optionsColor) {
              standardOptions.forEach((option) => {
                if (!option.color && optionsConfig.optionsColor) {
                  // 尝试多种键格式匹配颜色
                  const color =
                    optionsConfig.optionsColor[option.value] ||
                    optionsConfig.optionsColor[String(option.value)] ||
                    optionsConfig.optionsColor[Number(option.value)];
                  if (color) {
                    option.color = color;
                  }
                }
              });
              // 应用颜色后的选项
            }

            options = convertToDetailOptions(standardOptions);
            // 转换后的详情选项
          }

          const rule = {
            field: item.field,
            label: item.title,
            type: getDisplayType(item.type),
            options,
            transform: item.transform,
            componentProps: item.componentProps,
            span: item.span,
          };

          // 生成的规则
          return rule;
        });

      return {
        title: group.label || '',
        rules,
      };
    });

  // transformToGroupedDescriptions 输出结果
  return result;
}

/**
 * 根据字段类型确定显示类型
 */
function getDisplayType(type: string): any {
  const typeMapping: Record<string, string> = {
    // 基础文本类型
    text: 'text',
    input: 'text',

    // 标签类型
    select: 'tag',
    radio: 'tag', // 单选框类型使用 tag 显示
    checkbox: 'tag', // 复选框类型使用 tag 显示
    tag: 'tag', // 添加 tag 类型映射
    multiselect: 'tag', // 多选也使用 tag 显示

    // 列表页面特有的类型
    text_tag: 'tag', // 文本标签列
    edit_radio: 'tag', // 编辑单选列
    celltag: 'tag', // 单元格标签列

    // 日期时间类型
    date: 'date',
    datetime: 'date',

    // 媒体类型
    image: 'image',
    file: 'file',
    files: 'file', // 兼容列表页面的 files 类型

    // 交互类型
    rate: 'rate',
    link: 'link',
    badge: 'badge',

    // 业务类型
    person: 'person', // 改为使用专门的 person 类型
    dept: 'dept', // 改为使用专门的 dept 类型
    entity: 'tag', // 实体类型也使用 tag 显示

    // 操作类型
    action: 'text', // 操作列在详情页面显示为文本
  };

  return typeMapping[type] || 'text';
}
