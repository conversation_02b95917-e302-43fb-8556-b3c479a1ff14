# GeneralTabs 通用标签页组件

基于 Ant Design Vue 4 的 Tabs 组件封装的通用标签页组件，支持动态配置、组件映射、懒加载等功能。

## 功能特性

- ✅ **动态配置**: 根据传入数据动态生成 Tab 项
- ✅ **组件映射**: 支持字符串组件名映射到实际组件
- ✅ **懒加载**: 支持 Tab 内容懒加载，提升性能
- ✅ **预设配置**: 提供常用组件的预设配置
- ✅ **条件显示**: 支持根据条件动态显示/隐藏 Tab
- ✅ **插槽支持**: 支持自定义 Tab 标题和内容
- ✅ **事件处理**: 完整的事件支持（切换、点击、编辑等）

## 基础用法

```vue
<template>
  <GeneralTabs
    :items="tabItems"
    :component-map="componentMap"
    :common-data="sharedData"
    @change="handleTabChange"
  />
</template>

<script setup>
import GeneralTabs from '@/components/general-tabs';

const tabItems = [
  {
    key: 'info',
    label: '基本信息',
    component: 'UserInfo',
    props: { userId: 123 },
  },
  {
    key: 'settings',
    label: '设置',
    component: 'UserSettings',
    disabled: false,
  },
];

const componentMap = {
  UserInfo: UserInfoComponent,
  UserSettings: UserSettingsComponent,
};

const sharedData = {
  currentUser: { id: 123, name: '张三' },
};

function handleTabChange(key) {
  console.log('切换到:', key);
}
</script>
```

## 预设配置

使用预设配置快速创建常用 Tab：

```vue
<script setup>
import { COMMON_PRESETS, createPresetTabs } from '@/components/general-tabs';

const tabs = createPresetTabs([
  COMMON_PRESETS.detailInfo({
    title: '用户详情',
    data: userData,
  }),
  COMMON_PRESETS.dataTable({
    dataSource: tableData,
    columns: tableColumns,
  }),
  COMMON_PRESETS.chartView({
    height: 400,
    data: chartData,
  }),
]);
</script>
```

## 动态生成

根据数据自动推荐并生成 Tab：

```vue
<script setup>
import { createDynamicTabs } from '@/components/general-tabs';

// 自动根据数据类型推荐 Tab 配置
const dynamicTabs = createDynamicTabs(responseData, [
  { label: '自定义Tab', type: 'custom' },
]);
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| items | Tab 项配置列表 | `TabItem[]` | `[]` |
| componentMap | 组件映射表 | `ComponentMap` | `{}` |
| commonData | 传递给所有组件的公共数据 | `Record<string, any>` | `{}` |
| lazyLoad | 是否懒加载 Tab 内容 | `boolean` | `true` |
| activeKey | 当前激活的 Tab | `string` | - |
| defaultActiveKey | 默认激活的 Tab | `string` | - |
| tabPosition | Tab 位置 | `'top' \| 'right' \| 'bottom' \| 'left'` | `'top'` |
| type | Tab 类型 | `'line' \| 'card' \| 'editable-card'` | `'line'` |
| size | Tab 大小 | `'large' \| 'default' \| 'small'` | `'default'` |
| centered | 是否居中显示 | `boolean` | `false` |
| bordered | 是否显示边框 | `boolean` | `true` |

### TabItem

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| key | Tab 唯一标识 | `string` | - |
| label | Tab 标题 | `string` | - |
| component | 要渲染的组件 | `Component \| string` | - |
| props | 传递给组件的 props | `Record<string, any>` | `{}` |
| disabled | 是否禁用 | `boolean` | `false` |
| closable | 是否可关闭 | `boolean` | `false` |
| forceRender | 是否强制渲染 | `boolean` | `false` |
| icon | 自定义图标 | `Component \| string` | - |
| condition | 条件显示 | `boolean \| ((data?: any) => boolean)` | `true` |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| change | Tab 切换时触发 | `(activeKey: string) => void` |
| edit | Tab 编辑时触发 | `(targetKey: string, action: 'add' \| 'remove') => void` |
| tabClick | Tab 点击时触发 | `(key: string, event: MouseEvent) => void` |
| tabScroll | Tab 滚动时触发 | `(direction: 'left' \| 'right') => void` |

### 插槽

| 插槽名       | 说明            | 参数                           |
| ------------ | --------------- | ------------------------------ |
| `${key}-tab` | 自定义 Tab 标题 | `{ item: TabItem }`            |
| `${key}`     | 自定义 Tab 内容 | `{ item: TabItem, data: any }` |

## 预设组件类型

- `table` - 数据表格
- `form` - 表单组件
- `descriptions` - 详情描述
- `chart` - 图表组件
- `list` - 列表组件
- `tree` - 树形组件
- `custom` - 自定义组件

## 高级用法

### 条件显示

```vue
<script setup>
const tabs = [
  {
    key: 'admin',
    label: '管理员功能',
    component: 'AdminPanel',
    condition: (data) => data.user.role === 'admin',
  },
];
</script>
```

### 自定义插槽

```vue
<template>
  <GeneralTabs :items="tabs">
    <!-- 自定义 Tab 标题 -->
    <template #info-tab="{ item }">
      <UserOutlined />
      {{ item.label }}
    </template>

    <!-- 自定义 Tab 内容 -->
    <template #settings="{ data }">
      <CustomSettingsPanel :user="data.user" />
    </template>
  </GeneralTabs>
</template>
```

### 懒加载控制

```vue
<script setup>
// 关闭懒加载，所有 Tab 内容立即渲染
const lazyLoad = false;

// 或者通过 forceRender 强制渲染特定 Tab
const tabs = [
  {
    key: 'important',
    label: '重要内容',
    component: 'ImportantComponent',
    forceRender: true, // 立即渲染，不受懒加载影响
  },
];
</script>
```

## 注意事项

1. 使用字符串组件名时，需要在 `componentMap` 中注册对应组件
2. 懒加载模式下，只有激活过的 Tab 才会渲染内容
3. `commonData` 会自动合并到每个组件的 props 中
4. 条件显示的 Tab 在条件不满足时会被完全隐藏
