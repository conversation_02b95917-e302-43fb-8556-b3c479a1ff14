# transformBackendSearchToSchema PHP 后端数据规范文档

## 概述

本文档为 PHP 后端开发者提供 `transformBackendSearchToSchema` 函数所需的数据格式规范。该函数负责将后端返回的表单字段配置转换为前端 Vben 表单系统所需的 Schema 格式，支持复杂的表单组件、字段联动、API 数据源等高级功能。

## ⚠️ 重要更新：分组数据的特殊用途

**关键信息**：从最新版本开始，分组数据的第二组具有特殊用途：

- **第一组数据**：用于列表页面的搜索条件表单
- **第二组数据**：**专门用于编辑新增页面的表格展示**
- **第三组及以后**：可用于其他特殊用途或高级配置

在设计 PHP 后端接口时，请确保将编辑新增页面需要的表单字段放在第二组中。

## 新增字段类型：edittable

`edittable` 类型是一个特殊的插槽组件类型，允许在表单中嵌入自定义内容（如表格、图表等）。

### 数据格式规范

```php
[
    'field' => 'order_details',        // 字段名，将作为插槽名称
    'type' => 'edittable',             // 类型必须为 'edittable'
    'title' => '订单明细',             // 字段标题
    'config' => [
        'columns' => [                  // 表格列配置（可选）
            [
                'field' => 'product_name',
                'title' => '商品名称',
                'type' => 'text',
                'width' => 200
            ],
            [
                'field' => 'quantity',
                'title' => '数量',
                'type' => 'number',
                'width' => 100
            ]
            // ... 更多列配置
        ],
        'tabList' => [                  // 表格数据（可选）
            [
                'id' => 1,
                'product_name' => '商品A',
                'quantity' => 2
            ]
            // ... 更多数据
        ],
        'height' => '400px',            // 表格高度（可选）
        'editable' => true,             // 是否可编辑（可选）
        // ... 其他自定义配置都会传递到前端
    ]
]
```

### 重要说明

1. **插槽名称**：`field` 字段的值将直接作为前端插槽的名称
2. **配置传递**：`config` 中的所有配置都会作为 `params` 传递到前端插槽
3. **灵活性**：可以在 `config` 中添加任何自定义配置，前端都能接收到
4. **多个插槽**：可以在同一个表单中定义多个 `edittable` 字段
5. **操作列控制**：操作列完全由后端配置控制，前端不会自动添加

### 操作列配置（可选）

如果需要在表格中显示操作列，请在 `columns` 配置中包含操作列定义：

```php
'columns' => [
    // ... 其他列配置
    [
        'title' => '操作',
        'dataIndex' => 'actions',
        'key' => 'actions',
        'width' => 150,
        'fixed' => 'right',
        'editable' => false,
        'type' => 'actions',
        'actions' => [
            [
                'type' => 'edit',
                'title' => '编辑',
                'icon' => 'EditOutlined'
            ],
            [
                'type' => 'delete',
                'title' => '删除',
                'icon' => 'DeleteOutlined',
                'popconfirm' => [
                    'title' => '确定要删除这条记录吗？',
                    'okText' => '确定',
                    'cancelText' => '取消'
                ]
            ],
            [
                'type' => 'custom',
                'title' => '复制',
                'icon' => 'CopyOutlined',
                'code' => 'copy_row'
            ]
        ]
    ]
]
```

**操作类型说明：**

- `edit`: 编辑操作，会打开编辑表单
- `delete`: 删除操作，会从表格中删除该行
- `custom`: 自定义操作，需要指定 `code` 字段用于识别操作类型

## 新增字段类型：Upload

`Upload` 类型用于文件上传功能，支持多种文件类型和上传样式。

### 数据格式规范

```php
[
    'field' => 'avatar',                    // 字段名
    'type' => 'Upload',                     // 类型必须为 'Upload'
    'title' => '头像',                      // 字段标题
    'required' => true,                     // 是否必填（可选）
    'config' => [
        'accept' => '.png,.jpg,.jpeg',      // 接受的文件类型
        'maxCount' => 1,                    // 最大文件数量
        'multiple' => false,                // 是否支持多选
        'showUploadList' => true,           // 是否显示文件列表
        'listType' => 'picture-card',       // 上传列表样式
        'uploadText' => '上传头像',         // 上传按钮文本
        'disabled' => false                 // 是否禁用
        // 注意：上传地址固定为 'oss/putFile'，无需配置
    ]
]
```

### 配置项说明

- **accept**: 接受的文件类型，使用逗号分隔的文件扩展名
- **maxCount**: 最大文件数量，默认为 1
- **multiple**: 是否支持多选文件，默认为 false
- **showUploadList**: 是否显示文件列表，默认为 true
- **listType**: 上传列表样式，支持：
  - `text`: 文本样式，适合文档类文件
  - `picture`: 图片样式，显示缩略图
  - `picture-card`: 卡片样式，适合图片上传
  - `picture-circle`: 圆形样式，适合头像上传
- **uploadText**: 上传按钮的文本，默认为 "上传{title}"
- **disabled**: 是否禁用上传功能

**重要说明**：上传地址固定为 `oss/putFile`，无需在配置中指定上传地址或自定义请求函数。

**上传功能特性**：

1. **批量上传优化**：大批量文件采用逐个上传策略，避免并发上传导致的服务器压力
2. **智能状态管理**：上传过程中自动禁用表单提交按钮，防止用户在文件未完成上传时提交表单
3. **简洁错误提示**：上传失败时显示简洁的错误信息，避免信息过载
4. **完成状态反馈**：所有文件上传完成后自动启用表单提交按钮
5. **多组件支持**：支持同一表单中存在多个上传组件，各组件状态独立管理

### 常用文件类型

```php
// 图片文件
'accept' => '.png,.jpg,.jpeg,.gif,.bmp,.webp'

// 文档文件
'accept' => '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt'

// 压缩文件
'accept' => '.zip,.rar,.7z,.tar,.gz'

// 音频文件
'accept' => '.mp3,.wav,.flac,.aac'

// 视频文件
'accept' => '.mp4,.avi,.mov,.wmv,.flv'
```

## 函数签名

```typescript
transformBackendSearchToSchema(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options?: { enableGrouping?: boolean }
): VbenFormSchema[]
```

## 重要更新：分组数据处理

### 编辑新增页面的特殊处理

从版本更新开始，`transformBackendSearchToSchema` 方法现在支持分组数据的特殊处理：

- **第一组数据**：通常用于搜索条件表单
- **第二组数据**：专门用于编辑新增页面的表格展示
- **多组数据**：支持更复杂的分组结构

### 分组数据的应用场景

1. **列表页面搜索**：使用第一组数据作为搜索条件
2. **编辑新增页面**：使用第二组数据作为表单字段
3. **详情页面**：可以使用所有分组数据进行展示

### 快速参考：分组数据结构

```php
// 推荐的分组数据结构
return response()->json([
    'schema' => [
        [
            'label' => '搜索条件',     // 第一组：列表搜索
            'dataItem' => $searchFields,
        ],
        [
            'label' => '表单字段',     // 第二组：编辑新增页面（重要！）
            'dataItem' => $formFields,
        ],
        [
            'label' => '高级设置',     // 第三组：可选配置
            'dataItem' => $advancedFields,
        ],
    ],
]);
```

## 核心数据结构

### 1. 基础字段配置 (BackendSearchItem)

```php
<?php
// PHP 数组格式
$backendSearchItem = [
    'field' => 'username',              // 必填：字段名
    'title' => '用户名',                // 必填：显示标题
    'type' => 'input',                  // 必填：字段类型
    'required' => true,                 // 可选：是否必填
    'default' => '',                    // 可选：默认值
    'isonly' => false,                  // 可选：是否唯一
    'disabled' => false,                // 可选：是否禁用（外层）
    'ifShow' => true,                   // 可选：是否显示字段

    // 字段配置选项
    'config' => [
        'placeholder' => '请输入用户名',
        'allowClear' => true,
        'disabled' => false,            // 可选：是否禁用（config内层，优先级低于外层）
        // ... 更多配置
    ],

    // 联动配置（可选）
    'linkage' => [
        'triggerFields' => ['user_type'],
        'rules' => [
            // 联动规则配置
        ],
    ],
];
```

### 2. 字段属性优先级说明

#### 2.1 disabled 属性优先级

`disabled` 属性可以在两个位置设置，优先级如下：

1. **外层 disabled**（优先级高）：直接在字段定义的根级别
2. **config.disabled**（优先级低）：在 config 对象内部

```php
<?php
// 示例：外层 disabled 优先级更高
$field = [
    'field' => 'username',
    'title' => '用户名',
    'type' => 'input',
    'disabled' => true,        // 外层：优先级高，最终生效
    'config' => [
        'disabled' => false,   // 内层：优先级低，被忽略
        'placeholder' => '请输入用户名',
    ],
];

// 转换结果：componentProps.disabled = true
```

**推荐做法**：

- 对于简单的禁用控制，直接使用外层的 `disabled` 属性
- 对于复杂的联动禁用逻辑，使用 `config.linkage` 配置

#### 2.2 编辑权限控制 (editPermission)

**新功能**：支持根据表单模式（新增/编辑）控制字段的可编辑性。

```php
<?php
$field = [
    'field' => 'user_code',
    'title' => '用户编码',
    'type' => 'input',
    'config' => [
        'placeholder' => '请输入用户编码',
        'editPermission' => 'add-only',  // 编辑权限控制
    ],
];
```

**editPermission 支持的值：**

- `'both'`（默认）：新增和编辑都可以填写
- `'add-only'`：只有新增时可以填写，编辑时不可填写
- `'edit-only'`：只有编辑时可以填写，新增时不可填写
- `'none'`：新增和编辑都不可填写（只读显示）

**使用场景：**

- 用户编码：新增时可填写，编辑时不可修改
- 创建时间：只在编辑时显示，新增时不显示
- 系统字段：完全只读，不允许用户修改

#### 2.3 联动赋值配置 (linkageAssignment)

**新功能**：支持在选择某个字段后，自动给其他字段赋值。

```php
<?php
$field = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'labelField' => 'name',
        'valueField' => 'id',

        // 联动赋值配置
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'manager_id',
                    'valueMapping' => 'manager_id', // 直接使用选中项的字段名
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'department_code',
                    'valueMapping' => 'code', // 直接使用选中项的字段名
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'location',
                    'valueMapping' => '总部大楼', // 固定值
                    'clearOnEmpty' => false,
                ],
            ],
        ],
    ],
];
```

**linkageAssignment 配置说明：**

- `targetFields`：目标字段数组，定义要赋值的字段
- `field`：目标字段名
- `valueMapping`：值映射，支持以下格式：
  - 字段名字符串：直接使用选中项的字段名（如 `'manager_id'`）
  - 固定值：直接设置要赋值的内容（如 `'总部大楼'`）
- `clearOnEmpty`：当当前字段清空时，是否清空目标字段（默认 true）
- `options`：动态选项配置，为目标字段设置选项数据
  - `mapping`：选项映射，支持多种格式：
    - **函数形式**：`function($selectedValue, $selectedOption) { return $options; }` - 根据选中值和选项动态计算
    - **字段名字符串**：`'subcategories'` - 直接使用选中项的字段名获取选项
    - **对象映射**：`['value1' => $options1, 'value2' => $options2]` - 根据值映射
    - **固定值**：直接设置固定的选项数组
  - `default`：默认选项（当没有匹配时使用）
  - `autoSelect`：是否自动选择第一个选项
  - `clearValue`：是否清空当前值（当选项变化时）

**详细配置说明：**

1. **valueMapping 字段名形式**（推荐）：

   ```php
   'valueMapping' => 'manager_id'  // 直接使用选中项的字段名
   ```

   前端会自动从选中项数据中提取对应字段的值。

2. **valueMapping 固定值形式**：

   ```php
   'valueMapping' => '总部大楼'  // 直接设置固定值
   ```

3. **clearOnEmpty 配置**：
   - `true`（默认）：当前字段清空时，清空目标字段
   - `false`：当前字段清空时，保持目标字段的值不变

4. **options 动态选项配置**：

   支持两种配置格式：

   **数组格式**（简化配置）：

   ```php
   'options' => [
       ['label' => '选项1', 'value' => 'option1'],
       ['label' => '选项2', 'value' => 'option2'],
   ]
   ```

   **对象格式**（完整配置）：

   ```php
   'options' => [
       'mapping' => [
           '选择值1' => [
               ['label' => '选项1', 'value' => 'option1'],
               ['label' => '选项2', 'value' => 'option2'],
           ],
           '选择值2' => [
               ['label' => '选项3', 'value' => 'option3'],
               ['label' => '选项4', 'value' => 'option4'],
           ],
       ],
       'default' => [], // 默认选项
       'autoSelect' => false, // 是否自动选择第一个选项
       'clearValue' => true, // 选项变化时是否清空当前值
   ]
   ```
   - `mapping`：根据当前字段的选择值，为目标字段设置对应的选项数组
   - `default`：当没有匹配的选项时使用的默认选项
   - `autoSelect`：当选项更新后，是否自动选择第一个选项
   - `clearValue`：当选项发生变化时，是否清空目标字段的当前值

**配置示例：**

```php
<?php
// 示例1：基本联动赋值
$departmentField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'manager_id',
                    'valueMapping' => 'manager_id', // 从选中项中提取 manager_id 字段
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'department_code',
                    'valueMapping' => 'code', // 从选中项中提取 code 字段
                    'clearOnEmpty' => true,
                ],
            ],
        ],
    ],
];

// 示例2：混合配置（字段名 + 固定值）
$projectField = [
    'field' => 'project_id',
    'title' => '项目',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/projects/list',
        'labelField' => 'title',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'project_name',
                    'valueMapping' => 'title', // 从选中项中提取 title 字段
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'project_status',
                    'valueMapping' => '进行中', // 固定值
                    'clearOnEmpty' => false,
                ],
            ],
        ],
    ],
];

// 示例3：动态选项配置（对象映射方式）
$categoryField = [
    'field' => 'category_id',
    'title' => '商品分类',
    'type' => 'select',
    'config' => [
        'options' => [
            ['label' => '电子产品', 'value' => 'electronics'],
            ['label' => '服装', 'value' => 'clothing'],
            ['label' => '食品', 'value' => 'food'],
        ],
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'subcategory_id',
                    'valueMapping' => null, // 不设置值，只设置选项
                    'options' => [
                        'mapping' => [
                            'electronics' => [
                                ['label' => '手机', 'value' => 'phone'],
                                ['label' => '电脑', 'value' => 'computer'],
                                ['label' => '平板', 'value' => 'tablet'],
                            ],
                            'clothing' => [
                                ['label' => '上衣', 'value' => 'top'],
                                ['label' => '裤子', 'value' => 'pants'],
                                ['label' => '鞋子', 'value' => 'shoes'],
                            ],
                            'food' => [
                                ['label' => '水果', 'value' => 'fruit'],
                                ['label' => '蔬菜', 'value' => 'vegetable'],
                                ['label' => '肉类', 'value' => 'meat'],
                            ],
                        ],
                        'default' => [], // 默认为空选项
                        'autoSelect' => false, // 不自动选择
                        'clearValue' => true, // 选项变化时清空当前值
                    ],
                ],
            ],
        ],
    ],
];

// 示例4：使用字段名字符串方式的动态选项配置
$departmentFieldWithOptions = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'labelField' => 'name',
        'valueField' => 'id',
        // API 返回的数据格式：
        // [
        //   {
        //     "id": 1,
        //     "name": "技术部",
        //     "manager_id": "tech_001",
        //     "employees": [
        //       {"label": "张三", "value": "emp_001"},
        //       {"label": "李四", "value": "emp_002"}
        //     ]
        //   }
        // ]
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'manager_id',
                    'valueMapping' => 'manager_id', // 从选中项中提取 manager_id 字段
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'employee_id',
                    'valueMapping' => null, // 不设置值，只设置选项
                    'options' => [
                        'mapping' => 'employees', // 直接使用选中项的 employees 字段作为选项
                        'default' => [], // 默认为空选项
                        'autoSelect' => false, // 不自动选择
                        'clearValue' => true, // 选项变化时清空当前值
                    ],
                ],
            ],
        ],
    ],
];

// 示例5：使用数组格式的简化选项配置
$clientField = [
    'field' => 'client_id',
    'title' => '客户',
    'type' => 'select',
    'config' => [
        'options' => [
            ['label' => '客户A', 'value' => 'client_a'],
            ['label' => '客户B', 'value' => 'client_b'],
        ],
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'client_id',
                    'valueMapping' => 'customer_id', // 从选中项中提取 customer_id 字段
                    'clearOnEmpty' => true,
                    'options' => [
                        ['value' => 'customer_id', 'label' => 'customer_name']
                    ], // 直接使用数组格式设置固定选项
                ],
            ],
        ],
    ],
];
```

**前端实现原理：**

转换方法会自动为配置了 `linkageAssignment` 的字段创建 `componentProps` 函数和 `dependencies` 配置：

```javascript
// 转换后的 schema 结构
{
  field: 'category_id',
  component: 'Select',
  componentProps: (values, formApi) => {
    return {
      onChange: (value, selectedOption) => {
        // 执行联动赋值逻辑
        const targetFields = linkageAssignment.targetFields;

        targetFields.forEach(targetField => {
          if (value && selectedOption) {
            // 处理值映射
            let targetValue;
            if (typeof targetField.valueMapping === 'string' &&
                selectedOption.hasOwnProperty(targetField.valueMapping)) {
              targetValue = selectedOption[targetField.valueMapping];
            } else {
              targetValue = targetField.valueMapping;
            }

            // 设置目标字段值
            if (formApi && formApi.setFieldValue) {
              formApi.setFieldValue(targetField.field, targetValue);
            }

            // 处理动态选项配置
            if (targetField.options) {
              const optionsConfig = targetField.options;
              let targetOptions = [];

              // 使用类似 valueMapping 的逻辑来获取选项
              if (typeof optionsConfig.mapping === 'function') {
                // 如果是函数，直接调用
                targetOptions = optionsConfig.mapping(value, selectedOption) || [];
              } else if (typeof optionsConfig.mapping === 'string') {
                // 直接使用字符串作为字段名从 selectedOption 中获取选项
                targetOptions = selectedOption?.[optionsConfig.mapping] || optionsConfig.default || [];
              } else if (typeof optionsConfig.mapping === 'object' && optionsConfig.mapping !== null) {
                // 处理对象配置格式
                if (value && optionsConfig.mapping[value]) {
                  targetOptions = optionsConfig.mapping[value];
                } else {
                  targetOptions = optionsConfig.default || [];
                }
              } else {
                // 如果是其他类型的固定值，直接使用
                targetOptions = optionsConfig.mapping || optionsConfig.default || [];
              }

              // 设置目标字段的选项
              if (formApi && formApi.updateSchema) {
                formApi.updateSchema({
                  field: targetField.field,
                  componentProps: {
                    options: targetOptions
                  }
                });
              }

              // 如果配置了清空值或自动选择
              if (optionsConfig.clearValue && formApi && formApi.setFieldValue) {
                formApi.setFieldValue(targetField.field, undefined);
              } else if (optionsConfig.autoSelect && targetOptions.length > 0 && formApi && formApi.setFieldValue) {
                formApi.setFieldValue(targetField.field, targetOptions[0].value);
              }
            }
          } else if (targetField.clearOnEmpty !== false) {
            // 清空目标字段
            if (formApi && formApi.setFieldValue) {
              formApi.setFieldValue(targetField.field, undefined);
            }
          }
        });
      },
    };
  },
  dependencies: {
    triggerFields: ['category_id'],
  }
}
```

**完整前端使用示例：**

```javascript
// 后端返回的配置数据
const backendConfig = [
  {
    field: 'category_id',
    title: '商品分类',
    type: 'select',
    config: {
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装', value: 'clothing' },
        { label: '食品', value: 'food' },
      ],
      linkageAssignment: {
        targetFields: [
          {
            field: 'subcategory_id',
            valueMapping: null,
            options: {
              mapping: {
                'electronics': [
                  { label: '手机', value: 'phone' },
                  { label: '电脑', value: 'computer' },
                  { label: '平板', value: 'tablet' },
                ],
                'clothing': [
                  { label: '上衣', value: 'top' },
                  { label: '裤子', value: 'pants' },
                  { label: '鞋子', value: 'shoes' },
                ],
                'food': [
                  { label: '水果', value: 'fruit' },
                  { label: '蔬菜', value: 'vegetable' },
                  { label: '肉类', value: 'meat' },
                ],
              },
              default: [],
              autoSelect: false,
              clearValue: true,
            },
          },
        ],
      },
    },
  },
  {
    field: 'subcategory_id',
    title: '子分类',
    type: 'select',
    config: {
      options: [], // 初始为空，通过联动动态设置
      placeholder: '请先选择商品分类',
    },
  },
];

// 使用 transformBackendSearchToSchema 转换
import { transformBackendSearchToSchema } from '@/utils/search-schema';

const formSchema = transformBackendSearchToSchema(backendConfig);

// 在 Vue 组件中使用
<template>
  <VbenForm :schema="formSchema" @submit="handleSubmit" />
</template>

<script setup>
const handleSubmit = (values) => {
  console.log('表单提交值:', values);
  // 输出示例: { category_id: 'electronics', subcategory_id: 'phone' }
};
</script>
```

**支持的组件类型：**

- `select`：下拉选择
- `apiSelect`：API下拉选择
- `apiselect`：API下拉选择（别名）
- `tree`：树形选择
- `apiTree`：API树形选择
- `radio`：单选按钮组

**使用场景：**

**联动赋值场景：**

- 选择部门后自动填充部门经理、部门编码等信息
- 选择商品后自动填充商品价格、规格等信息
- 选择地区后自动填充邮政编码、区号等信息
- 选择客户后自动填充客户联系人、地址等信息

**动态选项场景：**

- 选择商品分类后，子分类字段显示对应的子分类选项
- 选择省份后，城市字段显示该省份下的城市选项
- 选择品牌后，型号字段显示该品牌下的型号选项
- 选择部门后，职位字段显示该部门的可用职位选项

### 3. 支持的字段类型 (type)

| 类型        | 前端组件      | 说明         | 适用场景     |
| ----------- | ------------- | ------------ | ------------ |
| `input`     | Input         | 普通输入框   | 文本输入     |
| `text`      | Input         | 文本输入框   | 同 input     |
| `password`  | InputPassword | 密码输入框   | 密码输入     |
| `textarea`  | Textarea      | 多行文本框   | 长文本输入   |
| `number`    | InputNumber   | 数字输入框   | 数值输入     |
| `select`    | Select        | 下拉选择框   | 固定选项选择 |
| `apiSelect` | ApiSelect     | API下拉选择  | 动态选项选择 |
| `radio`     | RadioGroup    | 单选按钮组   | 单选选择     |
| `checkbox`  | CheckboxGroup | 复选框组     | 多选选择     |
| `date`      | DatePicker    | 日期选择器   | 日期选择     |
| `dateRange` | RangePicker   | 日期范围选择 | 日期范围     |
| `time`      | TimePicker    | 时间选择器   | 时间选择     |
| `tree`      | TreeSelect    | 树形选择器   | 层级数据选择 |
| `apiTree`   | ApiTreeSelect | API树形选择  | 动态层级选择 |
| `switch`    | Switch        | 开关组件     | 布尔值选择   |
| `rate`      | Rate          | 评分组件     | 评分输入     |
| `mentions`  | Mentions      | 提及组件     | @用户功能    |
| `hidden`    | Input         | 隐藏字段     | 隐藏数据传递 |

### 3. 基础组件配置示例

#### 3.1 输入框类型 (input/text/password/textarea)

```php
<?php
$inputField = [
    'field' => 'username',
    'title' => '用户名',
    'type' => 'input',
    'required' => true,
    'config' => [
        'placeholder' => '请输入用户名',
        'allowClear' => true,
        'disabled' => false,
        'prefix' => 'user',              // 前缀
        'suffix' => '@domain.com',       // 后缀
        'componentProps' => [
            'maxlength' => 50,
            'showCount' => true,
        ],
    ],
];

$passwordField = [
    'field' => 'password',
    'title' => '密码',
    'type' => 'password',
    'required' => true,
    'config' => [
        'placeholder' => '请输入密码',
        'allowClear' => true,
    ],
];

$textareaField = [
    'field' => 'description',
    'title' => '描述',
    'type' => 'textarea',
    'config' => [
        'placeholder' => '请输入描述信息',
        'componentProps' => [
            'rows' => 4,
            'maxlength' => 500,
            'showCount' => true,
        ],
    ],
];
```

#### 3.2 数字输入框 (number)

```php
<?php
$numberField = [
    'field' => 'age',
    'title' => '年龄',
    'type' => 'number',
    'config' => [
        'placeholder' => '请输入年龄',
        'min' => 0,
        'max' => 120,
        'precision' => 0,               // 精度
        'allowClear' => true,
    ],
];
```

#### 3.3 选择框类型 (select)

```php
<?php
$selectField = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'select',
    'config' => [
        'placeholder' => '请选择状态',
        'allowClear' => true,
        'showSearch' => true,           // 是否显示搜索
        'filterOption' => true,         // 是否支持过滤
        'multiple' => false,            // 是否多选（会转换为 mode: 'multiple'）
        'mode' => 'default',            // 选择模式: default/multiple/tags

        // 选项数据 - 数组格式
        'options' => [
            ['label' => '启用', 'value' => 1],
            ['label' => '禁用', 'value' => 0],
            ['label' => '待审核', 'value' => 2, 'disabled' => true],
        ],

        // 或者对象格式
        'options' => [
            '1' => ['label' => '启用'],
            '0' => ['label' => '禁用'],
            '2' => ['label' => '待审核', 'disabled' => true],
        ],

        // 多选相关配置
        'maxTagCount' => 3,             // 最多显示标签数
        'maxTagTextLength' => 10,       // 标签最大长度
        'maxTagPlaceholder' => '...',   // 超出时占位符
    ],
];
```

#### 3.4 API选择框 (apiSelect)

```php
<?php
$apiSelectField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'placeholder' => '请选择部门',
        'allowClear' => true,
        'showSearch' => true,
        'multiple' => false,

        // API 配置
        'url' => '/api/departments/list',    // API 地址（推荐）
        'params' => [                        // 请求参数
            'status' => 1,
        ],
        'labelField' => 'name',              // 显示字段名
        'valueField' => 'id',                // 值字段名
        'resultField' => 'items',            // 结果字段路径
        'immediate' => true,                 // 是否立即加载

        // 自定义标签格式化（前端配置）
        'labelFormatter' => null,           // 自定义显示格式函数
        'alwaysLoad' => false,               // 每次显示时都重新加载

        // 搜索相关配置
        'searchable' => true,                // 是否启用搜索
        'searchParamName' => 'search',       // 搜索参数名
        'searchFieldName' => 'name',         // 搜索字段名
        'disableSearchRequest' => false,     // 是否禁用搜索请求

        // 分页配置（用于 ApiSelect 滚动加载）
        'pagination' => true,
        'pageSize' => 20,
        'pageParamName' => 'page',
        'pageSizeParamName' => 'pageSize',
        'loadMore' => true,
        'returnParamsField' => 'department_id', // 回显数据时用于传递参数的字段名

        // 数据处理函数
        'beforeFetch' => null,               // 请求前处理函数，支持函数名引用或配置对象
        'afterFetch' => null,                // 请求后处理函数，支持函数名引用或配置对象
        'autoSelect' => false,               // 自动选择: first/last/one/false

        // afterFetch 配置示例：
        // 1. 函数名引用（字符串）
        'afterFetch' => 'formatUserOptions',

        // 2. 配置对象（推荐）
        'afterFetch' => [
            'type' => 'formatOptions',
            'labelFormat' => '{name}-{price}',
            'filterInactive' => true,
        ],
    ],
];
```

#### 3.5 单选按钮组 (radio)

```php
<?php
$radioField = [
    'field' => 'gender',
    'title' => '性别',
    'type' => 'radio',
    'config' => [
        'optionType' => 'default',          // 按钮类型: default/button
        'buttonStyle' => 'outline',         // 按钮样式: outline/solid

        'options' => [
            ['label' => '男', 'value' => 'male'],
            ['label' => '女', 'value' => 'female'],
            ['label' => '其他', 'value' => 'other', 'disabled' => true],
        ],
    ],
];
```

#### 3.6 复选框组 (checkbox)

```php
<?php
$checkboxField = [
    'field' => 'hobbies',
    'title' => '爱好',
    'type' => 'checkbox',
    'config' => [
        'options' => [
            ['label' => '读书', 'value' => 'reading'],
            ['label' => '运动', 'value' => 'sports'],
            ['label' => '音乐', 'value' => 'music'],
            ['label' => '旅行', 'value' => 'travel'],
        ],
    ],
];
```

#### 3.6.1 数据处理函数 (beforeFetch & afterFetch)

`beforeFetch` 和 `afterFetch` 是 `apiSelect` 和 `apiTree` 组件的数据处理钩子函数，允许在 API 请求前后对数据进行自定义处理。

**重要说明：这些函数支持以下配置方式：**

1. **前端直接配置**：在前端代码中直接设置函数
2. **后端传递函数名**：通过字段配置传递预定义的全局函数名（字符串）
3. **后端传递配置对象**：通过配置对象灵活定义处理逻辑（推荐）

##### PHP 后端配置方式

**方式1：传递函数名（字符串）**

```php
<?php
$apiSelectField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'labelField' => 'name',
        'valueField' => 'id',

        // 请求前处理函数 - 传递全局函数名（字符串）
        'beforeFetch' => 'addActiveStatusFilter',

        // 请求后处理函数 - 传递全局函数名（字符串）
        'afterFetch' => 'formatDepartmentOptions',
    ],
];
```

**方式2：传递配置对象（推荐）**

```php
<?php
$apiSelectField = [
    'field' => 'product_id',
    'title' => '产品',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/products/list',
        'labelField' => 'name',
        'valueField' => 'id',

        // 使用配置对象进行数据处理
        'afterFetch' => [
            'type' => 'formatOptions',                    // 处理类型（必需）
            'labelFormat' => '{name}-{fBuyPri}',          // 标签格式化模板
            'filterInactive' => true,                     // 过滤非活跃项
            'addEmptyOption' => true,                     // 添加空选项
            'emptyOptionText' => '请选择产品',             // 空选项文本
            'sortBy' => 'name',                          // 排序字段
            'sortOrder' => 'asc',                        // 排序方向：asc/desc
        ],
    ],
];
```

##### 配置对象参数说明

**formatOptions 类型配置对象支持的参数：**

| 参数名 | 类型 | 必需 | 说明 | 示例 |
| --- | --- | --- | --- | --- |
| `type` | string | 是 | 处理器类型，固定为 `formatOptions` | `'formatOptions'` |
| `labelFormat` | string | 否 | 标签格式化模板，使用 `{字段名}` 占位符 | `'{name}-{price}'` |
| `filterInactive` | boolean | 否 | 是否过滤非活跃项 | `true` |
| `filterField` | string | 否 | 自定义过滤字段名 | `'status'` |
| `filterValue` | any | 否 | 自定义过滤值 | `'active'` |
| `addEmptyOption` | boolean | 否 | 是否添加空选项 | `true` |
| `emptyOptionText` | string | 否 | 空选项显示文本 | `'请选择'` |
| `sortBy` | string | 否 | 排序字段名 | `'name'` |
| `sortOrder` | string | 否 | 排序方向：`asc` 或 `desc` | `'asc'` |

**标签格式化模板说明：**

- 使用 `{字段名}` 格式引用数据字段
- 支持嵌套字段访问：`{category.name}`
- 支持多个字段组合：`{name}-{price}元`

**过滤规则说明：**

- `filterInactive: true` 会过滤以下字段为 `true` 的项目：
  - `status === 'active'`
  - `is_active === true`
  - `active === true`
  - `enabled === true`

**使用示例：**

```php
<?php
// 复杂配置示例
'afterFetch' => [
    'type' => 'formatOptions',
    'labelFormat' => '{name} - ¥{price} ({category.name})',  // 支持嵌套字段
    'filterInactive' => true,                                // 过滤非活跃项
    'addEmptyOption' => true,                                // 添加空选项
    'emptyOptionText' => '请选择商品',                        // 空选项文本
    'sortBy' => 'sort_order',                               // 按排序字段排序
    'sortOrder' => 'asc',                                   // 升序排列
],
```

##### beforeFetch 使用方法

`beforeFetch` 函数在发送 API 请求前被调用，可以用来修改请求参数：

```javascript
// 前端配置示例
const departmentField = {
  field: 'department_id',
  type: 'apiSelect',
  title: '部门',
  config: {
    url: '/api/departments/list',
    labelField: 'name',
    valueField: 'id',

    // 请求前处理参数
    beforeFetch: async (params) => {
      // 添加额外的请求参数
      return {
        ...params,
        status: 'active',
        include_children: true,
        timestamp: Date.now(),
      };
    },
  },
};
```

##### afterFetch 使用方法

`afterFetch` 函数在 API 请求完成后被调用，可以用来处理和转换响应数据：

```javascript
// 前端配置示例
const userField = {
  field: 'user_id',
  type: 'apiSelect',
  title: '用户',
  config: {
    url: '/api/users/list',
    labelField: 'display_name',
    valueField: 'id',

    // 请求后处理响应数据
    afterFetch: async (response) => {
      // 处理 API 响应数据
      if (response && response.data) {
        // 转换数据格式
        const processedData = response.data.map((item) => ({
          ...item,
          display_name: `${item.name} (${item.department})`, // 组合显示名称
          disabled: item.status !== 'active', // 根据状态设置禁用
        }));

        // 返回处理后的数据
        return {
          ...response,
          data: processedData,
        };
      }
      return response;
    },
  },
};
```

##### 后端配置示例

**使用预定义函数名**

```php
<?php
// 用户选择字段
$userField = [
    'field' => 'user_id',
    'title' => '用户',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/users/list',
        'labelField' => 'name',
        'valueField' => 'id',
        'afterFetch' => 'formatUserOptions', // 引用预定义函数
    ],
];

// 部门选择字段
$departmentField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'beforeFetch' => 'addActiveFilter',    // 请求前过滤
        'afterFetch' => 'addEmptyOption',      // 请求后添加空选项
    ],
];
```

##### 前端处理函数实现

当后端传递函数名字符串时，前端需要预先定义对应的全局处理函数：

```javascript
// 全局处理函数注册（可以注册到 window 对象上）
window.apiDataProcessors = {
  // 格式化用户选项
  formatUserOptions: async (response) => {
    const data = response.data || response;
    return data.map((item) => ({
      ...item,
      label: `${item.name} (${item.email})`,
      disabled: item.status !== 'active',
    }));
  },

  // 添加激活状态过滤（用于 beforeFetch）
  addActiveFilter: async (params) => ({
    ...params,
    status: 'active',
  }),

  // 添加空选项（用于 afterFetch）
  addEmptyOption: async (response) => {
    const data = Array.isArray(response) ? response : response.data || [];
    return [{ id: '', name: '请选择', value: '' }, ...data];
  },

  // 格式化部门选项
  formatDepartmentOptions: async (response) => {
    const data = response.data || response;
    return data.map((item) => ({
      ...item,
      label: `${item.name} (${item.code || ''})`,
      disabled: item.status !== 'active',
    }));
  },
};
```

**注意事项：**

1. **函数名匹配**：后端传递的字符串必须与前端定义的函数名完全一致
2. **全局注册**：处理函数需要注册到全局对象上（如 `window.apiDataProcessors`）
3. **异步支持**：处理函数应该是 async 函数，支持异步操作
4. **错误处理**：如果找不到对应的处理函数，系统会输出警告并返回原始数据

#### 3.6.2 自定义标签格式化 (labelFormatter)

`labelFormatter` 是 `apiSelect` 和 `apiTree` 组件的高级功能，允许自定义选项的显示格式。这在需要显示复合信息（如汇率、价格等）时特别有用。

**注意：`labelFormatter` 是前端配置，需要在前端代码中设置，不能通过后端 PHP 配置传递。**

```javascript
// 前端配置示例
const currencyRateField = {
  field: 'currency_rate',
  type: 'apiSelect',
  title: '汇率',
  config: {
    url: '/api/currency/rates',
    labelField: 'name',
    valueField: 'id',
    placeholder: '请选择汇率',

    // 自定义标签格式化函数
    labelFormatter: (item) => {
      // item 包含 API 返回的完整数据
      // 例如: { id: 1, name: "南非兰特", rate: -0.00212, code: "ZAR" }

      const name = item.name || '';
      const rate = item.rate || 0;

      // 格式化汇率显示
      const formattedRate = rate >= 0 ? `+${rate}` : `${rate}`;

      // 返回 "名称 汇率" 格式
      return `${name} ${formattedRate}`;
    },
  },
};
```

**使用场景：**

1. **汇率显示**：显示 "货币名称 汇率变化"
2. **价格显示**：显示 "商品名称 ¥价格"
3. **用户信息**：显示 "姓名 (部门)"
4. **状态信息**：显示 "项目名称 [状态]"

**API 数据格式要求：**

```json
{
  "items": [
    {
      "id": 1,
      "name": "南非兰特",
      "rate": -0.00212,
      "code": "ZAR"
    },
    {
      "id": 2,
      "name": "韩国元",
      "rate": 0.00156,
      "code": "KRW"
    }
  ]
}
```

**显示效果：**

经过 `labelFormatter` 处理后，下拉选项显示为：

- "南非兰特 -0.00212"
- "韩国元 +0.00156"

#### 3.7 日期选择器 (date/dateRange)

```php
<?php
$dateField = [
    'field' => 'birthday',
    'title' => '生日',
    'type' => 'date',
    'config' => [
        'placeholder' => '请选择生日',
        'allowClear' => true,
        'componentProps' => [
            'format' => 'YYYY-MM-DD',
            'showTime' => false,
        ],
    ],
];

$dateRangeField = [
    'field' => 'date_range',
    'title' => '日期范围',
    'type' => 'dateRange',
    'config' => [
        'placeholder' => ['开始日期', '结束日期'],
        'allowClear' => true,
        'componentProps' => [
            'format' => 'YYYY-MM-DD',
        ],
    ],
];
```

#### 3.8 树形选择器 (tree/apiTree)

```php
<?php
$treeField = [
    'field' => 'category_id',
    'title' => '分类',
    'type' => 'tree',
    'config' => [
        'placeholder' => '请选择分类',
        'allowClear' => true,
        'showSearch' => true,
        'multiple' => false,

        // 静态树形数据
        'treeData' => [
            [
                'label' => '一级分类',
                'value' => 1,
                'children' => [
                    ['label' => '二级分类1', 'value' => 11],
                    ['label' => '二级分类2', 'value' => 12],
                ],
            ],
            [
                'label' => '另一个一级分类',
                'value' => 2,
                'children' => [
                    ['label' => '二级分类3', 'value' => 21],
                ],
            ],
        ],

        // 树形组件特有配置
        'treeCheckable' => false,           // 是否显示复选框
        'treeCheckStrictly' => false,       // 父子节点选中状态不关联
        'treeDefaultExpandAll' => true,     // 默认展开所有节点
    ],
];

$apiTreeField = [
    'field' => 'org_id',
    'title' => '组织',
    'type' => 'apiTree',
    'config' => [
        'placeholder' => '请选择组织',
        'allowClear' => true,
        'showSearch' => true,
        'multiple' => false,

        // API 配置
        'url' => '/api/organizations/tree',
        'params' => ['status' => 1],
        'labelField' => 'name',
        'valueField' => 'id',
        'childrenField' => 'children',
        'resultField' => 'items',
        'immediate' => true,

        // 搜索配置（注意：ApiTreeSelect 不支持搜索请求）
        'searchable' => false,
        'disableSearchRequest' => true,

        // 树形特有配置
        'treeCheckable' => false,
        'treeCheckStrictly' => false,
        'treeDefaultExpandAll' => false,
        'filterTreeNode' => null,           // 树节点过滤函数
    ],
];
```

#### 3.9 其他组件类型

```php
<?php
// 开关组件
$switchField = [
    'field' => 'is_active',
    'title' => '是否激活',
    'type' => 'switch',
    'default' => false,
    'config' => [
        'componentProps' => [
            'checkedChildren' => '开',
            'unCheckedChildren' => '关',
        ],
    ],
];

// 评分组件
$rateField = [
    'field' => 'rating',
    'title' => '评分',
    'type' => 'rate',
    'config' => [
        'componentProps' => [
            'count' => 5,
            'allowHalf' => true,
        ],
    ],
];

// 时间选择器
$timeField = [
    'field' => 'work_time',
    'title' => '工作时间',
    'type' => 'time',
    'config' => [
        'placeholder' => '请选择时间',
        'allowClear' => true,
        'componentProps' => [
            'format' => 'HH:mm:ss',
        ],
    ],
];

// 提及组件
$mentionsField = [
    'field' => 'mentions',
    'title' => '提及用户',
    'type' => 'mentions',
    'config' => [
        'placeholder' => '输入@提及用户',
        'options' => [
            ['label' => '张三', 'value' => 'zhangsan'],
            ['label' => '李四', 'value' => 'lisi'],
        ],
    ],
];

// 隐藏字段
$hiddenField = [
    'field' => 'hidden_data',
    'title' => '隐藏数据',
    'type' => 'hidden',
    'default' => 'some_value',
    'config' => [
        'formItemClass' => 'hidden',
    ],
];
```

## 4. 分组配置

### 4.1 简单数组格式（无分组）

```php
<?php
$searchFields = [
    [
        'field' => 'name',
        'title' => '姓名',
        'type' => 'input',
        'config' => ['placeholder' => '请输入姓名'],
    ],
    [
        'field' => 'status',
        'title' => '状态',
        'type' => 'select',
        'config' => [
            'options' => [
                ['label' => '启用', 'value' => 1],
                ['label' => '禁用', 'value' => 0],
            ],
        ],
    ],
];
```

### 4.2 分组格式

```php
<?php
// 方式一：直接分组数组格式
$groupedSearchFields = [
    [
        'label' => '基本信息',
        'dataItem' => [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'input',
                'config' => ['placeholder' => '请输入姓名'],
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'input',
                'config' => ['placeholder' => '请输入邮箱'],
            ],
        ],
    ],
    [
        'label' => '高级设置',
        'dataItem' => [
            [
                'field' => 'department_id',
                'title' => '部门',
                'type' => 'apiSelect',
                'config' => [
                    'url' => '/api/departments/list',
                    'labelField' => 'name',
                    'valueField' => 'id',
                ],
            ],
        ],
    ],
];

// 方式二：包装在 schema 属性中的格式
$wrappedGroupedFields = [
    'schema' => [
        [
            'label' => '基本信息',
            'dataItem' => [
                // 字段配置...
            ],
        ],
        [
            'label' => '高级设置',
            'dataItem' => [
                // 字段配置...
            ],
        ],
    ],
];
```

## 5. 字段联动配置

### 5.1 联动配置结构

```php
<?php
$linkageField = [
    'field' => 'city',
    'title' => '城市',
    'type' => 'select',
    'config' => [
        'placeholder' => '请选择城市',
        'options' => [], // 初始为空，通过联动动态设置

        // 联动配置
        'linkage' => [
            'triggerFields' => ['province'],    // 触发字段列表
            'rules' => [
                // 联动规则配置
                'options' => [
                    'mapping' => [
                        'beijing' => [
                            ['label' => '东城区', 'value' => 'dongcheng'],
                            ['label' => '西城区', 'value' => 'xicheng'],
                            ['label' => '朝阳区', 'value' => 'chaoyang'],
                        ],
                        'shanghai' => [
                            ['label' => '黄浦区', 'value' => 'huangpu'],
                            ['label' => '徐汇区', 'value' => 'xuhui'],
                            ['label' => '长宁区', 'value' => 'changning'],
                        ],
                    ],
                    'default' => [], // 默认选项
                ],
            ],
        ],
    ],
];
```

### 5.2 联动规则类型

#### 5.2.1 显示/隐藏规则 (visibility)

```php
<?php
$visibilityLinkage = [
    'field' => 'company_name',
    'title' => '公司名称',
    'type' => 'input',
    'config' => [
        'linkage' => [
            'triggerFields' => ['user_type'],
            'rules' => [
                'visibility' => [
                    // 当用户类型为企业用户时显示
                    'showWhen' => [
                        [
                            'field' => 'user_type',
                            'operator' => 'equals',
                            'value' => 'enterprise',
                        ],
                    ],
                    // 当用户类型为个人用户时隐藏
                    'hideWhen' => [
                        [
                            'field' => 'user_type',
                            'operator' => 'equals',
                            'value' => 'personal',
                        ],
                    ],
                ],
            ],
        ],
    ],
];
```

#### 5.2.2 禁用规则 (disabled)

```php
<?php
$disabledLinkage = [
    'field' => 'approval_reason',
    'title' => '审批原因',
    'type' => 'textarea',
    'config' => [
        'linkage' => [
            'triggerFields' => ['status'],
            'rules' => [
                'disabled' => [
                    // 当状态为已审批时禁用
                    'disableWhen' => [
                        [
                            'field' => 'status',
                            'operator' => 'equals',
                            'value' => 'approved',
                        ],
                    ],
                    // 当状态为待审批时启用
                    'enableWhen' => [
                        [
                            'field' => 'status',
                            'operator' => 'equals',
                            'value' => 'pending',
                        ],
                    ],
                ],
            ],
        ],
    ],
];
```

#### 5.2.3 必填规则 (required)

```php
<?php
$requiredLinkage = [
    'field' => 'id_number',
    'title' => '身份证号',
    'type' => 'input',
    'config' => [
        'linkage' => [
            'triggerFields' => ['user_type'],
            'rules' => [
                'required' => [
                    // 当用户类型为实名用户时必填
                    'requiredWhen' => [
                        [
                            'field' => 'user_type',
                            'operator' => 'equals',
                            'value' => 'verified',
                        ],
                    ],
                    // 当用户类型为游客时可选
                    'optionalWhen' => [
                        [
                            'field' => 'user_type',
                            'operator' => 'equals',
                            'value' => 'guest',
                        ],
                    ],
                ],
            ],
        ],
    ],
];
```

#### 5.2.4 动态选项规则 (options)

```php
<?php
$optionsLinkage = [
    'field' => 'district',
    'title' => '区县',
    'type' => 'select',
    'config' => [
        'linkage' => [
            'triggerFields' => ['province', 'city'],
            'rules' => [
                'options' => [
                    'mapping' => [
                        // 根据省市组合设置区县选项
                        'beijing_dongcheng' => [
                            ['label' => '东华门街道', 'value' => 'donghuamen'],
                            ['label' => '景山街道', 'value' => 'jingshan'],
                        ],
                        'beijing_xicheng' => [
                            ['label' => '西长安街街道', 'value' => 'xichanganjie'],
                            ['label' => '新街口街道', 'value' => 'xinjiekou'],
                        ],
                        'shanghai_huangpu' => [
                            ['label' => '南京东路街道', 'value' => 'nanjingdonglu'],
                            ['label' => '外滩街道', 'value' => 'waitan'],
                        ],
                    ],
                    'default' => [],
                ],
            ],
        ],
    ],
];
```

#### 5.2.5 动态组件属性规则 (componentProps)

```php
<?php
$componentPropsLinkage = [
    'field' => 'amount',
    'title' => '金额',
    'type' => 'number',
    'config' => [
        'linkage' => [
            'triggerFields' => ['currency'],
            'rules' => [
                'componentProps' => [
                    'mapping' => [
                        'CNY' => [
                            'prefix' => '¥',
                            'precision' => 2,
                            'max' => 999999.99,
                        ],
                        'USD' => [
                            'prefix' => '$',
                            'precision' => 2,
                            'max' => 99999.99,
                        ],
                        'EUR' => [
                            'prefix' => '€',
                            'precision' => 2,
                            'max' => 99999.99,
                        ],
                    ],
                    'default' => [
                        'precision' => 2,
                        'max' => 999999.99,
                    ],
                ],
            ],
        ],
    ],
];
```

### 5.3 条件操作符

支持的条件操作符：

| 操作符          | 说明           | 示例                               |
| --------------- | -------------- | ---------------------------------- |
| `equals`        | 等于           | `'value' => 'active'`              |
| `not_equals`    | 不等于         | `'value' => 'inactive'`            |
| `in`            | 包含在数组中   | `'value' => ['active', 'pending']` |
| `not_in`        | 不包含在数组中 | `'value' => ['deleted', 'banned']` |
| `contains`      | 包含字符串     | `'value' => 'admin'`               |
| `not_contains`  | 不包含字符串   | `'value' => 'guest'`               |
| `starts_with`   | 以...开头      | `'value' => 'user_'`               |
| `ends_with`     | 以...结尾      | `'value' => '_admin'`              |
| `greater`       | 大于           | `'value' => 18`                    |
| `greater_equal` | 大于等于       | `'value' => 18`                    |
| `less`          | 小于           | `'value' => 65`                    |
| `less_equal`    | 小于等于       | `'value' => 65`                    |
| `is_empty`      | 为空           | `'value' => null`                  |
| `is_not_empty`  | 不为空         | `'value' => null`                  |
| `regex`         | 正则匹配       | `'value' => '/^[0-9]+$/'`          |
| `not_regex`     | 正则不匹配     | `'value' => '/^[0-9]+$/'`          |
| `custom`        | 自定义函数     | `'value' => 'customFunction'`      |

## 6. 完整的控制器示例

```php
<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Department;
use App\Models\Role;

class UserController extends Controller
{
    /**
     * 获取用户搜索表单配置
     */
    public function getSearchForm()
    {
        // 基础搜索字段
        $basicFields = [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'input',
                'config' => [
                    'placeholder' => '请输入用户姓名',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'input',
                'config' => [
                    'placeholder' => '请输入邮箱地址',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'status',
                'title' => '状态',
                'type' => 'select',
                'config' => [
                    'placeholder' => '请选择状态',
                    'allowClear' => true,
                    'options' => [
                        ['label' => '启用', 'value' => 1],
                        ['label' => '禁用', 'value' => 0],
                        ['label' => '待审核', 'value' => 2],
                    ],
                ],
            ],
        ];

        // 高级搜索字段
        $advancedFields = [
            [
                'field' => 'department_id',
                'title' => '部门',
                'type' => 'apiSelect',
                'config' => [
                    'placeholder' => '请选择部门',
                    'allowClear' => true,
                    'url' => '/api/departments/list',
                    'labelField' => 'name',
                    'valueField' => 'id',
                    'immediate' => true,
                    'showSearch' => true,
                    'searchParamName' => 'search',
                ],
            ],
            [
                'field' => 'role_id',
                'title' => '角色',
                'type' => 'select',
                'config' => [
                    'placeholder' => '请选择角色',
                    'allowClear' => true,
                    'multiple' => true,
                    'options' => $this->getRoleOptions(),
                ],
            ],
            [
                'field' => 'created_at',
                'title' => '创建时间',
                'type' => 'dateRange',
                'config' => [
                    'placeholder' => ['开始时间', '结束时间'],
                    'allowClear' => true,
                ],
            ],
        ];

        // 返回分组格式 - 重要：第二组数据用于编辑新增页面
        return response()->json([
            'schema' => [
                [
                    'label' => '搜索条件', // 第一组：用于列表页面搜索
                    'dataItem' => $searchFields,
                ],
                [
                    'label' => '编辑表单', // 第二组：用于编辑新增页面表格
                    'dataItem' => $formFields,
                ],
                [
                    'label' => '高级设置', // 第三组：可选的额外配置
                    'dataItem' => $advancedFields,
                ],
            ],
        ]);
    }

    /**
     * 获取用户表单配置（新增/编辑）
     * 重要：返回分组数据，第二组用于编辑新增页面
     */
    public function getFormSchema($id = null)
    {
        $isEdit = !empty($id);

        // 第一组：搜索条件字段
        $searchFields = [
            [
                'field' => 'search_name',
                'type' => 'input',
                'title' => '搜索姓名',
                'config' => [
                    'placeholder' => '请输入姓名进行搜索',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'search_status',
                'type' => 'select',
                'title' => '状态筛选',
                'config' => [
                    'placeholder' => '请选择状态',
                    'allowClear' => true,
                    'options' => [
                        ['label' => '全部', 'value' => ''],
                        ['label' => '启用', 'value' => 'active'],
                        ['label' => '禁用', 'value' => 'inactive'],
                    ],
                ],
            ],
        ];

        // 第二组：编辑新增表单字段（这是重点！）
        $formFields = [
            [
                'field' => 'name',
                'type' => 'input',
                'title' => '姓名',
                'required' => true,
                'config' => [
                    'placeholder' => '请输入姓名',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'email',
                'type' => 'input',
                'title' => '邮箱',
                'required' => true,
                'config' => [
                    'placeholder' => '请输入邮箱地址',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'phone',
                'type' => 'input',
                'title' => '手机号',
                'config' => [
                    'placeholder' => '请输入手机号',
                    'allowClear' => true,
                ],
            ],
            [
                'field' => 'department_id',
                'type' => 'apiSelect',
                'title' => '部门',
                'required' => true,
                'config' => [
                    'url' => '/api/departments/list',
                    'labelField' => 'name',
                    'valueField' => 'id',
                    'placeholder' => '请选择部门',
                    'allowClear' => true,
                    'showSearch' => true,
                ],
            ],
            [
                'field' => 'role_ids',
                'type' => 'apiSelect',
                'title' => '角色',
                'config' => [
                    'url' => '/api/roles/list',
                    'labelField' => 'name',
                    'valueField' => 'id',
                    'placeholder' => '请选择角色',
                    'allowClear' => true,
                    'mode' => 'multiple',
                    'showSearch' => true,
                ],
            ],
            [
                'field' => 'status',
                'type' => 'select',
                'title' => '状态',
                'required' => true,
                'default' => 'active',
                'config' => [
                    'placeholder' => '请选择状态',
                    'options' => [
                        ['label' => '启用', 'value' => 'active'],
                        ['label' => '禁用', 'value' => 'inactive'],
                    ],
                ],
            ],
            [
                'field' => 'bio',
                'type' => 'textarea',
                'title' => '个人简介',
                'config' => [
                    'placeholder' => '请输入个人简介',
                    'rows' => 4,
                    'allowClear' => true,
                ],
            ],
        ];

        // 第三组：高级设置（可选）
        $advancedFields = [
            [
                'field' => 'permissions',
                'type' => 'checkbox',
                'title' => '特殊权限',
                'config' => [
                    'options' => [
                        ['label' => '系统管理', 'value' => 'system_admin'],
                        ['label' => '用户管理', 'value' => 'user_admin'],
                        ['label' => '数据导出', 'value' => 'data_export'],
                    ],
                ],
            ],
        ];

        // 如果是编辑模式，可以根据需要调整字段
        if ($isEdit) {
            // 编辑模式下可能需要隐藏某些字段或添加特殊逻辑
            // 例如：不允许修改邮箱
            foreach ($formFields as &$field) {
                if ($field['field'] === 'email') {
                    $field['config']['disabled'] = true;
                }
            }
        }

        // 返回分组数据 - 关键：第二组数据用于编辑新增页面的表格
        return response()->json([
            'schema' => [
                [
                    'label' => '搜索条件', // 第一组：列表页面搜索使用
                    'dataItem' => $searchFields,
                ],
                [
                    'label' => '用户信息', // 第二组：编辑新增页面表格使用（重要！）
                    'dataItem' => $formFields,
                ],
                [
                    'label' => '高级设置', // 第三组：可选的高级配置
                    'dataItem' => $advancedFields,
                ],
            ],
        ]);
    }

    /**
     * 旧版本示例（保留用于参考）
     */
    public function getFormSchemaOldVersion($id = null)
    {
        $isEdit = !empty($id);

        $schema = [
            // 基本信息分组
            [
                'label' => '基本信息',
                'dataItem' => [
                    [
                        'field' => 'name',
                        'title' => '姓名',
                        'type' => 'input',
                        'required' => true,
                        'config' => [
                            'placeholder' => '请输入用户姓名',
                            'allowClear' => true,
                        ],
                    ],
                    [
                        'field' => 'email',
                        'title' => '邮箱',
                        'type' => 'input',
                        'required' => true,
                        'config' => [
                            'placeholder' => '请输入邮箱地址',
                            'allowClear' => true,
                        ],
                    ],
                    [
                        'field' => 'password',
                        'title' => $isEdit ? '新密码' : '密码',
                        'type' => 'password',
                        'required' => !$isEdit,
                        'config' => [
                            'placeholder' => $isEdit ? '留空则不修改密码' : '请输入密码',
                            'allowClear' => true,
                        ],
                    ],
                ],
            ],
            // 详细信息分组
            [
                'label' => '详细信息',
                'dataItem' => [
                    [
                        'field' => 'user_type',
                        'title' => '用户类型',
                        'type' => 'radio',
                        'default' => 'personal',
                        'config' => [
                            'optionType' => 'button',
                            'buttonStyle' => 'solid',
                            'options' => [
                                ['label' => '个人用户', 'value' => 'personal'],
                                ['label' => '企业用户', 'value' => 'enterprise'],
                            ],
                        ],
                    ],
                    [
                        'field' => 'company_name',
                        'title' => '公司名称',
                        'type' => 'input',
                        'config' => [
                            'placeholder' => '请输入公司名称',
                            'allowClear' => true,
                            // 联动配置：只有企业用户才显示
                            'linkage' => [
                                'triggerFields' => ['user_type'],
                                'rules' => [
                                    'visibility' => [
                                        'showWhen' => [
                                            [
                                                'field' => 'user_type',
                                                'operator' => 'equals',
                                                'value' => 'enterprise',
                                            ],
                                        ],
                                    ],
                                    'required' => [
                                        'requiredWhen' => [
                                            [
                                                'field' => 'user_type',
                                                'operator' => 'equals',
                                                'value' => 'enterprise',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        'field' => 'department_id',
                        'title' => '部门',
                        'type' => 'apiSelect',
                        'config' => [
                            'placeholder' => '请选择部门',
                            'allowClear' => true,
                            'url' => '/api/departments/list',
                            'labelField' => 'name',
                            'valueField' => 'id',
                            'immediate' => true,
                        ],
                    ],
                    [
                        'field' => 'role_ids',
                        'title' => '角色',
                        'type' => 'select',
                        'config' => [
                            'placeholder' => '请选择角色',
                            'allowClear' => true,
                            'multiple' => true,
                            'options' => $this->getRoleOptions(),
                        ],
                    ],
                ],
            ],
        ];

        return response()->json($schema);
    }

    /**
     * 获取角色选项
     */
    private function getRoleOptions()
    {
        return Role::where('status', 1)
            ->get(['id', 'name'])
            ->map(function ($role) {
                return [
                    'label' => $role->name,
                    'value' => $role->id,
                ];
            })
            ->toArray();
    }

    /**
     * 部门列表 API（用于 apiSelect）
     */
    public function departmentList(Request $request)
    {
        $query = Department::where('status', 1);

        // 搜索功能
        if ($request->has('search') && !empty($request->search)) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // 分页
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 20);

        $departments = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get(['id', 'name', 'parent_id']);

        return response()->json([
            'items' => $departments->map(function ($dept) {
                return [
                    'id' => $dept->id,
                    'name' => $dept->name,
                    'value' => $dept->id,
                    'label' => $dept->name,
                    'parent_id' => $dept->parent_id,
                ];
            }),
            'total' => $query->count(),
        ]);
    }
}
```

## 7. 数据验证规则

### 7.1 必填字段验证

```php
<?php
function validateBackendSearchItem($item) {
    $required = ['field', 'title', 'type'];

    foreach ($required as $field) {
        if (!isset($item[$field]) || empty($item[$field])) {
            throw new InvalidArgumentException("Field '{$field}' is required");
        }
    }

    // 类型验证
    $validTypes = [
        'input', 'text', 'password', 'textarea', 'number', 'select',
        'apiSelect', 'radio', 'checkbox', 'date', 'dateRange', 'time',
        'tree', 'apiTree', 'switch', 'rate', 'mentions', 'hidden'
    ];

    if (!in_array($item['type'], $validTypes)) {
        throw new InvalidArgumentException("Invalid field type: {$item['type']}");
    }

    return true;
}

// 联动配置验证
function validateLinkageConfig($linkage) {
    if (!isset($linkage['triggerFields']) || !is_array($linkage['triggerFields'])) {
        throw new InvalidArgumentException("Linkage triggerFields must be an array");
    }

    if (!isset($linkage['rules']) || !is_array($linkage['rules'])) {
        throw new InvalidArgumentException("Linkage rules must be an array");
    }

    return true;
}
```

## 8. 常见问题与解决方案

### 8.1 API 组件相关问题

**问题**：ApiSelect 组件没有数据显示

**解决方案**：

```php
<?php
// 确保 API 返回正确的数据格式
public function apiSelectData(Request $request) {
    $items = Model::all()->map(function ($item) {
        return [
            'id' => $item->id,
            'label' => $item->name,    // 确保有 label 字段
            'value' => $item->id,      // 确保有 value 字段
        ];
    });

    return response()->json([
        'items' => $items,  // 确保数据在 items 字段中
        'total' => $items->count(),
    ]);
}

// 字段配置
$apiSelectField = [
    'field' => 'category_id',
    'title' => '分类',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/categories/list',
        'labelField' => 'label',     // 对应返回数据的字段名
        'valueField' => 'value',     // 对应返回数据的字段名
        'resultField' => 'items',    // 指定数据字段路径
        'immediate' => true,         // 立即加载数据
    ],
];
```

### 8.2 联动功能相关问题

**问题1**：字段联动不生效

**解决方案**：

**问题2**：联动赋值后 apiSelect 等远程组件无法显示正确的标签

**问题描述**：使用 `linkageAssignment` 联动赋值功能时，目标字段（如 apiSelect）虽然设置了正确的值，但界面上显示的仍然是值而不是对应的标签文本。

**原因分析**：

- apiSelect 等远程组件需要通过 API 请求获取选项数据来显示标签
- 仅设置值不会自动触发组件重新加载数据
- 组件需要重新渲染并发送 API 请求来获取对应的标签信息

**解决方案**：系统已自动处理此问题，当检测到目标字段是远程组件（apiSelect、apiTree）时，会根据组件配置采用不同的刷新策略：

**策略1：有 returnParamsField 配置的组件**

1. **参数更新**：通过 `updateSchema` 更新组件的 `params` 属性
2. **触发API请求**：参数变化会自动触发 `ApiComponent` 的 `params` watch，从而调用 `fetchApi`
3. **强制刷新**：使用时间戳 `key` 确保组件重新渲染

**策略2：没有 returnParamsField 配置的组件**

1. **组件重渲染**：使用时间戳 `key` 强制组件重新渲染
2. **属性更新**：设置 `immediate: true` 和 `alwaysLoad: true`
3. **直接调用API**：通过 `getFieldComponentRef` 获取组件实例，调用 `updateParam` 方法强制刷新
4. **延迟处理**：使用多层延迟确保各步骤按序执行
5. **重复设置值**：刷新后再次设置值，确保数据一致性

**配置示例**：

```php
// 无需特殊配置，系统会自动处理
$linkageField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments/list',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'manager_id', // apiSelect 组件
                    'valueMapping' => 'manager_id',
                    // 系统会自动处理标签显示问题
                ],
            ],
        ],
    ],
];
```

**调试信息**：在浏览器控制台中可以看到相关日志：

**有 returnParamsField 配置的组件**：

- `[联动赋值] 检测到远程组件 xxx，强制刷新以显示正确标签`
- `[联动赋值] 远程组件 xxx 有 returnParamsField 配置: paramName`
- `[联动赋值] 为远程组件 xxx 更新参数: {paramName: "value"}`
- `[联动赋值] 设置刷新标记: refresh_xxx`
- `[ApiComponent] params watch triggered - detailed: ...`
- `[ApiComponent] params changed (JSON comparison), calling fetchApi`

**没有 returnParamsField 配置的组件**：

- `[联动赋值] 检测到远程组件 xxx，强制刷新以显示正确标签`
- `[联动赋值] 远程组件 xxx 没有 returnParamsField 配置，使用通用刷新方式`
- `[联动赋值] 为远程组件 xxx 设置通用刷新标记: refresh_xxx`
- `[联动赋值] 刷新后重新设置值: xxx = "value"`
- `[联动赋值] 调用组件 xxx 的 updateParam 方法强制刷新`

**解决方案**：

```php
<?php
// 确保触发字段名正确
$linkageField = [
    'field' => 'city',
    'title' => '城市',
    'type' => 'select',
    'config' => [
        'linkage' => [
            'triggerFields' => ['province'],  // 确保字段名与表单中的字段名一致
            'rules' => [
                'options' => [
                    'mapping' => [
                        'beijing' => [  // 确保键值与触发字段的值匹配
                            ['label' => '东城区', 'value' => 'dongcheng'],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
```

### 8.3 选项数据相关问题

**问题**：选项数据格式不正确

**解决方案**：

```php
<?php
// 支持的选项格式
$options = [
    // 数组格式（推荐）
    ['label' => '选项1', 'value' => 1],
    ['label' => '选项2', 'value' => 2, 'disabled' => true],

    // 对象格式
    '1' => ['label' => '选项1'],
    '2' => ['label' => '选项2', 'disabled' => true],
];

// 确保数据值与选项值类型匹配
$rowData = [
    'status' => 1,  // 数字类型
    // 或
    'status' => '1',  // 字符串类型
];
```

### 8.4 分组显示相关问题

**问题**：分组标题不显示

**解决方案**：

```php
<?php
// 确保分组格式正确
$groupedData = [
    [
        'label' => '基本信息',  // 分组标题
        'dataItem' => [        // 字段数组
            // 字段配置...
        ],
    ],
];

// 或包装格式
$wrappedData = [
    'schema' => [
        [
            'label' => '基本信息',
            'dataItem' => [
                // 字段配置...
            ],
        ],
    ],
];
```

## 9. 性能优化建议

### 9.1 选项数据缓存

```php
<?php
class FormOptionsCache
{
    public static function getDepartmentOptions()
    {
        return Cache::remember('form_dept_options', 3600, function () {
            return Department::where('status', 1)
                ->get(['id', 'name'])
                ->map(function ($dept) {
                    return [
                        'label' => $dept->name,
                        'value' => $dept->id,
                    ];
                })
                ->toArray();
        });
    }

    public static function getRoleOptions()
    {
        return Cache::remember('form_role_options', 1800, function () {
            return Role::where('status', 1)
                ->get(['id', 'name'])
                ->map(function ($role) {
                    return [
                        'label' => $role->name,
                        'value' => $role->id,
                    ];
                })
                ->toArray();
        });
    }
}
```

### 9.2 API 接口优化

```php
<?php
// 优化 API 查询性能
public function optimizedApiList(Request $request)
{
    $query = Model::select(['id', 'name', 'status']);  // 只查询需要的字段

    // 添加索引优化搜索
    if ($request->has('search')) {
        $query->where('name', 'like', $request->search . '%');  // 前缀匹配更高效
    }

    // 分页查询
    $page = $request->get('page', 1);
    $pageSize = min($request->get('pageSize', 20), 100);  // 限制最大页面大小

    $items = $query->offset(($page - 1) * $pageSize)
        ->limit($pageSize)
        ->get()
        ->map(function ($item) {
            return [
                'label' => $item->name,
                'value' => $item->id,
            ];
        });

    return response()->json([
        'items' => $items,
        'total' => $query->count(),
    ]);
}
```

## 10. 总结

本文档提供了 `transformBackendSearchToSchema` 函数所需的完整数据格式规范，包括：

1. **基础字段配置**：支持 16 种不同的表单组件类型
2. **分组功能**：支持表单字段分组显示，提升用户体验
3. **联动功能**：支持复杂的字段间联动控制，包括显示/隐藏、启用/禁用、必填控制、动态选项、动态属性
4. **API 集成**：支持 ApiSelect 和 ApiTreeSelect 组件的动态数据加载
5. **数据验证**：提供完整的数据格式验证规则
6. **性能优化**：提供缓存和查询优化建议

遵循本规范可以确保前后端数据交互的一致性，实现复杂的表单功能，并保证系统的稳定性和性能。
