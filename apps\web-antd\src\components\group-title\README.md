# GroupTitle 分组标题组件

用于在表单中显示分组标题的组件，具有统一的样式和主题色适配。

## 功能特性

- ✅ **主题色适配**: 自动跟随系统主题色
- ✅ **渐变背景**: 美观的渐变背景效果
- ✅ **响应式设计**: 自适应容器宽度
- ✅ **简洁样式**: 清晰的视觉层次

## 基础用法

```vue
<template>
  <GroupTitle title="基本信息" />
  <GroupTitle title="联系方式" />
</template>

<script setup>
import { GroupTitle } from '#/components';
</script>
```

## 在 transformBackendSearchToSchema 中使用

当后端数据包含分组信息时，会自动使用 GroupTitle 组件：

```javascript
// 后端数据格式
const backendData = [
  {
    label: '基本信息',
    dataItem: [
      { field: 'name', type: 'input', title: '姓名' },
      { field: 'age', type: 'number', title: '年龄' },
    ],
  },
  {
    label: '联系方式',
    dataItem: [
      { field: 'phone', type: 'input', title: '电话' },
      { field: 'email', type: 'input', title: '邮箱' },
    ],
  },
];

// 转换后会自动生成 GroupTitle 组件
const schema = transformBackendSearchToSchema(backendData);
```

## Props

| 参数  | 说明               | 类型     | 默认值 |
| ----- | ------------------ | -------- | ------ |
| title | 分组标题文本       | `string` | `''`   |
| value | 标题文本（兼容性） | `string` | `''`   |

## 样式特性

- **主题色文字**: 使用 `hsl(var(--primary))` 主题色
- **渐变背景**: 从主题色到透明的渐变效果
- **圆角边框**: 4px 圆角
- **合适间距**: 上下 16px/8px，左右 12px 内边距

## 在表单中的表现

组件会自动占据表单的整行（`col-span-3`），确保分组标题独占一行，下方的表单字段按正常布局排列。

## 注意事项

1. 组件优先使用 `title` 属性，如果没有则使用 `value` 属性
2. 组件会自动适配系统主题，支持浅色/深色模式
3. 在表单中使用时会自动占据整行宽度
