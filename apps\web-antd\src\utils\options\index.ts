/**
 * 统一的选项处理工具函数
 * 用于标准化各种格式的选项数据，确保所有组件使用相同的处理逻辑
 */

export interface StandardOption {
  /** 选项的值 */
  value: any;
  /** 选项的显示标签 */
  label: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 选项颜色 */
  color?: string;
  /** 选项图标 */
  icon?: string;
  /** 选项描述 */
  description?: string;
  /** 头像URL（用于人员类型选项） */
  avatar?: string;
  /** 用户名（用于人员类型选项） */
  name?: string;
  /** 扩展属性 */
  [key: string]: any;
}

export interface OptionsConfig {
  /** 选项数据 */
  options?: any;
  /** 颜色配置映射 */
  optionsColor?: Record<number | string, string>;
  /** 默认值 */
  default?: any;
  /** 是否多选 */
  multiple?: boolean;
  /** 其他配置 */
  [key: string]: any;
}

/**
 * 统一的选项标准化函数
 * 支持多种数据格式：
 * 1. 字符串数组: ["启用", "停用"] -> [{value: 0abel: "启用"}, {value: 1, 1l: "停用"}]
 * 2. 对象数组: [{label: "选项1", value: "val1"}] -> 直接使用
 * 3. 对象格式: {"1": {name: "选项1", value: 1}} -> 转换为数组格式
 * 4. 对象格式（简单值）: {"1": "选项1"} -> [{value: "1", label: "选项1"}]
 *
 * @param options 选项配置
 * @param context 上下文信息（用于错误提示和调试）
 * @param context.componentName 组件名称
 * @param context.fieldName 字段名称
 * @param context.fieldType 字段类型
 * @returns 标准化的选项数组
 */
export function normalizeOptions(
  options: any,
  context?: {
    componentName?: string;
    fieldName?: string;
    fieldType?: string;
  },
): StandardOption[] {
  const {
    componentName: _componentName = 'unknown',
    fieldName: _fieldName = 'unknown',
    fieldType: _fieldType = 'unknown',
  } = context || {};

  if (!options) {
    return [];
  }

  // 处理数组格式
  if (Array.isArray(options)) {
    return options.map((opt, index) => {
      // 字符串或数字类型：使用索引作为 value
      if (typeof opt === 'string' || typeof opt === 'number') {
        return {
          value: index,
          label: String(opt),
          disabled: false,
        };
      }

      // 对象类型：标准化属性
      if (typeof opt === 'object' && opt) {
        return {
          value: opt.value === undefined ? index : opt.value,
          label: opt.label || opt.name || String(opt.value || index),
          disabled: opt.disabled || false,
          color: opt.color,
          icon: opt.icon,
          description: opt.description,
          avatar: opt.avatar,
          name: opt.name,
          // 保留所有原始属性
          ...opt,
        };
      }

      // 其他类型：转换为字符串
      return {
        value: index,
        label: String(opt),
        disabled: false,
      };
    });
  }

  // 处理对象格式
  if (typeof options === 'object' && options) {
    return Object.entries(options).map(([key, value]) => {
      // 复杂对象值
      if (typeof value === 'object' && value !== null) {
        const objValue = value as any;
        return {
          value: objValue.value === undefined ? key : objValue.value,
          label:
            objValue.label1 || objValue.label || objValue.name || String(key),
          disabled: objValue.disabled || false,
          color: objValue.color,
          icon: objValue.icon,
          description: objValue.description,
          avatar: objValue.avatar,
          name: objValue.name || objValue.label1 || objValue.label,
          // 保留所有原始属性
          ...objValue,
        };
      }

      // 简单值
      return {
        value: key,
        label: String(value),
        disabled: false,
      };
    });
  }

  // 其他格式：返回空数组
  return [];
}

/**
 * 专门为人员类型优化的选项标准化函数
 * 处理格式：{ "1": { "label1": "管理员", "avatar": "..." } }
 *
 * @param options 人员选项配置
 * @param context 上下文信息
 * @param context.componentName 组件名称
 * @param context.fieldName 字段名称
 * @param context.fieldType 字段类型
 * @returns 标准化的人员选项数组
 */
export function normalizePersonOptions(
  options: any,
  context?: {
    componentName?: string;
    fieldName?: string;
    fieldType?: string;
  },
): StandardOption[] {
  if (!options) return [];

  // 如果是数组，直接使用通用函数
  if (Array.isArray(options)) {
    return normalizeOptions(options, context);
  }

  // 对象格式的特殊处理
  if (typeof options === 'object' && options) {
    return Object.entries(options).map(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        const personData = value as any;
        return {
          value: personData.value === undefined ? key : personData.value,
          label:
            personData.label1 ||
            personData.label ||
            personData.name ||
            `用户${key}`,
          name:
            personData.name ||
            personData.label1 ||
            personData.label ||
            `用户${key}`,
          avatar: personData.avatar || '',
          disabled: personData.disabled || false,
          // 保留所有原始数据
          ...personData,
        };
      }

      // 兼容简单格式
      return {
        value: key,
        label: String(value),
        name: String(value),
        avatar: '',
        disabled: false,
      };
    });
  }

  return [];
}

/**
 * 从配置对象中提取选项相关配置
 * 支持 config.options 和直接 options 两种路径
 *
 * @param config 配置对象
 * @returns 提取的选项配置
 */
export function extractOptionsConfig(config: any): OptionsConfig {
  if (!config) return {};

  return {
    options: config.config?.options || config.options,
    optionsColor: config.config?.optionsColor || config.optionsColor || {},
    default: config.config?.default || config.default,
    multiple: config.config?.multiple ?? config.multiple,
    showAvatar: config.config?.showAvatar ?? config.showAvatar,
    avatarField: config.config?.avatarField || config.avatarField,
    nameField: config.config?.nameField || config.nameField,
    showPath: config.config?.showPath ?? config.showPath,
  };
}

/**
 * 根据值查找对应的选项
 * 支持严格匹配和类型转换匹配
 *
 * @param options 标准化后的选项数组
 * @param value 要查找的值
 * @returns 匹配的选项或null
 */
export function findOptionByValue(
  options: StandardOption[],
  value: any,
): null | StandardOption {
  if (!options || !Array.isArray(options)) return null;

  return (
    options.find((opt) => {
      // 严格相等比较
      if (opt.value === value) return true;

      // 类型转换比较（数字 <-> 字符串）
      if (String(opt.value) === String(value)) return true;

      // 数字类型的宽松比较
      if (typeof opt.value === 'number' && typeof value === 'string') {
        return opt.value === Number(value);
      }
      if (typeof opt.value === 'string' && typeof value === 'number') {
        return Number(opt.value) === value;
      }

      return false;
    }) || null
  );
}

/**
 * 根据值数组查找对应的选项数组（用于多选）
 *
 * @param options 标准化后的选项数组
 * @param values 要查找的值数组
 * @returns 匹配的选项数组
 */
export function findMultipleOptionsByValue(
  options: StandardOption[],
  values: any[],
): StandardOption[] {
  if (!Array.isArray(values)) return [];

  return values
    .map((value) => findOptionByValue(options, value))
    .filter((option): option is StandardOption => option !== null);
}

/**
 * 根据值获取对应的颜色
 * 支持多种类型的键匹配
 *
 * @param colors 颜色配置映射
 * @param value 值
 * @param defaultColor 默认颜色，如果为 null 则没有配置时返回 undefined
 * @returns 颜色值或 undefined（当没有配置且 defaultColor 为 null 时）
 */
export function getColorByValue(
  colors: Record<number | string, string> | undefined,
  value: any,
  defaultColor: null | string = null,
): string | undefined {
  if (!colors || typeof colors !== 'object') {
    return defaultColor || undefined;
  }

  // 直接匹配
  if (colors[value] !== undefined) return colors[value];

  // 字符串匹配
  if (colors[String(value)] !== undefined) return colors[String(value)];

  // 数字匹配
  if (
    typeof value === 'string' &&
    !Number.isNaN(Number(value)) &&
    colors[Number(value)] !== undefined
  ) {
    return colors[Number(value)];
  }
  if (typeof value === 'number' && colors[String(value)] !== undefined) {
    return colors[String(value)];
  }

  return defaultColor || undefined;
}

/**
 * 将标准化选项转换为详情展示组件的选项格式
 *
 * @param options 标准化后的选项数组
 * @returns 详情展示组件的选项配置
 */
export function convertToDetailOptions(options: StandardOption[]): {
  list: any[];
  mapping: Record<number | string, any>;
} {
  const mapping: Record<number | string, any> = {};
  const list: any[] = [];

  options.forEach((option) => {
    const detailOption = {
      label: option.label,
      value: option.value,
      color: option.color,
      icon: option.icon,
      status: option.status,
    };

    mapping[option.value] = detailOption;
    list.push(detailOption);
  });

  return { list, mapping };
}
