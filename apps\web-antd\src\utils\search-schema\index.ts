// 导出 API 请求相关函数
export {
  commonApiRequest,
  createAdvancedApiFunction,
  createApiFunction,
  createApiSelectPaginatedFunction,
  createListActionFunction,
  createPaginatedApiFunction,
  createSearchableApiFunction,
  createSearchableListActionFunction,
  defaultFilterTreeNode,
} from './api-request';

// 导出联动转换器
export { LinkageRuleConverter } from './linkage-converter';

// 导出联动调试工具 - 暂时注释掉，文件不存在
// export {
//   debugLinkageConfig,
//   fixOptionsMapping,
//   generateCorrectLinkageExample,
//   validateLinkageConfig,
// } from './linkage-debug';

// 导出主要转换函数
export {
  isFormBatchSetting,
  quickTransformSearch,
  setFormBatchSetting,
  transformBackendSearchToSchema,
  transformBackendSearchToSchemaWithGrouping,
  transformBackendSearchToSchemaWithoutGrouping,
} from './transform';

// 导出所有类型定义
export type {
  BackendSearchItem,
  BaseCondition,
  ComponentPropsRule,
  ConditionOperator,
  DependencyCondition,
  DirectGroupedSearchData,
  DisabledRule,
  DisplayRule,
  EditPermissionType,
  FieldConfig,
  GroupedSearchData,
  LinkageAssignmentConfig,
  LinkageConfig,
  LinkageRules,
  OptionsRule,
  RequiredRule,
  VisibilityRule,
} from './types';

export { TYPE_TO_COMPONENT_MAP } from './types';

// 导出工具函数
export {
  createDefaultSearchConfig,
  extendTypeMap,
  getSupportedTypes,
  validateSearchData,
} from './utils';
