/**
 * 认证相关配置
 */

/**
 * Token失效跳转配置
 */
export const AUTH_CONFIG = {
  /**
   * Token失效时跳转的页面URL
   * 可以是相对路径或绝对URL
   *
   * 示例：
   * - '/login' - 跳转到当前域名下的登录页面
   * - 'https://auth.example.com/login' - 跳转到其他域名的登录页面
   * - 'https://sso.example.com/login' - 跳转到SSO登录页面
   */
  AUTH_REDIRECT_URL: 'https://auth.georgebuilder.com/login',
} as const;

/**
 * 构建Token失效跳转URL
 * @returns 跳转URL
 */
export function buildAuthRedirectUrl(): string {
  // 直接返回配置的认证跳转URL
  return AUTH_CONFIG.AUTH_REDIRECT_URL;
}
