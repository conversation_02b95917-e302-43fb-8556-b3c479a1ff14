<script setup lang="ts">
/**
 * 通用标签页组件
 *
 * 功能特性：
 * - 支持动态标签页配置，自动处理后端数据
 * - 内置多种组件类型支持（表格、表单、描述等）
 * - 支持懒加载和条件显示
 * - 响应式布局和自定义样式
 * - 完整的事件处理机制
 *
 * 使用场景：
 * - 详情页面的多维度信息展示
 * - 复杂表单的分步骤展示
 * - 数据的分类展示和管理
 */

import type { TabItem, TabsConfig, TabsEvents } from './types';

import {
  computed,
  markRaw,
  nextTick,
  ref,
  shallowRef,
  watch,
  watchEffect,
} from 'vue';

import { Card, TabPane, Tabs } from 'ant-design-vue';

import ApprovalComponent from '#/components/approval-component';
// 导入内置组件
import DetailDescriptions from '#/components/detail-descriptions';
import EditTable from '#/components/edittable/EditTable.vue';
import FollowUpComponent from '#/components/follow-up-component';

// 导出类型供外部使用
export type { TabItem, TabsConfig, TabsEvents } from './types';

// ==================== 组件属性定义 ====================

interface Props extends Omit<TabsConfig, 'items'>, TabsEvents {
  // 推荐使用方式
  /** 后端标签页数据 - 自动处理和转换 */
  backendTabs?: any[];

  /** 表单数据映射 - 各标签页对应的数据 */
  formdatas?: Record<string, any>;

  // 兼容旧版本的属性
  /** @deprecated 已转换的标签页项列表 - 推荐使用 backendTabs */
  items?: TabItem[];

  /** @deprecated 标签页 key 列表 - 推荐使用 backendTabs */
  tabItems?: string[];

  /** @deprecated 公共数据 - 数据现在通过 formdatas 传递 */
  commonData?: Record<string, any>;

  // 配置选项
  /** 自定义标签页配置 */
  customTabConfigs?: Record<string, any>;

  /** 是否启用懒加载 - 提升性能 */
  lazyLoad?: boolean;

  /** 高度模式 - 控制组件高度行为 */
  heightMode?: 'auto' | 'full' | 'viewport';
}

const props = withDefaults(defineProps<Props>(), {
  defaultActiveKey: '',
  tabPosition: 'top',
  type: 'line',
  size: 'middle',
  centered: false,
  hideAdd: true,
  bordered: true,
  lazyLoad: true,
  items: () => [],
  backendTabs: () => [],
  commonData: () => ({}),
  formdatas: () => ({}),
  customTabConfigs: () => ({}),
  heightMode: 'auto',
  tabItems: () => [],
});

const emit = defineEmits<{
  change: [activeKey: string];
  edit: [targetKey: string, action: 'add' | 'remove'];
  tabClick: [key: string, event: MouseEvent];
  tabRendered: [tabKey: string];
  tabScroll: [info: { direction: 'bottom' | 'left' | 'right' | 'top' }];
  'update:activeKey': [activeKey: string];
}>();

// 当前激活的 Tab
const currentActiveKey = ref(props.activeKey || props.defaultActiveKey || '');

// 已渲染的 Tab 集合（用于懒加载）- 使用 shallowRef 优化性能
const renderedTabs = shallowRef<Set<string>>(
  new Set(currentActiveKey.value ? [currentActiveKey.value] : []),
);

// 监听外部 activeKey 变化
watch(
  () => props.activeKey,
  (newKey) => {
    if (newKey && newKey !== currentActiveKey.value) {
      currentActiveKey.value = newKey;
    }
  },
  { immediate: true },
);

// 监听当前激活 Tab 变化
watch(currentActiveKey, (newKey) => {
  // currentActiveKey 变化
  if (newKey) {
    // 无论是否懒加载，都要确保当前激活的 Tab 被添加到渲染列表
    renderedTabs.value.add(newKey);
    // 添加到渲染列表
  }
  emit('update:activeKey', newKey);
});

// ===== 组件注册系统 =====

interface ComponentValidator {
  (props: Record<string, any>): boolean;
}

interface ComponentConfig {
  component: any;
  validator: ComponentValidator;
  propsBuilder?: (tabData: any, commonData: any) => Record<string, any>;
  defaultProps?: Record<string, any>;
}

// 静态组件注册表 - 避免重复计算
const componentRegistry = new Map<string, ComponentConfig>([
  [
    'ApprovalComponent',
    {
      component: markRaw(ApprovalComponent),
      validator: () => true,
      propsBuilder: (tabData) => {
        const tabFormData = props.formdatas?.[tabData.key];
        return {
          data: tabFormData?.data || {},
          canApprove: true,
          currentUserId: 'current-user-id', // 实际应该从用户信息获取
          title: `${tabData.tab || tabData.label || ''} - 审批`,
        };
      },
    },
  ],
  [
    'DetailDescriptions',
    {
      component: markRaw(DetailDescriptions),
      validator: () => true,
      propsBuilder: (tabData) => {
        // 获取对应 tab 的完整数据结构
        const tabFormData = props.formdatas?.[tabData.key];
        const formFields = tabFormData?.form;
        const tabData_data = tabFormData?.data;

        return {
          data: tabData_data || {},
          rawFields: formFields,
          title: tabData.tab || tabData.label || '',
          bordered: false,
          column: 4,
        };
      },
    },
  ],
  [
    'EditTable',
    {
      component: markRaw(EditTable),
      validator: () => true,
      propsBuilder: (tabData) => {
        const tabFormData = props.formdatas?.[tabData.key];
        return {
          params: {
            columns: tabFormData?.form[0].dataItem[0].config.columns || [],
            form: tabFormData?.form[0].dataItem[0].config.form || [],
            tablist: tabFormData?.form[0].dataItem[0].config.tablist || [],
          },
          modelValue: tabFormData?.data || [],
        };
      },
    },
  ],
  [
    'EmptyTab',
    {
      component: 'div',
      validator: () => true,
      propsBuilder: () => ({ class: 'tab-empty-state' }),
    },
  ],
  [
    'FollowUpComponent',
    {
      component: markRaw(FollowUpComponent),
      validator: () => true,
      propsBuilder: (tabData) => {
        const tabFormData = props.formdatas?.[tabData.key];
        return {
          data: tabFormData?.data || [],
          canAdd: true,
          timelineMode: true,
          title: `${tabData.tab || tabData.label || ''} - 跟进`,
        };
      },
    },
  ],
]);

// ===== Tab 数据处理系统 =====

// 根据 templateType 进行组件映射：
// t1 --> ApprovalComponent (审批组件)
// t2 --> DetailDescriptions (详情组件)
// t3 --> EditTable (表格组件)
// t4 --> FollowUpComponent (跟进组件)

// 确定组件类型 - 根据 templateType 映射
function determineComponentType(tabData: any): string {
  const tabKey = tabData.key;
  const tabFormData = props.formdatas?.[tabKey];
  const templateType = tabFormData?.templateType;

  // 只在出现未知 templateType 时输出警告

  // 根据 templateType 判断组件类型
  if (templateType) {
    switch (templateType) {
      case 't1': {
        return 'ApprovalComponent';
      } // 审批组件
      case 't2': {
        return 'DetailDescriptions';
      } // 详情组件
      case 't3': {
        return 'EditTable';
      } // 表格组件
      case 't4': {
        return 'FollowUpComponent';
      } // 跟进组件
      default: {
        // 未知的 templateType，使用默认组件
        return 'DetailDescriptions';
      }
    }
  }

  // 如果没有 templateType，检查是否有数据
  if (tabFormData) {
    // 有数据但没有 templateType，默认使用 DetailDescriptions
    // 没有 templateType，使用默认的 DetailDescriptions
    return 'DetailDescriptions';
  }

  // 没有数据，显示空状态
  return 'EmptyTab';
}

// 构建组件 Props
function buildComponentProps(
  tabData: any,
  commonData: any,
  componentConfig: ComponentConfig,
): Record<string, any> {
  const baseProps = componentConfig.propsBuilder
    ? componentConfig.propsBuilder(tabData, commonData)
    : { data: commonData, tabData };

  const finalProps = {
    ...componentConfig.defaultProps,
    ...baseProps,
  };

  return finalProps;
}

// 处理单个 Tab 项
function processTabItem(tabData: any): TabItem {
  try {
    // 1. 确定组件类型（现在总是返回有效类型）
    const componentType = determineComponentType(tabData);

    // 2. 获取组件配置（现在总是能找到配置）
    const componentConfig = componentRegistry.get(componentType);

    // 3. 构建 props
    const componentProps = buildComponentProps(
      tabData,
      props.commonData,
      componentConfig!,
    );

    // 4. 构建 TabItem（不再进行数据验证，总是创建 Tab）
    const tabItem = {
      key: tabData.key,
      label: tabData.tab || tabData.label || tabData.key,
      component: componentType,
      props: componentProps,
      disabled: tabData.disabled || false,
      closable: tabData.closable || false,
    };

    return tabItem;
  } catch {
    // 处理 Tab 时发生错误
    // 即使出错也返回一个空状态的 Tab
    return {
      key: tabData.key,
      label: tabData.tab || tabData.label || tabData.key,
      component: 'EmptyTab',
      props: {},
      disabled: tabData.disabled || false,
      closable: tabData.closable || false,
    };
  }
}

// 缓存处理结果，避免重复计算
const processedItemsCache = new Map<string, TabItem>();

// 处理 Tab 数据 - 支持多种数据格式，添加缓存优化
const processedItems = computed<TabItem[]>(() => {
  // 新的使用方式：使用 tabItems (key 列表) - 已废弃，使用 backendTabs
  if (props.tabItems && props.tabItems.length > 0) {
    return props.tabItems.map((key) => {
      const cacheKey = `tabItems-${key}`;
      if (processedItemsCache.has(cacheKey)) {
        return processedItemsCache.get(cacheKey)!;
      }
      const tabData = { key, tab: key, label: key };
      const result = processTabItem(tabData);
      processedItemsCache.set(cacheKey, result);
      return result;
    });
  }

  // 如果提供了已转换的 items，直接使用
  if (props.items && props.items.length > 0) {
    return props.items;
  }

  // 如果提供了后端数据，进行转换
  if (props.backendTabs && props.backendTabs.length > 0) {
    return props.backendTabs.map((tabData) => {
      const cacheKey = `backend-${tabData.key}-${JSON.stringify(tabData)}`;
      if (processedItemsCache.has(cacheKey)) {
        return processedItemsCache.get(cacheKey)!;
      }
      const result = processTabItem(tabData);
      processedItemsCache.set(cacheKey, result);
      return result;
    });
  }

  return [];
});

// 过滤可显示的 Tab 项
const visibleItems = computed(() => {
  return processedItems.value.filter((item) => {
    if (item.condition === undefined) return true;
    if (typeof item.condition === 'boolean') return item.condition;
    if (typeof item.condition === 'function') {
      return item.condition(props.commonData);
    }
    return true;
  });
});

// 监听 Tab 数据变化，自动设置第一个为激活状态
watch(
  () => visibleItems.value,
  (newItems) => {
    if (newItems.length > 0) {
      // 如果当前没有激活的 Tab，或者当前激活的 Tab 不在可见列表中
      const currentKeyExists = newItems.some(
        (item) => item.key === currentActiveKey.value,
      );
      if (!currentActiveKey.value || !currentKeyExists) {
        const firstKey = newItems[0]?.key || '';
        currentActiveKey.value = firstKey;
      }
    }
  },
  { immediate: true },
);

// 优化渲染完成监听 - 避免重复触发，添加防抖
const renderedTabsSet = new Set<string>();
let renderCheckTimer: NodeJS.Timeout | null = null;

watchEffect(() => {
  if (visibleItems.value.length > 0) {
    // 清除之前的定时器
    if (renderCheckTimer) {
      clearTimeout(renderCheckTimer);
    }

    // 防抖处理，避免频繁触发
    renderCheckTimer = setTimeout(() => {
      nextTick(() => {
        // 只触发新渲染的 tab 事件，避免重复触发
        visibleItems.value.forEach((item) => {
          if (!renderedTabsSet.has(item.key)) {
            renderedTabsSet.add(item.key);
            emit('tabRendered', item.key);
          }
        });
      });
    }, 50); // 50ms 防抖延迟
  }
});

// 清理缓存的函数
function clearCache() {
  processedItemsCache.clear();
  renderedTabsSet.clear();
}

// 监听数据变化，清理缓存
watch(() => [props.backendTabs, props.formdatas], clearCache, { deep: true });

// ===== 渲染辅助函数 =====

// 获取组件实例
function getComponent(item: TabItem) {
  if (typeof item.component === 'string') {
    const config = componentRegistry.get(item.component);
    const component = config?.component;
    if (!component) {
      // 组件未找到，使用 div 作为回退
      return 'div';
    }
    return component;
  }
  return item.component || 'div';
}

// 检查 Tab 是否应该渲染
function shouldRenderTab(tabKey: string): boolean {
  // 如果不是懒加载模式，总是渲染
  if (!props.lazyLoad) return true;

  // 懒加载模式下，只渲染已访问过的 Tab
  return renderedTabs.value.has(tabKey);
}

// ===== 事件处理 =====

// Tab 切换事件
function handleTabChange(activeKey: number | string) {
  const keyStr = String(activeKey);
  // Tab 切换事件

  // 检查目标 Tab 是否存在
  const targetTab = visibleItems.value.find((item) => item.key === keyStr);
  if (!targetTab) {
    // 目标 Tab 不存在
    return;
  }

  // 更新当前激活的 Tab
  currentActiveKey.value = keyStr;

  // 触发事件
  emit('change', keyStr);
  props.onChange?.(keyStr);
}

// Tab 点击事件
function handleTabClick(
  key: number | string,
  event: KeyboardEvent | MouseEvent,
) {
  const keyStr = String(key);
  emit('tabClick', keyStr, event as MouseEvent);
  props.onTabClick?.(keyStr, event as MouseEvent);
}

// 初始化渲染的 Tabs (使用 watchEffect 确保在数据准备好后执行)
watchEffect(() => {
  if (!props.lazyLoad) {
    visibleItems.value.forEach((item) => {
      renderedTabs.value.add(item.key);
    });
  }
});
</script>

<template>
  <div v-if="visibleItems.length === 0" class="empty-tabs">
    <slot name="empty">
      <div class="empty-content">暂无标签页内容</div>
    </slot>
  </div>

  <div class="general-tabs-wrapper" :class="[`height-mode-${heightMode}`]">
    <Card
      class="general-tabs-card"
      :class="[className]"
      :bordered="bordered"
      v-bind="$attrs"
    >
      <!-- Card 内容区域包含 Tabs -->
      <Tabs
        v-model:active-key="currentActiveKey"
        :tab-position="tabPosition"
        :type="type"
        :size="size"
        :centered="centered"
        :hide-add="hideAdd"
        class="card-inner-tabs"
        @change="handleTabChange"
        @tab-click="handleTabClick"
        :animated="true"
      >
        <TabPane
          v-for="item in visibleItems"
          :key="item.key"
          :tab="item.label"
          :disabled="item.disabled"
          :closable="item.closable"
          :force-render="true"
        >
          <!-- Tab 标题插槽 -->
          <template #tab>
            <slot :name="`${item.key}-tab`" :item="item">
              <span class="tab-title">
                <component v-if="item.icon" :is="item.icon" class="tab-icon" />
                {{ item.label }}
              </span>
            </slot>
          </template>

          <!-- Tab 内容 - 使用 v-show 优化切换性能 -->
          <div
            v-show="shouldRenderTab(item.key)"
            class="tab-content"
            :class="{ 'tab-content-hidden': !shouldRenderTab(item.key) }"
          >
            <slot :name="item.key" :item="item" :data="props.commonData">
              <!-- 空状态组件特殊处理 -->
              <template v-if="item.component === 'EmptyTab'">
                <div class="tab-empty-state">
                  <div class="empty-content">
                    <div class="empty-icon">📄</div>
                    <div class="empty-title">暂无内容</div>
                    <div class="empty-description">
                      该标签页暂时没有可显示的内容
                    </div>
                  </div>
                </div>
              </template>
              <!-- 正常组件渲染 -->
              <template v-else>
                <component :is="getComponent(item)" v-bind="item.props" />
              </template>
            </slot>
          </div>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .card-inner-tabs :deep(.ant-tabs-tab) {
    padding: 8px 12px;
    font-size: 13px;
  }

  .tab-content {
    min-height: 150px;
  }
}

.empty-tabs {
  padding: 40px 20px;
  text-align: center;
}

.empty-content {
  font-size: 14px;
  color: #999;
}

/* 错误状态样式 */
.tab-error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
}

.error-content {
  text-align: center;
}

.error-icon {
  margin-bottom: 16px;
  font-size: 48px;
}

.error-title {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
}

.error-description {
  font-size: 14px;
  color: #999;
}

/* 空状态样式已在文件末尾定义 */

/* 外层包装器 - 基础样式，固定宽度 */
.general-tabs-wrapper {
  box-sizing: border-box; /* 包含padding在宽度计算内 */
  display: flex;
  flex-direction: column;
  width: 100%; /* 固定宽度为父容器的100% */
  max-width: 100%; /* 防止超出父容器 */
  padding: 16px;
}

/* 不同高度模式 */
.height-mode-auto {
  /* 自动高度，根据内容调整 */
  min-height: 300px;
}

.height-mode-full {
  /* 占满父容器高度 */
  height: 100%;
  min-height: 500px;
}

.height-mode-viewport {
  /* 占满视口高度 */
  height: 100vh;
  min-height: 500px;
}

/* Card + Tabs 组合样式 - 固定宽度 */
.general-tabs-card {
  box-sizing: border-box;
  display: flex;
  flex: 1; /* 占满包装器的剩余空间 */
  flex-direction: column;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 0; /* 重要：让 flex 正确工作 */
}

/* Card 内容区域占满剩余高度 - 固定宽度 */
.general-tabs-card :deep(.ant-card-body) {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 0; /* 重要：让 flex 正确工作 */
  padding: 16px;
  overflow: hidden; /* 防止内容撑开容器 */
}

.card-inner-tabs {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 0; /* 重要：让 flex 正确工作 */

  /* 移除 Tabs 的默认边框，因为 Card 已经有边框了 */
  border: none;
}

/* Tabs 内容区域占满剩余高度 - 固定宽度 */
.card-inner-tabs :deep(.ant-tabs-content-holder) {
  box-sizing: border-box;
  flex: 1;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 0; /* 重要：让 flex 正确工作 */
  padding-top: 16px;
  overflow: auto; /* 内容溢出时滚动 */
}

.card-inner-tabs :deep(.ant-tabs-content) {
  box-sizing: border-box;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 100%;
}

.card-inner-tabs :deep(.ant-tabs-tabpane) {
  box-sizing: border-box;
  width: 100%; /* 固定宽度 */
  min-width: 0; /* 允许flex收缩 */
  max-width: 100%; /* 防止超出 */
  height: 100%;
  overflow: auto; /* 内容溢出时滚动 */
}

/* 调整 Tab 标题样式 */
.tab-title {
  display: flex;
  gap: 6px;
  align-items: center;
}

.tab-icon {
  font-size: 14px;
}

/* Tab 内容区域 - 允许内容完整显示 */
.tab-content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0; /* 允许flex收缩 */
  height: 100%;
  min-height: 200px;
  overflow: auto; /* 允许滚动显示完整内容 */
}

/* 隐藏的 Tab 内容 - 优化切换性能 */
.tab-content-hidden {
  position: absolute;
  left: -9999px;
  visibility: hidden;
}

/* Tab 空状态样式 */
.tab-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: 40px 20px;
}

.empty-content-wrapper {
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  opacity: 0.6;
}

.empty-title {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #666;
}

.empty-description {
  max-width: 300px;
  margin: 0 auto;
  font-size: 14px;
  line-height: 1.5;
  color: #999;
}

/* 优化 Card 内 Tabs 的视觉效果 */
.card-inner-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

.card-inner-tabs :deep(.ant-tabs-tab) {
  padding: 12px 16px;
  font-weight: 500;
}

.card-inner-tabs :deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

/* 确保内部组件正确显示 */
.tab-content :deep(*) {
  box-sizing: border-box;
}

/* 特别处理表格组件 - 允许完整显示 */
.tab-content :deep(.ant-table-wrapper) {
  width: 100%;
  overflow-x: auto;
}

.tab-content :deep(.ant-table) {
  width: 100%;

  /* min-width: max-content; 根据内容自动调整宽度 */
}

/* 特别处理描述组件 */
.tab-content :deep(.ant-descriptions) {
  width: 100%;
  max-width: 100%;
}

/* 特别处理表单组件 */
.tab-content :deep(.ant-form) {
  width: 100%;
  max-width: 100%;
}
</style>
