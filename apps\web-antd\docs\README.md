# 项目文档

## 📚 文档目录

### API 管理器

API 管理器是一个全局的 API 调用解决方案，解决了组件间 API 函数传递的问题。

- **[API 管理器完整文档](./api-manager.md)** - 详细的使用指南和 API 参考
- **[快速开始指南](./api-manager-quick-start.md)** - 5 分钟快速上手
- **[API 标准规范](./api-standards.md)** - API 模块开发规范

### 搜索表单转换

搜索表单转换工具用于将后端搜索条件数据转换为前端表单 Schema。

- **[transformBackendSearchToSchema 文档](./transformBackendSearchToSchema.md)** - 搜索表单转换函数详细文档

## 🚀 快速导航

### 新手入门

1. **[快速开始](./api-manager-quick-start.md)** - 立即开始使用 API 管理器
2. **[标准规范](./api-standards.md)** - 了解 API 开发规范
3. **[完整文档](./api-manager.md)** - 深入了解所有功能
4. **[搜索转换](./transformBackendSearchToSchema.md)** - 搜索表单转换工具

### 开发者指南

#### API 管理器

- **核心概念**: 全局 API 管理、自动缓存、类型安全
- **主要优势**: 无需传递 API 函数、统一错误处理、响应式状态
- **使用场景**: 通用页面、自定义组件、表格操作、表单提交

#### 核心功能

| 功能 | 描述 | 文档链接 |
| --- | --- | --- |
| 自动路由匹配 | 根据当前路由自动加载对应的 API | [详细说明](./api-manager.md#自动路由匹配) |
| 智能缓存 | 避免重复加载，提高性能 | [缓存机制](./api-manager.md#缓存机制) |
| 类型安全 | 完整的 TypeScript 支持 | [类型定义](./api-manager.md#类型定义) |
| 错误处理 | 统一的错误处理和用户提示 | [错误处理](./api-manager.md#错误处理) |
| 搜索转换 | 后端搜索条件转前端表单 Schema | [转换文档](./transformBackendSearchToSchema.md) |

## 📖 使用示例

### 基础使用

```typescript
// 在组件中使用
import { useCommonPageApi } from '#/api';

const { list, create, update, delete: deleteItem } = useCommonPageApi();

// 加载数据
const data = await list({ page: 1, pageSize: 20 });

// 创建数据
await create({ name: 'New Item' });
```

### 高级用法

```typescript
// 自定义配置
const { callApi, hasMethod } = useCommonPageApi({
  onLoaded: (api) => {
    console.log('API 加载成功');
  },
  onError: (error) => {
    console.error('API 加载失败:', error);
  },
});

// 调用自定义方法
if (hasMethod('customAction')) {
  await callApi('customAction', params);
}
```

## 🔧 开发工具

### API 模块模板

创建新的 API 模块时，可以使用以下模板：

```typescript
// src/api/common/example/demo.ts
import { requestClient } from '#/api/request';

export async function listAction(params: any) {
  return requestClient.post('/api/demo/list', params);
}

export async function create(data: any) {
  return requestClient.post('/api/demo/create', data);
}

export async function update(id: string | number, data: any) {
  return requestClient.put(`/api/demo/${id}`, data);
}

export async function delete(id: string | number) {
  return requestClient.delete(`/api/demo/${id}`);
}
```

### 检查清单

创建新 API 模块时的检查项：

- [ ] 文件路径与路由匹配
- [ ] 实现了 `listAction` 方法
- [ ] 添加了适当的类型定义
- [ ] 包含错误处理
- [ ] 响应格式符合标准

## 🎯 最佳实践

### 1. 组件设计

- 使用 `useCommonPageApi` 替代手动 API 管理
- 利用 `onLoaded` 回调进行初始化
- 检查 `isApiReady` 状态确保 API 可用

### 2. 错误处理

- 使用 try-catch 包装 API 调用
- 利用内置的错误提示机制
- 提供用户友好的错误信息

### 3. 性能优化

- 利用自动缓存机制
- 按需清除不必要的缓存
- 使用批量操作减少请求次数

## 🔄 迁移指南

### 从旧版本迁移

如果你正在从旧的 API 管理方式迁移，请参考：

1. **[迁移步骤](./api-manager-quick-start.md#迁移步骤)** - 详细的迁移指南
2. **[对比示例](./api-manager-quick-start.md#对比示例)** - 新旧方式对比
3. **[常见问题](./api-manager.md#常见问题)** - 迁移过程中的常见问题

### 迁移检查清单

- [ ] 移除手动 API 加载代码
- [ ] 更新组件导入
- [ ] 替换 API 调用方式
- [ ] 移除不必要的 props 传递
- [ ] 测试所有功能

## 🆘 获取帮助

### 常见问题

1. **API 未加载**: 检查路由路径是否正确
2. **方法不存在**: 确认 API 模块是否正确导出
3. **类型错误**: 检查 TypeScript 类型定义

### 调试技巧

```typescript
// 检查 API 状态
const { isApiReady, error, currentApi } = useCommonPageApi();

console.log('API 状态:', {
  ready: isApiReady.value,
  error: error.value,
  api: currentApi.value,
});

// 检查可用方法
const { hasMethod } = useCommonPageApi();
console.log('listAction 可用:', hasMethod('listAction'));
```

### 联系支持

如果遇到问题：

1. 查看控制台错误信息
2. 检查网络请求状态
3. 确认 API 模块路径和导出
4. 参考文档示例代码

## 📝 更新日志

### v1.0.0 (2024-01-01)

- ✨ 新增 API 管理器核心功能
- ✨ 支持自动路由匹配
- ✨ 添加智能缓存机制
- ✨ 完整的 TypeScript 支持
- 📚 完善的文档和示例

## 🤝 贡献指南

### 贡献代码

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

### 改进文档

1. 发现文档问题或不足
2. 提交 Issue 或直接修改
3. 确保示例代码可运行
4. 保持文档风格一致

---

**感谢使用 API 管理器！** 🎉

如果这个工具对你有帮助，请考虑给项目一个 ⭐️
