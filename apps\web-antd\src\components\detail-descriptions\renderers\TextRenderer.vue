<script setup lang="ts">
/**
 * 文本渲染器
 *
 * 功能特性：
 * - 处理各种数据类型的文本显示
 * - 支持空值占位符
 * - 自动换行处理
 * - 类型安全的值转换
 *
 * 使用场景：
 * - 普通文本字段显示
 * - 数字、布尔值等基础类型显示
 * - 默认渲染器（当没有指定特定类型时）
 */

import { computed } from 'vue';

// ==================== 组件属性定义 ====================

interface Props {
  /** 要显示的值 - 支持任意类型 */
  value: any;

  /** 空值时的占位符文本 */
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '-',
});

// ==================== 数据处理逻辑 ====================

/**
 * 显示值计算
 * 处理空值情况并转换为字符串
 */
const displayValue = computed(() => {
  // 检查空值情况
  if (props.value === null || props.value === undefined || props.value === '') {
    return props.placeholder;
  }

  // 转换为字符串显示
  return String(props.value);
});
</script>

<template>
  <!-- 文本内容显示 -->
  <span class="text-renderer">
    {{ displayValue }}
  </span>
</template>

<style scoped>
/* ==================== 文本渲染器样式 ==================== */

/** 文本渲染器容器 - 支持长文本换行 */
.text-renderer {
  line-height: 1.5;
  word-break: break-all;
  word-wrap: break-word;
}
</style>
