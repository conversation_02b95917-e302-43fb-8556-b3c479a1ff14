/**
 * API 组件值标签解析工具
 * 用于解决编辑模式下 apiselect 等组件只显示值不显示标签的问题
 */

import type { VbenFormSchema } from '#/adapter/form';

/**
 * API 组件值解析器接口
 */
export interface ApiValueResolver {
  /** 字段名 */
  fieldName: string;
  /** API 函数 */
  api: (params?: any) => Promise<any>;
  /** 标签字段名 */
  labelField: string;
  /** 值字段名 */
  valueField: string;
  /** 结果字段名 */
  resultField?: string;
  /** API 参数 */
  params?: Record<string, any>;
}

/**
 * 从表单 schema 中提取 API 组件信息
 * @param schema 表单 schema 数组
 * @returns API 组件解析器数组
 */
export function extractApiResolvers(
  schema: VbenFormSchema[],
): ApiValueResolver[] {
  const resolvers: ApiValueResolver[] = [];

  schema.forEach((item) => {
    if (
      (item.component === 'ApiSelect' || item.component === 'ApiTreeSelect') &&
      item.componentProps?.api
    ) {
      const props = item.componentProps;
      resolvers.push({
        fieldName: item.fieldName,
        api: props.api,
        labelField: props.labelField || 'label',
        valueField: props.valueField || 'value',
        resultField: props.resultField,
        params: props.params || {},
      });
    }
  });

  return resolvers;
}

/**
 * 解析 API 组件的值标签映射
 * @param resolvers API 组件解析器数组
 * @param formValues 表单值对象
 * @returns 值标签映射对象
 */
export async function resolveApiValues(
  resolvers: ApiValueResolver[],
  formValues: Record<string, any>,
): Promise<Record<string, Record<string, string>>> {
  const valueLabelMap: Record<string, Record<string, string>> = {};

  // 并行处理所有 API 请求
  const promises = resolvers.map(async (resolver) => {
    const { fieldName, api, labelField, valueField, resultField, params } =
      resolver;

    // 如果表单中没有这个字段的值，跳过
    if (!(fieldName in formValues) || formValues[fieldName] === null) {
      return;
    }

    try {
      // 调用 API 获取选项数据
      const response = await api(params);
      let options = response;

      // 如果指定了 resultField，从响应中提取数据
      if (resultField) {
        const fields = resultField.split('.');
        for (const field of fields) {
          options = options?.[field];
        }
      }

      // 确保 options 是数组
      if (!Array.isArray(options)) {
        return;
      }

      // 构建值标签映射
      const fieldMap: Record<string, string> = {};
      options.forEach((option) => {
        const value = option[valueField];
        const label = option[labelField];
        if (value !== null && label !== null) {
          fieldMap[String(value)] = String(label);
        }
      });

      valueLabelMap[fieldName] = fieldMap;
    } catch {
      // 静默处理错误，避免影响表单正常使用
    }
  });

  await Promise.all(promises);
  return valueLabelMap;
}

/**
 * 为表单值添加标签信息
 * @param formValues 原始表单值
 * @param valueLabelMap 值标签映射
 * @returns 包含标签信息的表单值
 */
export function enrichFormValuesWithLabels(
  formValues: Record<string, any>,
  valueLabelMap: Record<string, Record<string, string>>,
): Record<string, any> {
  const enrichedValues = { ...formValues };

  Object.keys(valueLabelMap).forEach((fieldName) => {
    const fieldMap = valueLabelMap[fieldName];
    const fieldValue = formValues[fieldName];

    if (fieldValue !== null && fieldMap[String(fieldValue)]) {
      // 为字段添加标签信息（可选，用于调试或其他用途）
      enrichedValues[`${fieldName}_label`] = fieldMap[String(fieldValue)];
    }
  });

  return enrichedValues;
}

/**
 * 智能设置表单值
 * 在设置值之前先解析 API 组件的选项数据，确保能正确显示标签
 * @param formApi 表单 API 实例
 * @param schema 表单 schema
 * @param values 要设置的值
 */
export async function setFormValuesWithApiResolution(
  formApi: any,
  schema: VbenFormSchema[],
  values: Record<string, any>,
): Promise<void> {
  if (!values || Object.keys(values).length === 0) {
    return;
  }

  // 提取 API 组件解析器
  const resolvers = extractApiResolvers(schema);

  if (resolvers.length === 0) {
    // 没有 API 组件，直接设置值
    await formApi.setValues(values);
    return;
  }

  try {
    // 解析 API 组件的值标签映射
    const valueLabelMap = await resolveApiValues(resolvers, values);

    // 设置表单值
    await formApi.setValues(values);

    // 可选：为调试目的记录带标签的值
    enrichFormValuesWithLabels(values, valueLabelMap);
  } catch {
    // 如果解析失败，仍然设置原始值
    await formApi.setValues(values);
  }
}
