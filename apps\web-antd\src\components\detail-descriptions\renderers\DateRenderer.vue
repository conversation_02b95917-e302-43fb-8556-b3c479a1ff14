<script setup lang="ts">
import { computed } from 'vue';

import dayjs from 'dayjs';

interface Props {
  value: any;
  format?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  format: 'YYYY-MM-DD HH:mm:ss',
  placeholder: '-',
});

const formattedDate = computed(() => {
  if (!props.value) {
    return props.placeholder;
  }

  try {
    const date = dayjs(props.value);
    if (!date.isValid()) {
      return props.placeholder;
    }
    return date.format(props.format);
  } catch {
    return props.placeholder;
  }
});
</script>

<template>
  <span class="date-renderer">
    {{ formattedDate }}
  </span>
</template>

<style scoped>
.date-renderer {
  color: #262626;
}
</style>
