# transformColumns PHP 后端数据规范文档

## 概述

本文档为 PHP 后端开发者提供 `transformColumns` 函数所需的数据格式规范。该函数负责将后端返回的列配置数据转换为前端 VXE Table 组件所需的格式。

## 函数签名

```typescript
transformColumns(
  backendColumns: any[],      // 后端列配置数组
  tableData?: any[],          // 表格数据（用于检测行级按钮）
  options?: {                 // 可选配置
    actionColumnConfig?: any;
    autoAddActionColumn?: boolean;
  }
)
```

## 核心数据结构

### 1. 后端列配置 (backendColumns)

#### 基础列配置

```php
<?php
// PHP 数组格式
$backendColumns = [
    [
        'field' => 'id',                    // 必填：字段名
        'title' => 'ID',                   // 必填：列标题
        'width' => 80,                     // 可选：列宽度（像素）
        'type' => 'text',                  // 可选：列类型
        'align' => 'center',               // 可选：对齐方式 left|center|right
        'fixed' => 'left',                 // 可选：固定列 left|right|false
        'sortable' => true,                // 可选：是否可排序
        'minWidth' => 60,                  // 可选：最小宽度
        'maxWidth' => 300,                 // 可选：最大宽度
        'resizable' => true,               // 可选：是否可调整大小
        'visible' => true,                 // 可选：是否显示（默认true）
    ],
    [
        'field' => 'name',
        'title' => '姓名',
        'width' => 120,
        'type' => 'text',
        'sortable' => true,
    ],
    // ... 更多列配置
];
```

#### 支持的列类型 (type)

| 类型          | 说明         | 示例用途           |
| ------------- | ------------ | ------------------ |
| `text`        | 普通文本列   | 姓名、标题等       |
| `text_tag`    | 文本标签列   | 带颜色的状态标签   |
| `radio`       | 单选列       | 状态选择           |
| `edit_radio`  | 可编辑单选列 | 行内编辑状态       |
| `celltag`     | 单元格标签列 | 远程选择标签       |
| `multiselect` | 多选列       | 多个选项显示       |
| `dept`        | 部门列       | 部门层级显示       |
| `person`      | 人员列       | 人员信息（含头像） |
| `files`       | 文件列       | 文件附件显示       |
| `action`      | 操作列       | 操作按钮（动态）   |
| `actions`     | 操作列       | 操作按钮（固定）   |
| `checkbox`    | 复选框列     | 表格多选           |

### 2. 带选项的列配置

```php
<?php
// 状态列示例
$statusColumn = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'text_tag',
    'width' => 100,
    'options' => [
        ['label' => '启用', 'value' => 1, 'color' => 'green'],
        ['label' => '禁用', 'value' => 0, 'color' => 'red'],
        ['label' => '待审核', 'value' => 2, 'color' => 'orange'],
    ],
    // 或者对象格式
    'options' => [
        '1' => ['label' => '启用', 'color' => 'green'],
        '0' => ['label' => '禁用', 'color' => 'red'],
    ],
];

// 部门列示例
$deptColumn = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'dept',
    'width' => 150,
    'options' => [
        ['label' => '技术部', 'value' => 1, 'parent_id' => null],
        ['label' => '前端组', 'value' => 11, 'parent_id' => 1],
        ['label' => '后端组', 'value' => 12, 'parent_id' => 1],
        ['label' => '销售部', 'value' => 2, 'parent_id' => null],
    ],
    'multiple' => false,        // 是否多选
    'showPath' => true,         // 是否显示路径
];

// 人员列示例
$personColumn = [
    'field' => 'creator_id',
    'title' => '创建人',
    'type' => 'person',
    'width' => 120,
    'options' => [
        '1' => [
            'label1' => '张三',
            'avatar' => '/avatars/zhangsan.jpg'
        ],
        '2' => [
            'label1' => '李四',
            'avatar' => '/avatars/lisi.jpg'
        ],
    ],
    'multiple' => false,        // 是否多选
    'showAvatar' => true,       // 是否显示头像
    'avatarField' => 'avatar',  // 头像字段名
    'nameField' => 'label1',    // 姓名字段名
];
```

### 3. 表格数据 (tableData) 与操作按钮

```php
<?php
// 表格数据示例
$tableData = [
    [
        'id' => 1,
        'name' => '张三',
        'status' => 1,
        'department_id' => 11,
        'creator_id' => 1,
        'created_at' => '2024-01-01 10:00:00',

        // 行级操作按钮配置
        'actionBtnList' => [
            [
                'title' => '查看',          // 必填：按钮文本
                'type' => 'view',           // 必填：按钮类型
                'key' => 'view',            // 可选：按钮标识
                'icon' => 'eye',            // 可选：图标
                'color' => 'primary',       // 可选：颜色
                'permission' => 'user.view', // 可选：权限控制
            ],
            [
                'title' => '编辑',
                'type' => 'edit',
                'key' => 'edit',
                'icon' => 'edit',
                'color' => 'warning',
                'permission' => 'user.edit',
            ],
            [
                'title' => '删除',
                'type' => 'delete',
                'key' => 'delete',
                'icon' => 'delete',
                'color' => 'danger',
                'permission' => 'user.delete',
            ],
            [
                'title' => '审核',
                'type' => 'approve',
                'key' => 'approve',
                'icon' => 'check',
            ],
            [
                'title' => '导出',
                'type' => 'export',
                'key' => 'export',
                'icon' => 'download',
            ],
        ],
    ],
    [
        'id' => 2,
        'name' => '李四',
        'status' => 0,
        'department_id' => 12,
        'creator_id' => 2,
        'created_at' => '2024-01-02 14:30:00',
        'actionBtnList' => [
            [
                'title' => '查看',
                'type' => 'view',
                'key' => 'view',
            ],
            [
                'title' => '启用',
                'type' => 'enable',
                'key' => 'enable',
                'color' => 'success',
            ],
        ],
    ],
];
```

### 4. 操作列配置详解

#### 4.1 动态操作列 (action 类型)

适用于普通表格，支持每行不同的动态按钮：

```php
<?php
// 动态操作列配置
$actionColumn = [
    'field' => 'action',
    'title' => '操作',
    'type' => 'action',
    'width' => 200,
    'fixed' => 'right',
    'align' => 'center',
    'defaultActionButtons' => [
        [
            'type' => 'edit',
            'title' => '编辑',
            'icon' => 'EditOutlined'
        ],
        [
            'type' => 'delete',
            'title' => '删除',
            'icon' => 'DeleteOutlined'
        ]
    ]
];

// 表格数据中的行级按钮
$tableData = [
    [
        'id' => 1,
        'name' => '张三',
        'actionBtnList' => [
            ['title' => '查看', 'type' => 'view', 'key' => 'view'],
            ['title' => '编辑', 'type' => 'edit', 'key' => 'edit'],
            ['title' => '删除', 'type' => 'delete', 'key' => 'delete'],
        ]
    ]
];
```

#### 4.2 固定操作列 (actions 类型)

适用于 edittable 组件，使用固定的操作按钮配置：

```php
<?php
// 固定操作列配置
$actionsColumn = [
    'title' => '操作',
    'dataIndex' => 'actions',
    'key' => 'actions',
    'width' => 150,
    'fixed' => 'right',
    'editable' => false,
    'type' => 'actions',
    'actions' => [
        [
            'type' => 'edit',
            'title' => '编辑',
            'icon' => 'EditOutlined'
        ],
        [
            'type' => 'delete',
            'title' => '删除',
            'icon' => 'DeleteOutlined',
            'popconfirm' => [
                'title' => '确定要删除这条记录吗？',
                'okText' => '确定',
                'cancelText' => '取消'
            ]
        ],
        [
            'type' => 'custom',
            'title' => '复制',
            'icon' => 'CopyOutlined',
            'code' => 'copy_row'
        ]
    ]
];
```

#### 4.3 操作按钮配置详解

```php
<?php
// 操作按钮完整配置示例
$actionButton = [
    'type' => 'edit',                    // 必填：按钮类型
    'title' => '编辑',                   // 必填：按钮文本
    'key' => 'edit',                     // 可选：按钮标识
    'icon' => 'EditOutlined',            // 可选：图标名称
    'color' => 'primary',                // 可选：按钮颜色
    'code' => 'edit_user',               // 可选：自定义操作代码
    'permission' => 'user.edit',         // 可选：权限控制
    'popconfirm' => [                    // 可选：确认弹窗
        'title' => '确定要执行此操作吗？',
        'okText' => '确定',
        'cancelText' => '取消'
    ],
    'disabled' => false,                 // 可选：是否禁用
    'loading' => false,                  // 可选：是否显示加载状态
    'tooltip' => '编辑用户信息',         // 可选：提示信息
];

// 支持的按钮类型
$buttonTypes = [
    'view',      // 查看
    'edit',      // 编辑
    'delete',    // 删除
    'add',       // 添加
    'copy',      // 复制
    'export',    // 导出
    'import',    // 导入
    'approve',   // 审核
    'reject',    // 拒绝
    'enable',    // 启用
    'disable',   // 禁用
    'custom',    // 自定义（需要指定 code）
];

// 支持的按钮颜色
$buttonColors = [
    'primary',   // 主色调（蓝色）
    'success',   // 成功（绿色）
    'warning',   // 警告（橙色）
    'danger',    // 危险（红色）
    'info',      // 信息（浅蓝色）
    'default',   // 默认（灰色）
];
```

## 完整的 PHP 后端响应示例

```php
<?php
// 控制器方法示例
class UserController
{
    public function list(Request $request)
    {
        // 列配置
        $columns = [
            [
                'field' => 'id',
                'title' => 'ID',
                'width' => 80,
                'type' => 'text',
                'fixed' => 'left',
                'sortable' => true,
            ],
            [
                'field' => 'avatar',
                'title' => '头像',
                'width' => 80,
                'type' => 'person',
                'showAvatar' => true,
            ],
            [
                'field' => 'name',
                'title' => '姓名',
                'width' => 120,
                'type' => 'text',
                'sortable' => true,
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'width' => 200,
                'type' => 'text',
            ],
            [
                'field' => 'department_id',
                'title' => '部门',
                'width' => 150,
                'type' => 'dept',
                'options' => $this->getDepartmentOptions(),
            ],
            [
                'field' => 'status',
                'title' => '状态',
                'width' => 100,
                'type' => 'text_tag',
                'options' => [
                    ['label' => '在职', 'value' => 1, 'color' => 'green'],
                    ['label' => '离职', 'value' => 0, 'color' => 'red'],
                ],
            ],
            [
                'field' => 'created_at',
                'title' => '创建时间',
                'width' => 160,
                'type' => 'text',
            ],
        ];

        // 获取数据
        $users = User::with(['department'])->paginate(20);

        // 构建表格数据
        $items = $users->items();
        foreach ($items as &$item) {
            // 添加操作按钮
            $item['actionBtnList'] = $this->buildActionButtons($item);
        }

        return response()->json([
            'nav' => [
                'columns' => $columns,
                'title' => '用户管理',
                'showCreateBtn' => true,
                'rowSelection' => true,
            ],
            'items' => $items,
            'total' => $users->total(),
        ]);
    }

    private function buildActionButtons($user)
    {
        $buttons = [
            [
                'title' => '查看',
                'type' => 'view',
                'key' => 'view',
                'icon' => 'eye',
            ],
        ];

        // 根据权限和状态动态添加按钮
        if (auth()->user()->can('user.edit')) {
            $buttons[] = [
                'title' => '编辑',
                'type' => 'edit',
                'key' => 'edit',
                'icon' => 'edit',
            ];
        }

        if (auth()->user()->can('user.delete') && $user['status'] == 0) {
            $buttons[] = [
                'title' => '删除',
                'type' => 'delete',
                'key' => 'delete',
                'icon' => 'delete',
                'color' => 'danger',
            ];
        }

        if ($user['status'] == 1) {
            $buttons[] = [
                'title' => '禁用',
                'type' => 'disable',
                'key' => 'disable',
                'color' => 'warning',
            ];
        } else {
            $buttons[] = [
                'title' => '启用',
                'type' => 'enable',
                'key' => 'enable',
                'color' => 'success',
            ];
        }

        return $buttons;
    }

    // edittable 组件的列配置示例
    public function getEditTableColumns()
    {
        return [
            [
                'field' => 'product_name',
                'title' => '商品名称',
                'type' => 'text',
                'width' => 200,
                'editable' => true
            ],
            [
                'field' => 'quantity',
                'title' => '数量',
                'type' => 'number',
                'width' => 100,
                'editable' => true
            ],
            [
                'field' => 'price',
                'title' => '单价',
                'type' => 'number',
                'width' => 120,
                'editable' => true
            ],
            [
                'field' => 'amount',
                'title' => '金额',
                'type' => 'number',
                'width' => 120,
                'editable' => false
            ],
            // 固定操作列配置
            [
                'title' => '操作',
                'dataIndex' => 'actions',
                'key' => 'actions',
                'width' => 150,
                'fixed' => 'right',
                'editable' => false,
                'type' => 'actions',
                'actions' => [
                    [
                        'type' => 'edit',
                        'title' => '编辑',
                        'icon' => 'EditOutlined'
                    ],
                    [
                        'type' => 'delete',
                        'title' => '删除',
                        'icon' => 'DeleteOutlined',
                        'popconfirm' => [
                            'title' => '确定要删除这条记录吗？',
                            'okText' => '确定',
                            'cancelText' => '取消'
                        ]
                    ],
                    [
                        'type' => 'custom',
                        'title' => '复制',
                        'icon' => 'CopyOutlined',
                        'code' => 'copy_row'
                    ]
                ]
            ]
        ];
    }

    private function getDepartmentOptions()
    {
        return Department::all()->map(function ($dept) {
            return [
                'label' => $dept->name,
                'value' => $dept->id,
                'parent_id' => $dept->parent_id,
            ];
        })->toArray();
    }
}
```

## 操作按钮智能分组机制

当操作按钮数量超过 3 个时，系统会自动进行智能分组：

- **主要按钮**：前 3 个按钮直接显示
- **次要按钮**：超过 3 个的按钮放入下拉菜单

```php
<?php
// 按钮数量 > 3 的示例
$actionBtnList = [
    ['title' => '查看', 'type' => 'view', 'key' => 'view'],
    ['title' => '编辑', 'type' => 'edit', 'key' => 'edit'],
    ['title' => '删除', 'type' => 'delete', 'key' => 'delete'],
    // 以下按钮会自动放入下拉菜单
    ['title' => '审核', 'type' => 'approve', 'key' => 'approve'],
    ['title' => '导出', 'type' => 'export', 'key' => 'export'],
    ['title' => '打印', 'type' => 'print', 'key' => 'print'],
];
```

## 特殊配置选项

### 1. 操作列自定义配置

```php
<?php
// 在 options 参数中配置操作列
$options = [
    'actionColumnConfig' => [
        'width' => 300,           // 操作列宽度
        'title' => '操作',        // 操作列标题
        'fixed' => 'right',       // 固定在右侧
        'align' => 'center',      // 对齐方式
    ],
    'autoAddActionColumn' => true,  // 是否自动添加操作列
];
```

### 2. 多选列配置

```php
<?php
$multiselectColumn = [
    'field' => 'tags',
    'title' => '标签',
    'type' => 'multiselect',
    'width' => 200,
    'options' => [
        ['label' => 'VIP', 'value' => 'vip'],
        ['label' => '新用户', 'value' => 'new'],
        ['label' => '活跃用户', 'value' => 'active'],
    ],
    'separator' => ', ',        // 分隔符
    'maxDisplay' => 3,          // 最大显示数量
    'showCount' => true,        // 是否显示总数
];
```

### 3. 文件列配置

```php
<?php
$filesColumn = [
    'field' => 'attachments',
    'title' => '附件',
    'type' => 'files',
    'width' => 150,
];

// 对应的数据格式
$rowData = [
    'attachments' => [
        [
            'name' => '文档.pdf',
            'url' => '/uploads/document.pdf',
            'size' => '1.2MB',
            'type' => 'pdf',
        ],
        [
            'name' => '图片.jpg',
            'url' => '/uploads/image.jpg',
            'size' => '500KB',
            'type' => 'image',
        ],
    ],
];
```

## 数据验证规则

### 必填字段验证

```php
<?php
// 列配置验证
function validateColumnConfig($column) {
    $required = ['field', 'title'];

    foreach ($required as $field) {
        if (!isset($column[$field]) || empty($column[$field])) {
            throw new InvalidArgumentException("Column field '{$field}' is required");
        }
    }

    // 类型验证
    $validTypes = [
        'text', 'text_tag', 'radio', 'edit_radio', 'celltag',
        'multiselect', 'dept', 'person', 'files', 'action', 'checkbox'
    ];

    if (isset($column['type']) && !in_array($column['type'], $validTypes)) {
        throw new InvalidArgumentException("Invalid column type: {$column['type']}");
    }

    return true;
}

// 操作按钮验证
function validateActionButton($button) {
    $required = ['title', 'type'];

    foreach ($required as $field) {
        if (!isset($button[$field]) || empty($button[$field])) {
            throw new InvalidArgumentException("Button field '{$field}' is required");
        }
    }

    return true;
}
```

## 常见问题与解决方案

### 1. 操作列不显示

**问题**：配置了 actionBtnList 但操作列不显示

**解决方案**：

```php
<?php
// 确保每行数据都包含 actionBtnList
foreach ($items as &$item) {
    if (!isset($item['actionBtnList'])) {
        $item['actionBtnList'] = [];
    }
}

// 或者在列配置中明确添加操作列
$columns[] = [
    'field' => 'action',
    'title' => '操作',
    'type' => 'action',
    'width' => 200,
    'fixed' => 'right',
];
```

### 2. 选项数据不显示

**问题**：配置了 options 但选项不显示正确的标签

**解决方案**：

```php
<?php
// 确保选项格式正确
$options = [
    // 数组格式
    ['label' => '启用', 'value' => 1],
    ['label' => '禁用', 'value' => 0],

    // 或对象格式
    '1' => ['label' => '启用'],
    '0' => ['label' => '禁用'],
];

// 确保数据值与选项值匹配
$rowData = [
    'status' => 1,  // 对应选项中的 value
];
```

### 3. 人员列头像不显示

**问题**：人员列配置了头像但不显示

**解决方案**：

```php
<?php
// 确保使用正确的数据格式
$personOptions = [
    '1' => [
        'label1' => '张三',           // 姓名字段
        'avatar' => '/path/to/avatar.jpg',  // 头像字段
    ],
];

// 确保字段名配置正确
$personColumn = [
    'field' => 'creator_id',
    'title' => '创建人',
    'type' => 'person',
    'options' => $personOptions,
    'showAvatar' => true,
    'avatarField' => 'avatar',    // 头像字段名
    'nameField' => 'label1',      // 姓名字段名
];
```

## 性能优化建议

### 1. 选项数据缓存

```php
<?php
// 使用缓存避免重复查询
class ColumnOptionsCache
{
    public static function getDepartmentOptions()
    {
        return Cache::remember('dept_options', 3600, function () {
            return Department::all()->map(function ($dept) {
                return [
                    'label' => $dept->name,
                    'value' => $dept->id,
                    'parent_id' => $dept->parent_id,
                ];
            })->toArray();
        });
    }

    public static function getUserOptions()
    {
        return Cache::remember('user_options', 1800, function () {
            return User::all()->mapWithKeys(function ($user) {
                return [$user->id => [
                    'label1' => $user->name,
                    'avatar' => $user->avatar_url,
                ]];
            })->toArray();
        });
    }
}
```

### 2. 按钮权限优化

```php
<?php
// 批量检查权限避免重复查询
class ActionButtonBuilder
{
    private $userPermissions;

    public function __construct()
    {
        $this->userPermissions = auth()->user()->getAllPermissions()->pluck('name')->toArray();
    }

    public function buildButtons($item)
    {
        $buttons = [];

        // 基础按钮
        $buttons[] = ['title' => '查看', 'type' => 'view', 'key' => 'view'];

        // 权限控制的按钮
        if (in_array('user.edit', $this->userPermissions)) {
            $buttons[] = ['title' => '编辑', 'type' => 'edit', 'key' => 'edit'];
        }

        if (in_array('user.delete', $this->userPermissions)) {
            $buttons[] = ['title' => '删除', 'type' => 'delete', 'key' => 'delete'];
        }

        return $buttons;
    }
}
```

## 总结

本文档提供了 `transformColumns` 函数所需的完整数据格式规范，包括：

1. **基础列配置**：字段名、标题、宽度、类型等基本属性
2. **特殊列类型**：支持 11 种不同的列类型，满足各种显示需求
3. **操作按钮配置**：支持动态按钮、权限控制、智能分组
4. **选项数据处理**：支持多种选项格式，适应不同数据结构
5. **性能优化**：提供缓存和权限优化建议

遵循本规范可以确保前后端数据交互的一致性和系统的稳定性。

## 操作列类型对比

### action vs actions 类型对比

| 特性         | action 类型（单数）        | actions 类型（复数） |
| ------------ | -------------------------- | -------------------- |
| **使用场景** | 普通表格                   | edittable 组件       |
| **按钮来源** | 行数据中的 `actionBtnList` | 列配置中的 `actions` |
| **动态性**   | 支持每行不同按钮           | 所有行使用相同按钮   |
| **配置位置** | 表格数据中                 | 列配置中             |
| **插槽名称** | `action`                   | `actions`            |
| **权限控制** | 在行数据中配置             | 在列配置中配置       |

### 选择建议

- **使用 `action` 类型**：当需要根据行数据动态显示不同按钮时
- **使用 `actions` 类型**：当所有行使用相同的操作按钮时，特别是在 edittable 组件中

### 完整的 edittable 配置示例

```php
<?php
// 用于 transformBackendSearchToSchema 的 edittable 配置
$edittableField = [
    'field' => 'order_details',
    'type' => 'edittable',
    'title' => '订单明细',
    'config' => [
        'height' => '400px',
        'columns' => [
            [
                'field' => 'product_name',
                'title' => '商品名称',
                'type' => 'text',
                'width' => 200,
                'editable' => true
            ],
            [
                'field' => 'quantity',
                'title' => '数量',
                'type' => 'number',
                'width' => 100,
                'editable' => true
            ],
            [
                'field' => 'price',
                'title' => '单价',
                'type' => 'number',
                'width' => 120,
                'editable' => true
            ],
            // 操作列配置（actions 类型）
            [
                'title' => '操作',
                'dataIndex' => 'actions',
                'key' => 'actions',
                'width' => 150,
                'fixed' => 'right',
                'editable' => false,
                'type' => 'actions',
                'actions' => [
                    [
                        'type' => 'edit',
                        'title' => '编辑',
                        'icon' => 'EditOutlined'
                    ],
                    [
                        'type' => 'delete',
                        'title' => '删除',
                        'icon' => 'DeleteOutlined',
                        'popconfirm' => [
                            'title' => '确定要删除这条记录吗？',
                            'okText' => '确定',
                            'cancelText' => '取消'
                        ]
                    ]
                ]
            ]
        ],
        'tabList' => [
            [
                'id' => 1,
                'product_name' => '商品A',
                'quantity' => 2,
                'price' => 100.00
            ]
        ]
    ]
];
```
