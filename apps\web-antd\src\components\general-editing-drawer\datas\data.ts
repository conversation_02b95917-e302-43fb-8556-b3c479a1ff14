/**
 * 编辑抽屉组件的数据处理和配置文件
 * 用于优化代码结构，提取常量、类型定义和工具函数
 */

import type { VbenFormSchema } from '#/adapter/form';

import dayjs from 'dayjs';

// ==================== 类型定义 ====================

/**
 * 编辑模式类型
 */
export type EditMode = 'create' | 'edit';

/**
 * 抽屉数据接口
 */
export interface DrawerData {
  schema: any[];
  apiFn: any;
  params: Record<string, any>;
  mode: EditMode;
  row: Record<string, any>;
}

/**
 * 表单数据处理配置
 */
export interface FormDataProcessConfig {
  /** 字段名 */
  fieldName: string;
  /** 组件类型 */
  component: string;
  /** 组件属性 */
  componentProps?: Record<string, any>;
}

/**
 * 日期范围字段映射配置
 */
export interface RangePickerFieldMapping {
  /** RangePicker 字段名 */
  fieldName: string;
  /** 开始字段名 */
  startField: string;
  /** 结束字段名 */
  endField: string;
  /** 开始日期格式 */
  startFormat: string;
  /** 结束日期格式 */
  endFormat: string;
}

// ==================== 常量定义 ====================

/**
 * 默认日期格式
 */
export const DEFAULT_DATE_FORMATS = {
  START: 'YYYY-MM-DD 00:00:00',
  END: 'YYYY-MM-DD 23:59:59',
  STANDARD: 'YYYY-MM-DD HH:mm:ss',
} as const;

/**
 * 需要字符串转换的组件类型（输入框类型）
 */
export const STRING_CONVERSION_COMPONENTS = [
  'Input',
  'InputPassword',
  'Textarea',
] as const;

/**
 * 需要数字转换的组件类型（除了输入框外的其他组件）
 */
export const NUMBER_CONVERSION_COMPONENTS = [
  'Select',
  'ApiSelect',
  'RadioGroup',
  'InputNumber',
  'Rate',
  'Slider',
  'Switch',
] as const;

/**
 * 需要数组格式的组件类型
 */
export const ARRAY_FORMAT_COMPONENTS = [
  'ApiTreeSelect',
  'TreeSelect',
  'EditTable',
] as const;

/**
 * 日期相关组件类型
 */
export const DATE_COMPONENTS = [
  'DatePicker',
  'RangePicker',
  'TimePicker',
] as const;

/**
 * 组件初始化延迟时间（毫秒）
 */
export const COMPONENT_INIT_DELAYS = {
  /** API组件加载延迟 */
  API_LOAD: 0,
  /** 字段恢复延迟 */
  FIELD_RESTORE: 200,
  /** 组件完全初始化延迟 */
  FULL_INIT: 300,
} as const;

// ==================== 工具函数 ====================

/**
 * 检查值是否为空
 */
export function isEmpty(value: any): boolean {
  return value === undefined || value === null || value === '';
}

/**
 * 检查值是否有效（非空且非undefined）
 */
export function isValidValue(value: any): boolean {
  return !isEmpty(value);
}

/**
 * 安全的字符串转换
 */
export function safeStringConversion(value: any): string {
  if (isEmpty(value)) return '';
  return String(value);
}

/**
 * 安全的数组转换
 */
export function safeArrayConversion(value: any): any[] {
  if (Array.isArray(value)) return value;
  if (isEmpty(value)) return [];
  return [value];
}

/**
 * 安全的数字转换
 */
export function safeNumberConversion(value: any): number | string {
  if (isEmpty(value)) return value;

  // 如果已经是数字，直接返回
  if (typeof value === 'number') return value;

  // 尝试转换为数字
  const numValue = Number(value);

  // 如果转换成功且不是NaN，返回数字
  if (!Number.isNaN(numValue) && Number.isFinite(numValue)) {
    return numValue;
  }

  // 转换失败，返回原值
  return value;
}

/**
 * 检查组件是否需要字符串转换
 */
export function needsStringConversion(component: string): boolean {
  return STRING_CONVERSION_COMPONENTS.includes(component as any);
}

/**
 * 检查组件是否需要数字转换
 */
export function needsNumberConversion(component: string): boolean {
  return NUMBER_CONVERSION_COMPONENTS.includes(component as any);
}

/**
 * 检查组件是否需要数组格式
 */
export function needsArrayFormat(component: string): boolean {
  return ARRAY_FORMAT_COMPONENTS.includes(component as any);
}

/**
 * 检查组件是否为日期类型
 */
export function isDateComponent(component: string): boolean {
  return DATE_COMPONENTS.includes(component as any);
}

/**
 * 安全的日期转换
 */
export function safeDateConversion(value: string): dayjs.Dayjs | null {
  if (isEmpty(value) || typeof value !== 'string') return null;

  try {
    const dayjsDate = dayjs(value);
    return dayjsDate.isValid() ? dayjsDate : null;
  } catch (error) {
    console.warn('Failed to parse date:', value, error);
    return null;
  }
}

/**
 * 安全的日期范围转换
 */
export function safeDateRangeConversion(
  value: any[],
): [dayjs.Dayjs | null, dayjs.Dayjs | null] | null {
  if (!Array.isArray(value) || value.length !== 2) return null;

  try {
    const [startDate, endDate] = value;
    const startDayjs = startDate ? safeDateConversion(startDate) : null;
    const endDayjs = endDate ? safeDateConversion(endDate) : null;

    return [startDayjs, endDayjs];
  } catch (error) {
    console.warn('Failed to parse date range:', value, error);
    return null;
  }
}

/**
 * 处理EditTable数据格式
 */
export function processEditTableData(value: any): any[] {
  if (Array.isArray(value)) return value;
  if (isEmpty(value)) return [];

  // 尝试从对象中提取数组
  if (typeof value === 'object') {
    if (value.list && Array.isArray(value.list)) return value.list;
    if (value.data && Array.isArray(value.data)) return value.data;
  }

  return [];
}

/**
 * 处理TreeSelect多选数据
 */
export function processTreeSelectData(
  value: any,
  componentProps: Record<string, any> = {},
): any {
  const isMultipleMode =
    componentProps.multiple || componentProps.treeCheckable;

  if (!isMultipleMode) return value;

  // 多选模式下确保值是数组格式
  if (isValidValue(value) && !Array.isArray(value)) {
    return [value];
  }

  return isEmpty(value) ? [] : value;
}

/**
 * 生成RangePicker字段映射配置
 */
export function generateRangePickerMapping(
  fieldName: string,
  componentProps: Record<string, any> = {},
): [string, string[], string[]] {
  const startField = componentProps.startField || `${fieldName}_start`;
  const endField = componentProps.endField || `${fieldName}_end`;
  const startFormat = componentProps.formatstart || DEFAULT_DATE_FORMATS.START;
  const endFormat = componentProps.formatend || DEFAULT_DATE_FORMATS.END;

  return [fieldName, [startField, endField], [startFormat, endFormat]];
}

/**
 * 处理创建模式下的字段禁用状态
 */
export function processCreateModeFields(
  schema: VbenFormSchema[],
): VbenFormSchema[] {
  return schema.map((schemaItem) => {
    const item = { ...schemaItem };

    // 确保组件属性对象存在
    item.componentProps = item.componentProps ? { ...item.componentProps } : {};

    // 在创建模式下，将禁用的字段设为可用
    if (item.componentProps.disabled === true) {
      item.componentProps.disabled = false;
    }
    if (item.disabled === true) {
      item.disabled = false;
    }

    return item;
  });
}

/**
 * 提取schema中的默认值
 */
export function extractDefaultValues(
  schema: VbenFormSchema[],
): Record<string, any> {
  const defaultValues: Record<string, any> = {};

  schema.forEach((schemaItem) => {
    if (schemaItem.defaultValue !== undefined && schemaItem.fieldName) {
      defaultValues[schemaItem.fieldName] = schemaItem.defaultValue;
    }
  });

  return defaultValues;
}

/**
 * 查找需要恢复的字段
 */
export function findFieldsToRestore(
  originalData: Record<string, any>,
  currentValues: Record<string, any>,
  schemaFieldNames: Set<string>,
): Record<string, any> {
  const fieldsToRestore: Record<string, any> = {};

  for (const [key, originalValue] of Object.entries(originalData)) {
    // 只处理表单 schema 中定义的字段
    if (!schemaFieldNames.has(key)) continue;

    const currentValue = currentValues[key];
    if (isValidValue(originalValue) && isEmpty(currentValue)) {
      fieldsToRestore[key] = originalValue;
    }
  }

  return fieldsToRestore;
}

/**
 * 获取schema字段名集合
 */
export function getSchemaFieldNames(schema: VbenFormSchema[]): Set<string> {
  return new Set(schema.map((item) => item.fieldName).filter(Boolean));
}

/**
 * 创建延迟Promise
 */
export function createDelay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 错误处理工具
 */
export function handleError(error: unknown, context: string = ''): string {
  const errorMessage = error instanceof Error ? error.message : '未知错误';
  const fullMessage = context ? `${context}：${errorMessage}` : errorMessage;

  console.error('EditingDrawer Error:', { context, error });
  return fullMessage;
}

// ==================== 数据处理器类 ====================

/**
 * 应该保持字符串格式的字段名列表
 * 这些字段即使包含数字，也不应该被转换为数字类型
 */
const FORCE_STRING_FIELDS = new Set([
  'area_code',
  'category',
  'code',
  'country_code',
  'grade',
  'id',
  'key',
  'level',
  'mobile',
  'number',
  'phone',
  'postal',
  'serial',
  'status',
  'tel',
  'type',
  'uuid',
  'version',
  'zip',
]);

/**
 * 处理表单字段数据
 */
export function processFormData(
  formData: Record<string, any>,
  schema: VbenFormSchema[],
): Record<string, any> {
  const processedData = { ...formData };

  schema.forEach((item) => {
    const fieldName = item.fieldName;
    if (!fieldName) return;

    processFieldByComponent(processedData, fieldName, item);
  });

  return processedData;
}

/**
 * 检查字段是否应该强制保持字符串格式
 */
function isForceStringField(fieldName: string): boolean {
  return (
    FORCE_STRING_FIELDS.has(fieldName.toLowerCase()) ||
    fieldName.toLowerCase().includes('type') ||
    fieldName.toLowerCase().includes('status') ||
    fieldName.toLowerCase().includes('code') ||
    fieldName.toLowerCase().endsWith('_id') ||
    fieldName.toLowerCase().endsWith('id')
  );
}

/**
 * 处理布尔值
 */
function processBooleanValue(value: any): boolean {
  if (typeof value === 'boolean') {
    return value;
  }
  if (typeof value === 'string') {
    return value === 'true' || value === '1';
  }
  if (typeof value === 'number') {
    return value === 1;
  }
  return Boolean(value);
}

/**
 * 处理复选框值
 */
function processCheckboxValue(
  value: any,
  isForceStringField: boolean = false,
): any[] {
  if (Array.isArray(value)) {
    return isForceStringField ? value.map((v) => processStringValue(v)) : value;
  }
  if (typeof value === 'string') {
    // 尝试解析逗号分隔的字符串
    const arrayValue = value
      .split(',')
      .map((item) => item.trim())
      .filter(Boolean);
    return isForceStringField
      ? arrayValue.map((v) => processStringValue(v))
      : arrayValue;
  }
  if (value !== null && value !== undefined && value !== '') {
    const arrayValue = [value];
    return isForceStringField
      ? arrayValue.map((v) => processStringValue(v))
      : arrayValue;
  }
  return [];
}

/**
 * 处理日期范围值
 */
function processDateRangeValue(value: any): any {
  if (Array.isArray(value) && value.length === 2) {
    const dateRange = safeDateRangeConversion(value);
    return dateRange || value;
  }
  return value;
}

/**
 * 处理日期值
 */
function processDateValue(value: any): any {
  if (typeof value === 'string') {
    const dayjsDate = safeDateConversion(value);
    return dayjsDate || value;
  }
  return value;
}

/**
 * 处理 EditTable 值
 */
function processEditTableValue(value: any): any[] {
  return processEditTableData(value);
}

/**
 * 根据组件类型处理字段数据
 */
function processFieldByComponent(
  data: Record<string, any>,
  fieldName: string,
  schemaItem: VbenFormSchema,
): void {
  const { component, componentProps = {} } = schemaItem;
  const value = data[fieldName];

  // 如果值为空，跳过处理
  if (!isValidValue(value)) {
    return;
  }

  // 检查是否为强制字符串字段
  const isForceStringFieldFlag = isForceStringField(fieldName);

  switch (component) {
    case 'ApiSelect':
    case 'Select': {
      data[fieldName] = processSelectValue(
        value,
        componentProps,
        isForceStringFieldFlag,
      );
      break;
    }

    case 'ApiTreeSelect':
    case 'TreeSelect': {
      data[fieldName] = processTreeSelectValue(value, componentProps);
      break;
    }

    case 'CheckboxGroup': {
      data[fieldName] = processCheckboxValue(value, isForceStringFieldFlag);
      break;
    }

    case 'DatePicker': {
      data[fieldName] = processDateValue(value);
      break;
    }

    case 'EditTable': {
      data[fieldName] = processEditTableValue(value);
      break;
    }

    case 'Input':
    case 'InputPassword':
    case 'Textarea': {
      data[fieldName] = processStringValue(value);
      break;
    }
    case 'InputNumber': {
      // 即使是 InputNumber，如果字段名在强制字符串列表中，也保持字符串格式
      data[fieldName] = isForceStringFieldFlag
        ? processStringValue(value)
        : processNumberValue(value);
      break;
    }
    case 'RadioGroup': {
      data[fieldName] = processRadioValue(value, isForceStringFieldFlag);
      break;
    }

    case 'RangePicker': {
      data[fieldName] = processDateRangeValue(value);
      break;
    }

    case 'Switch': {
      data[fieldName] = processBooleanValue(value);
      break;
    }

    case 'Upload': {
      data[fieldName] = processUploadValue(value);
      break;
    }

    default: {
      // 对于未明确处理的组件类型，进行智能推断
      data[fieldName] = processGenericValue(
        value,
        component,
        isForceStringFieldFlag,
      );
      break;
    }
  }
}

/**
 * 处理通用值（智能推断）
 */
function processGenericValue(
  value: any,
  component: string,
  isForceStringField: boolean = false,
): any {
  // 如果是强制字符串字段，直接转换为字符串
  if (isForceStringField) {
    return processStringValue(value);
  }

  // 根据组件名称进行智能推断
  if (
    component.toLowerCase().includes('select') ||
    component.toLowerCase().includes('tree')
  ) {
    // 选择类组件，保持原值
    return value;
  }

  if (
    component.toLowerCase().includes('input') ||
    component.toLowerCase().includes('text')
  ) {
    // 输入类组件，转换为字符串
    return safeStringConversion(value);
  }

  if (component.toLowerCase().includes('number')) {
    // 数字类组件，转换为数字
    return safeNumberConversion(value);
  }

  if (
    component.toLowerCase().includes('date') && // 日期类组件，尝试日期转换
    typeof value === 'string'
  ) {
    const dayjsDate = safeDateConversion(value);
    return dayjsDate || value;
  }

  // 默认返回原值
  return value;
}

/**
 * 处理数字值
 */
function processNumberValue(value: any): number | string {
  return safeNumberConversion(value);
}

/**
 * 处理单选框值
 */
function processRadioValue(
  value: any,
  isForceStringField: boolean = false,
): any {
  // 单选框通常返回单个值
  if (Array.isArray(value)) {
    const singleValue = value.length > 0 ? value[0] : undefined;
    return isForceStringField ? processStringValue(singleValue) : singleValue;
  }
  return isForceStringField ? processStringValue(value) : value;
}

/**
 * 处理 Select 值
 */
function processSelectValue(
  value: any,
  componentProps: Record<string, any>,
  isForceStringField: boolean = false,
): any {
  const isMultiple = componentProps.multiple;

  if (!isMultiple) {
    // 单选模式
    if (Array.isArray(value)) {
      const singleValue = value.length > 0 ? value[0] : undefined;
      return isForceStringField ? processStringValue(singleValue) : singleValue;
    }
    return isForceStringField ? processStringValue(value) : value;
  }

  // 多选模式：确保返回数组
  if (Array.isArray(value)) {
    return isForceStringField ? value.map((v) => processStringValue(v)) : value;
  }
  if (value !== null && value !== undefined && value !== '') {
    const arrayValue = [value];
    return isForceStringField
      ? arrayValue.map((v) => processStringValue(v))
      : arrayValue;
  }
  return [];
}

/**
 * 处理字符串值
 */
function processStringValue(value: any): string {
  return safeStringConversion(value);
}

/**
 * 处理 TreeSelect 值
 */
function processTreeSelectValue(
  value: any,
  componentProps: Record<string, any>,
): any {
  const isMultipleMode =
    componentProps.multiple || componentProps.treeCheckable;

  if (!isMultipleMode) {
    // 单选模式：确保返回单个值
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : undefined;
    }
    return value;
  }

  // 多选模式：确保返回数组
  if (Array.isArray(value)) {
    return value;
  }
  if (value !== null && value !== undefined && value !== '') {
    return [value];
  }
  return [];
}

/**
 * 处理上传文件值
 */
function processUploadValue(value: any): any {
  if (Array.isArray(value)) {
    return value;
  }
  if (typeof value === 'string') {
    try {
      // 尝试解析 JSON 字符串
      const parsed = JSON.parse(value);
      return Array.isArray(parsed) ? parsed : [parsed];
    } catch {
      // 如果不是 JSON，可能是单个文件路径
      return value ? [{ url: value }] : [];
    }
  }
  if (value && typeof value === 'object') {
    return [value];
  }
  return [];
}

/**
 * RangePicker配置生成器
 */
export const RangePickerConfigGenerator = {
  /**
   * 生成RangePicker字段映射数组
   */
  generateFieldMappingArray(
    schema: VbenFormSchema[],
  ): Array<[string, string[], string[]]> {
    const rangePickerFields = schema.filter(
      (item) => item.component === 'RangePicker',
    );

    return rangePickerFields.map((field) => {
      const config = field.componentProps || {};
      return generateRangePickerMapping(field.fieldName, config);
    });
  },
};

// ==================== 导出默认配置 ====================

/**
 * 默认表单配置
 */
export const DEFAULT_FORM_CONFIG = {
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
  fieldMappingTime: [],
} as const;

/**
 * 默认抽屉配置
 */
export const DEFAULT_DRAWER_CONFIG = {
  loading: false,
  confirmLoading: false,
} as const;
