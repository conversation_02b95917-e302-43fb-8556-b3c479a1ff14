<script setup lang="ts">
/**
 * 审批组件
 *
 * 功能特性：
 * - 显示审批流程和状态
 * - 支持审批操作（通过/拒绝）
 * - 支持查看审批历史
 * - 支持审批意见填写
 * - 支持附件上传
 */

import type { ApprovalNode, ApprovalProps, ApprovalRecord } from './types';

import { computed, h, ref } from 'vue';

import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  FileTextOutlined,
  PlusOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';
import {
  Avatar,
  Button,
  Card,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Radio,
  RadioGroup,
  Space,
  Step,
  Steps,
  Tag,
  Tooltip,
  Upload,
} from 'ant-design-vue';

interface Props extends ApprovalProps {
  /** 审批数据 */
  data?: ApprovalRecord;
  /** 是否可以审批 */
  canApprove?: boolean;
  /** 当前用户ID */
  currentUserId?: string;
  /** 标题 */
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  data: undefined,
  canApprove: false,
  currentUserId: undefined,
  title: '审批流程',
});

const emit = defineEmits<{
  approve: [
    data: {
      attachments?: any[];
      comment: string;
      result: 'approve' | 'reject';
    },
  ];
  refresh: [];
  view: [node: ApprovalNode];
}>();

// 表单相关
const showApprovalModal = ref(false);
const formRef = ref();
const approvalForm = ref({
  result: 'approve' as 'approve' | 'reject',
  comment: '',
  attachments: [] as any[],
});

// 表单规则
const formRules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' } as any,
    { min: 5, message: '审批意见至少5个字符', trigger: 'blur' } as any,
  ],
};

// 计算属性
const approvalNodes = computed(() => {
  return props.data?.nodes || [];
});

const currentStep = computed(() => {
  const nodes = approvalNodes.value;
  const currentIndex = nodes.findIndex((node) => node.status === 'pending');
  return currentIndex === -1 ? nodes.length - 1 : currentIndex;
});

const canCurrentUserApprove = computed(() => {
  if (!props.canApprove || !props.currentUserId) return false;

  const currentNode = approvalNodes.value[currentStep.value];
  return (
    currentNode?.status === 'pending' &&
    currentNode?.approvers?.some(
      (approver) => approver.userId === props.currentUserId,
    )
  );
});

const overallStatus = computed(() => {
  const nodes = approvalNodes.value;
  if (nodes.some((node) => node.status === 'rejected')) return 'rejected';
  if (nodes.every((node) => node.status === 'approved')) return 'approved';
  if (nodes.some((node) => node.status === 'pending')) return 'pending';
  return 'draft';
});

// 方法
const getStepStatus = (index: number) => {
  const node = approvalNodes.value[index];
  if (!node) return 'wait';

  switch (node.status) {
    case 'approved': {
      return 'finish';
    }
    case 'pending': {
      return 'process';
    }
    case 'rejected': {
      return 'error';
    }
    default: {
      return 'wait';
    }
  }
};

const getStepIcon = (node: ApprovalNode) => {
  switch (node.status) {
    case 'approved': {
      return h(CheckCircleOutlined);
    }
    case 'pending': {
      return h(ClockCircleOutlined);
    }
    case 'rejected': {
      return h(CloseCircleOutlined);
    }
    default: {
      return h(UserOutlined);
    }
  }
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    approved: 'green',
    rejected: 'red',
    pending: 'orange',
    draft: 'default',
  };
  return colorMap[status] || 'default';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    approved: '已通过',
    rejected: '已拒绝',
    pending: '待审批',
    draft: '草稿',
  };
  return textMap[status] || '未知';
};

const handleApprove = () => {
  showApprovalModal.value = true;
  approvalForm.value = {
    result: 'approve',
    comment: '',
    attachments: [],
  };
};

const handleSubmitApproval = async () => {
  try {
    await formRef.value?.validate();

    emit('approve', {
      result: approvalForm.value.result,
      comment: approvalForm.value.comment,
      attachments: approvalForm.value.attachments,
    });

    showApprovalModal.value = false;
    message.success('审批提交成功');

    // 重置表单
    formRef.value?.resetFields();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleCancelApproval = () => {
  showApprovalModal.value = false;
  formRef.value?.resetFields();
};

const handleViewNodeDetail = (node: ApprovalNode) => {
  emit('view', node);
};

const formatTime = (time?: string) => {
  if (!time) return '';
  return new Date(time).toLocaleString('zh-CN');
};

// 获取上传请求头
const getUploadHeaders = () => {
  const token =
    typeof window === 'undefined' ? '' : window.localStorage.getItem('token');
  return {
    authorization: `Bearer ${token}`,
  };
};

// 文件上传处理
const handleFileUpload = (info: any) => {
  if (info.file.status === 'done') {
    // 移除重复的成功提示，统一由upload store管理
    approvalForm.value.attachments.push({
      name: info.file.name,
      url: info.file.response?.url || '',
      size: info.file.size,
    });
  }
  // 移除重复的错误提示，统一由upload store管理
};
</script>

<template>
  <div class="approval-component">
    <Card :title="title" class="approval-card">
      <!-- 操作栏 -->
      <template #extra>
        <Space>
          <Tag :color="getStatusColor(overallStatus)">
            {{ getStatusText(overallStatus) }}
          </Tag>
          <Button
            v-if="canCurrentUserApprove"
            type="primary"
            @click="handleApprove"
          >
            审批
          </Button>
          <Button @click="emit('refresh')"> 刷新 </Button>
        </Space>
      </template>

      <!-- 审批流程 -->
      <div v-if="approvalNodes.length > 0" class="approval-flow">
        <Steps
          :current="currentStep"
          direction="vertical"
          class="approval-steps"
        >
          <Step
            v-for="(node, index) in approvalNodes"
            :key="node.id || index"
            :status="getStepStatus(index)"
            :title="node.name"
            :description="node.description"
          >
            <template #icon>
              <component :is="getStepIcon(node)" />
            </template>

            <template #description>
              <div class="step-content">
                <div class="step-info">
                  <p class="step-desc">{{ node.description }}</p>

                  <!-- 审批人列表 -->
                  <div v-if="node.approvers?.length" class="approvers">
                    <span class="label">审批人：</span>
                    <Space wrap>
                      <Tooltip
                        v-for="approver in node.approvers"
                        :key="approver.userId"
                        :title="approver.userName"
                      >
                        <Avatar
                          :size="24"
                          :src="approver.avatar"
                          class="approver-avatar"
                        >
                          {{ approver.userName?.charAt(0) }}
                        </Avatar>
                      </Tooltip>
                    </Space>
                  </div>

                  <!-- 审批时间 -->
                  <div v-if="node.approveTime" class="approve-time">
                    <span class="label">审批时间：</span>
                    <span>{{ formatTime(node.approveTime) }}</span>
                  </div>

                  <!-- 审批意见 -->
                  <div v-if="node.comment" class="approve-comment">
                    <span class="label">审批意见：</span>
                    <p class="comment-text">{{ node.comment }}</p>
                  </div>

                  <!-- 附件 -->
                  <div v-if="node.attachments?.length" class="attachments">
                    <span class="label">附件：</span>
                    <Space wrap>
                      <Tag
                        v-for="(file, fileIndex) in node.attachments"
                        :key="fileIndex"
                        color="blue"
                        class="attachment-tag"
                      >
                        <FileTextOutlined />
                        {{ file.name }}
                      </Tag>
                    </Space>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div v-if="node.status !== 'draft'" class="step-actions">
                  <Button
                    type="link"
                    size="small"
                    @click="handleViewNodeDetail(node)"
                  >
                    查看详情
                  </Button>
                </div>
              </div>
            </template>
          </Step>
        </Steps>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-content">
          <FileTextOutlined class="empty-icon" />
          <p class="empty-text">暂无审批流程</p>
        </div>
      </div>
    </Card>

    <!-- 审批弹窗 -->
    <Modal
      v-model:open="showApprovalModal"
      title="审批操作"
      width="600px"
      @ok="handleSubmitApproval"
      @cancel="handleCancelApproval"
    >
      <Form
        ref="formRef"
        :model="approvalForm"
        :rules="formRules"
        layout="vertical"
      >
        <FormItem label="审批结果" name="result">
          <RadioGroup v-model:value="approvalForm.result">
            <Radio value="approve">通过</Radio>
            <Radio value="reject">拒绝</Radio>
          </RadioGroup>
        </FormItem>

        <FormItem label="审批意见" name="comment">
          <Input.TextArea
            v-model:value="approvalForm.comment"
            placeholder="请输入审批意见..."
            :rows="4"
            show-count
            :maxlength="500"
          />
        </FormItem>

        <FormItem label="附件">
          <Upload
            action="/api/oss/putFile"
            :headers="getUploadHeaders()"
            @change="handleFileUpload"
          >
            <Button :icon="h(PlusOutlined)">上传附件</Button>
          </Upload>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
.approval-component {
  width: 100%;
}

.approval-card {
  width: 100%;
}

.approval-flow {
  width: 100%;
  padding: 16px 0;
}

.approval-steps {
  width: 100%;
}

.step-content {
  padding: 8px 0;
}

.step-info {
  margin-bottom: 8px;
}

.step-desc {
  margin-bottom: 8px;
  color: #666;
}

.label {
  margin-right: 8px;
  font-weight: 500;
  color: #333;
}

.approvers {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 8px 0;
}

.approver-avatar {
  margin-right: 4px;
}

.approve-time {
  margin: 8px 0;
  font-size: 12px;
  color: #666;
}

.approve-comment {
  margin: 8px 0;
}

.comment-text {
  padding: 8px;
  margin: 4px 0;
  line-height: 1.6;
  background: #f5f5f5;
  border-radius: 4px;
}

.attachments {
  margin: 8px 0;
}

.attachment-tag {
  cursor: pointer;
}

.step-actions {
  margin-top: 8px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px;
}

.empty-content {
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #d9d9d9;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
</style>
