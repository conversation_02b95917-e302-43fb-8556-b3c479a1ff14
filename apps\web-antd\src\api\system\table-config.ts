import { requestClient } from '#/api/request';

export namespace TableConfigApi {
  /**
   * 表格列配置接口
   */
  export interface ColumnConfig {
    /** 列字段名 */
    field: string;
    /** 列标题 */
    title: string;
    /** 是否显示 */
    visible: boolean;
    /** 列宽度 */
    width?: number;
    /** 排序索引 */
    sortIndex: number;
    /** 是否固定 */
    fixed?: 'left' | 'right';
  }

  /**
   * 表格配置
   */
  export interface TableConfig {
    /** 配置ID */
    id?: string;
    /** 用户ID */
    userId: string;
    /** 页面标识 */
    pageKey: string;
    /** 表格标识 */
    tableKey: string;
    /** 列配置 */
    columns: ColumnConfig[];
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  }

  /**
   * 保存配置请求参数
   */
  export interface SaveConfigRequest {
    /** 页面标识 */
    pageKey: string;
    /** 表格标识 */
    tableKey: string;
    /** 列配置 */
    columns: ColumnConfig[];
  }

  /**
   * 获取配置请求参数
   */
  export interface GetConfigRequest {
    /** 页面标识 */
    pageKey: string;
    /** 表格标识 */
    tableKey: string;
  }
}

/**
 * 保存表格列配置
 * @param data 配置数据
 */
export async function saveTableConfig(data: TableConfigApi.SaveConfigRequest) {
  return requestClient.post<TableConfigApi.TableConfig>(
    '/system/table-config/save',
    data,
  );
}

/**
 * 获取表格列配置
 * @param params 查询参数
 */
export async function getTableConfig(params: TableConfigApi.GetConfigRequest) {
  return requestClient.get<TableConfigApi.TableConfig>(
    '/system/table-config/get',
    {
      params,
    },
  );
}

/**
 * 删除表格列配置
 * @param params 查询参数
 */
export async function deleteTableConfig(
  params: TableConfigApi.GetConfigRequest,
) {
  return requestClient.delete('/system/table-config/delete', {
    params,
  });
}

/**
 * 重置表格列配置为默认
 * @param params 查询参数
 */
export async function resetTableConfig(
  params: TableConfigApi.GetConfigRequest,
) {
  return requestClient.post('/system/table-config/reset', params);
}
