<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  value: any;
  count?: number;
  allowHalf?: boolean;
  character?: string;
  showText?: boolean;
  textMapping?: Record<number, string>;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  count: 5,
  allowHalf: false,
  character: undefined,
  showText: false,
  textMapping: undefined,
  placeholder: '暂无评分',
});

const rateValue = computed<null | number>(() => {
  const val = Number(props.value);
  if (Number.isNaN(val)) return null;
  return Math.max(0, Math.min(props.count, val));
});

/**
 * 获取评分文本
 */
function getRateText(): string {
  if (rateValue.value === null) return '';

  // 使用自定义文本映射
  if (props.textMapping) {
    const key = props.allowHalf ? rateValue.value : Math.floor(rateValue.value);
    return props.textMapping[key] || `${rateValue.value}分`;
  }

  // 默认文本
  const texts = ['很差', '较差', '一般', '良好', '优秀'];
  const index = Math.floor(rateValue.value) - 1;

  if (index >= 0 && index < texts.length) {
    return texts[index];
  }

  return `${rateValue.value}分`;
}
</script>

<template>
  <div class="rate-renderer">
    <a-rate
      v-if="rateValue !== null"
      :value="rateValue"
      :count="count"
      :allow-half="allowHalf"
      :disabled="true"
      :character="character"
    />
    <span v-if="showText && rateValue !== null" class="rate-text">
      {{ getRateText() }}
    </span>
    <span v-if="rateValue === null" class="rate-placeholder">{{
      placeholder
    }}</span>
  </div>
</template>

<style scoped>
.rate-renderer {
  display: flex;
  gap: 8px;
  align-items: center;
}

.rate-text {
  font-size: 14px;
  color: #666;
}

.rate-placeholder {
  color: #999;
}
</style>
