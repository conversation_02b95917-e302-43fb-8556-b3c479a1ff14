<script setup lang="ts">
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { transformBackendSearchToSchema } from '#/utils/search-schema';

const emit = defineEmits(['confirm']);
const data = ref();
const fieldMappingTimeArray = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '查询',
  cancelText: '重置',
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();

      // 尝试多种可能的数据路径
      let rawData = null;
      // 尝试路径1: schema[0].dataItem
      if (data.value?.schema) {
        rawData = data.value.schema;
      }
      // 尝试路径2: 直接是数组
      else if (Array.isArray(data.value)) {
        rawData = data.value;
      }
      // 尝试路径3: data 字段
      else if (data.value?.data && Array.isArray(data.value.data)) {
        rawData = data.value.data;
      }
      // 尝试路径4: items 字段
      else if (data.value?.items && Array.isArray(data.value.items)) {
        rawData = data.value.items;
      }

      if (rawData && Array.isArray(rawData) && rawData.length > 0) {
        // rawData
        const result = transformBackendSearchToSchema(rawData);

        // 查找 RangePicker 组件
        const rangePickerFields = result.filter(
          (item) => item.component === 'RangePicker',
        );

        // 如果找到多个 RangePicker 字段，生成 fieldMappingTime 格式
        if (rangePickerFields.length > 0) {
          fieldMappingTimeArray.value = rangePickerFields.map((field) => {
            // 从配置中获取字段信息，或使用默认值
            const config = (field.componentProps as any) || {};
            const startField = config.startField || `${field.fieldName}_strat`;
            const endField = config.endField || `${field.fieldName}_end`;
            const formatstrat = config.formatstrat || 'YYYY-MM-DD 00:00:00';
            const formatend = config.formatend || 'YYYY-MM-DD 23:59:59';

            return [
              field.fieldName, // RangePicker 的字段名
              [startField, endField], // 起始和结束字段名
              [formatstrat, formatend], // 日期格式
            ];
          });
        }
        formApi.setState({
          schema: result,
          fieldMappingTime: fieldMappingTimeArray.value,
        });
      } else {
        // rawData 不满足条件，跳过转换
      }
    }
  },
  onConfirm: async () => {
    try {
      drawerApi.setState({
        confirmLoading: true,
      });
      const fromData = await formApi.getValues();
      drawerApi.close();
      emit('confirm', fromData, 'search');
    } catch {
      drawerApi.setState({
        confirmLoading: false,
      });
      // Search form error
    } finally {
      drawerApi.setState({
        confirmLoading: false,
      });
    }
  },
  onCancel: async () => {
    try {
      drawerApi.setState({
        confirmLoading: true,
      });
      await formApi.resetForm();
      const fromData = await formApi.getValues();
      drawerApi.close();
      emit('confirm', fromData, 'reset');
    } catch {
      drawerApi.setState({
        confirmLoading: false,
      });
      // Reset form error
    } finally {
      drawerApi.setState({
        confirmLoading: false,
      });
    }
  },
});

const [BaseForm, formApi] = useVbenForm({
  // 提交函数
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
</script>
<template>
  <Drawer title="筛选">
    <BaseForm />
  </Drawer>
</template>
