import type { UploadFile } from 'ant-design-vue';

import { baseRequestClient } from '#/api/request';
import { useUploadStore } from '#/stores/upload';

/**
 * 批量上传状态管理
 */
interface BatchUploadState {
  isUploading: boolean;
  uploadedCount: number;
  totalCount: number;
  failedFiles: string[];
  onUploadStateChange?: (state: BatchUploadState) => void;
}

/**
 * 创建批量上传函数 - 使用 Pinia store 管理状态
 * @param fieldName 字段名
 * @returns 自定义上传函数
 */
export function createBatchUploadWithStore(fieldName: string) {
  const uploadStore = useUploadStore();

  /**
   * 设置总文件数（保留用于兼容性，实际状态管理在 onChange 中处理）
   */
  function setTotalCount(count: number) {
    uploadStore.setFieldState(fieldName, {
      totalCount: count,
    });
  }

  /**
   * 自定义上传请求函数
   */
  const customRequest = async function (options: any) {
    const { file, onSuccess, onError, onProgress } = options;

    const currentState = uploadStore.getFieldState(fieldName);

    // 如果是批量上传的第一个文件，初始化状态
    if (!currentState.isUploading) {
      uploadStore.setFieldState(fieldName, {
        isUploading: true,
        uploadedCount: 0,
        failedFiles: [],
      });
    }

    try {
      // 模拟上传进度
      onProgress?.({ percent: 0 });

      // 使用 baseRequestClient 的 upload 方法
      const response = await baseRequestClient.upload('oss/putFile', {
        file,
      });

      // 模拟上传进度完成
      onProgress?.({ percent: 100 });

      // 更新上传成功计数
      const updatedState = uploadStore.getFieldState(fieldName);
      const newUploadedCount = updatedState.uploadedCount + 1;

      uploadStore.setFieldState(fieldName, {
        uploadedCount: newUploadedCount,
      });

      onSuccess?.(response, file);

      // 检查是否所有文件都已上传完成
      const finalState = uploadStore.getFieldState(fieldName);
      if (finalState.uploadedCount >= finalState.totalCount) {
        uploadStore.setFieldState(fieldName, {
          isUploading: false,
        });
      }
    } catch (error) {
      // 记录失败的文件
      const fileName = file.name || '未知文件';
      const updatedState = uploadStore.getFieldState(fieldName);

      uploadStore.setFieldState(fieldName, {
        uploadedCount: updatedState.uploadedCount + 1,
        failedFiles: [...updatedState.failedFiles, fileName],
      });

      // 不在这里显示单个文件错误提示，统一在store的manageMessage中处理

      onError?.(error);

      // 检查是否所有文件都已处理完成（包括失败的）
      const finalState = uploadStore.getFieldState(fieldName);
      if (finalState.uploadedCount >= finalState.totalCount) {
        uploadStore.setFieldState(fieldName, {
          isUploading: false,
        });
      }
    }
  };

  // 返回上传函数和设置总数的函数
  return { customRequest, setTotalCount };
}

/**
 * 创建批量上传函数
 * @param onUploadStateChange 上传状态变化回调
 * @returns 自定义上传函数
 */
export function createBatchUploadFunction(
  onUploadStateChange?: (state: BatchUploadState) => void,
) {
  const uploadState: BatchUploadState = {
    isUploading: false,
    uploadedCount: 0,
    totalCount: 0,
    failedFiles: [],
    onUploadStateChange,
  };

  /**
   * 更新上传状态
   */
  function updateUploadState(updates: Partial<BatchUploadState>) {
    Object.assign(uploadState, updates);
    uploadState.onUploadStateChange?.(uploadState);
  }

  /**
   * 设置总文件数
   */
  function setTotalCount(count: number) {
    updateUploadState({
      totalCount: count,
    });
  }

  /**
   * 自定义上传请求函数
   */
  const customRequest = async function (options: any) {
    const { file, onSuccess, onError, onProgress } = options;

    // 如果是批量上传的第一个文件，初始化状态
    if (!uploadState.isUploading) {
      updateUploadState({
        isUploading: true,
        uploadedCount: 0,
        failedFiles: [],
      });
    }

    try {
      // 模拟上传进度
      onProgress?.({ percent: 0 });

      // 使用 baseRequestClient 的 upload 方法
      const response = await baseRequestClient.upload('oss/putFile', {
        file,
      });

      // 模拟上传进度完成
      onProgress?.({ percent: 100 });

      // 上传成功
      updateUploadState({
        uploadedCount: uploadState.uploadedCount + 1,
      });

      onSuccess?.(response, file);

      // 检查是否所有文件都已上传完成
      if (uploadState.uploadedCount >= uploadState.totalCount) {
        // 确保状态更新并触发回调
        updateUploadState({
          isUploading: false,
        });

        // 不在这里显示提示，避免与统一消息系统重复
      }
    } catch (error) {
      // 记录失败的文件
      const fileName = file.name || '未知文件';
      updateUploadState({
        uploadedCount: uploadState.uploadedCount + 1,
        failedFiles: [...uploadState.failedFiles, fileName],
      });

      // 不在这里显示单个文件错误提示，避免与汇总提示重复

      onError?.(error);

      // 检查是否所有文件都已处理完成（包括失败的）
      if (uploadState.uploadedCount >= uploadState.totalCount) {
        // 确保状态更新并触发回调
        updateUploadState({
          isUploading: false,
        });

        // 不在这里显示提示，避免与统一消息系统重复
      }
    }
  };

  // 返回上传函数和设置总数的函数
  return { customRequest, setTotalCount };
}

/**
 * 创建带状态管理的批量上传函数
 * 返回上传函数和状态管理对象
 */
export function createBatchUploadWithState() {
  let uploadState: BatchUploadState = {
    isUploading: false,
    uploadedCount: 0,
    totalCount: 0,
    failedFiles: [],
  };

  const { customRequest, setTotalCount } = createBatchUploadFunction(
    (state) => {
      uploadState = { ...state };
    },
  );

  return {
    customRequest,
    setTotalCount,
    getUploadState: () => uploadState,
    isUploading: () => uploadState.isUploading,
  };
}

/**
 * 处理文件列表变化，更新总文件数
 */
export function handleFileListChange(
  fileList: UploadFile[],
  setTotalCount: (count: number) => void,
) {
  // 确保 fileList 是数组，如果不是则使用空数组
  const safeFileList = Array.isArray(fileList) ? fileList : [];

  // 只计算正在上传或等待上传的文件
  const pendingFiles = safeFileList.filter(
    (file) =>
      file.status === 'uploading' || file.status === 'done' || !file.status,
  );
  setTotalCount(pendingFiles.length);
}
