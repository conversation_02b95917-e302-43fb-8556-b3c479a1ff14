<script setup lang="ts">
interface Props {
  value?: any;
  title?: string;
  item?: any;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  title: '',
  item: undefined,
});

// 从 item 中获取标题，优先级：item.label > title > value
const displayTitle = props.item?.label || props.title || props.value || '';
</script>

<template>
  <div class="group-title-renderer">
    <div class="group-title">
      {{ displayTitle }}
    </div>
  </div>
</template>

<style scoped>
.group-title-renderer {
  width: 100%;
  margin: 20px 0 16px;
}

.group-title {
  padding-left: 12px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-primary-color, #1890ff);
  border-left: 4px solid var(--ant-primary-color, #1890ff);
}
</style>
