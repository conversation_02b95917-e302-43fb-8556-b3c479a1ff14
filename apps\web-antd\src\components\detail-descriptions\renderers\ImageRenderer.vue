<script setup lang="ts">
import type { ImageInfo } from '../types';

import { computed } from 'vue';

interface Props {
  value: any;
  width?: number;
  height?: number;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  width: 60,
  height: 60,
  placeholder: '暂无图片',
});

const imageList = computed<ImageInfo[]>(() => {
  if (!props.value) return [];

  // 如果是字符串，转换为图片对象
  if (typeof props.value === 'string') {
    return [{ url: props.value, alt: '图片' }];
  }

  // 如果是数组
  if (Array.isArray(props.value)) {
    return props.value.map((item) => {
      if (typeof item === 'string') {
        return { url: item, alt: '图片' };
      }
      return item as ImageInfo;
    });
  }

  // 如果是对象
  if (typeof props.value === 'object') {
    return [props.value as ImageInfo];
  }

  return [];
});
</script>

<template>
  <div class="image-renderer">
    <template v-if="imageList.length > 0">
      <a-image
        v-for="(img, index) in imageList"
        :key="index"
        :src="img.thumbnail || img.url"
        :alt="img.alt"
        :width="img.width || width"
        :height="img.height || height"
        :preview="{ src: img.url }"
        class="image-item"
      />
    </template>
    <span v-else class="image-placeholder">{{ placeholder }}</span>
  </div>
</template>

<style scoped>
.image-renderer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.image-placeholder {
  color: #999;
}
</style>
