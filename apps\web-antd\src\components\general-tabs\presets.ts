/**
 * 预设组件配置
 */

import type { Component } from 'vue';

import type { PresetTabConfig, TabItem } from './types';

// 预设组件映射
export const PRESET_COMPONENTS: Record<string, Component | string> = {
  table: 'VbenTable',
  form: 'VbenForm',
  descriptions: 'DetailDescriptions',
  chart: 'VChart',
  list: 'AList',
  tree: 'ATree',
  custom: 'div',
};

/**
 * 创建预设 Tab 配置
 */
export function createPresetTab(config: PresetTabConfig): TabItem {
  const { type, label, config: componentConfig, disabled, condition } = config;

  return {
    key: `preset-${type}-${Math.random().toString(36).slice(2, 8)}`,
    label,
    component: PRESET_COMPONENTS[type] || 'div',
    props: componentConfig || {},
    disabled,
    condition,
  };
}

/**
 * 批量创建预设 Tabs
 */
export function createPresetTabs(configs: PresetTabConfig[]): TabItem[] {
  return configs.map((config) => createPresetTab(config));
}

/**
 * 常用预设配置
 */
export const COMMON_PRESETS = {
  // 数据表格 Tab
  dataTable: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'table',
    label: '数据列表',
    config: {
      bordered: true,
      size: 'small',
      ...config,
    },
  }),

  // 详情描述 Tab
  detailInfo: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'descriptions',
    label: '详细信息',
    config: {
      bordered: true,
      column: 3,
      ...config,
    },
  }),

  // 编辑表单 Tab
  editForm: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'form',
    label: '编辑信息',
    config: {
      layout: 'vertical',
      ...config,
    },
  }),

  // 图表展示 Tab
  chartView: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'chart',
    label: '图表分析',
    config: {
      height: 400,
      ...config,
    },
  }),

  // 树形结构 Tab
  treeView: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'tree',
    label: '树形结构',
    config: {
      showLine: true,
      checkable: false,
      ...config,
    },
  }),

  // 列表展示 Tab
  listView: (config?: Record<string, any>): PresetTabConfig => ({
    type: 'list',
    label: '列表展示',
    config: {
      size: 'default',
      bordered: true,
      ...config,
    },
  }),
};

/**
 * 根据数据类型自动推荐 Tab 配置
 */
export function getRecommendedTabs(data: any): PresetTabConfig[] {
  const tabs: PresetTabConfig[] = [];

  // 如果有数组数据，推荐表格
  if (Array.isArray(data) || (data && Array.isArray(data.list))) {
    tabs.push(COMMON_PRESETS.dataTable());
  }

  // 如果有对象数据，推荐详情描述
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    tabs.push(COMMON_PRESETS.detailInfo());
  }

  // 如果有图表相关字段，推荐图表
  if (data && (data.chartData || data.statistics || data.analytics)) {
    tabs.push(COMMON_PRESETS.chartView());
  }

  // 如果有树形数据，推荐树形组件
  if (
    data &&
    (data.tree ||
      data.children ||
      (Array.isArray(data) && data.some((item) => item.children)))
  ) {
    tabs.push(COMMON_PRESETS.treeView());
  }

  return tabs;
}

/**
 * 创建动态 Tab 配置
 */
export function createDynamicTabs(
  data: any,
  customConfigs?: Partial<PresetTabConfig>[],
): TabItem[] {
  // 获取推荐配置
  const recommendedTabs = getRecommendedTabs(data);

  // 合并自定义配置
  if (customConfigs) {
    customConfigs.forEach((customConfig, index) => {
      if (recommendedTabs[index]) {
        Object.assign(recommendedTabs[index], customConfig);
      } else {
        recommendedTabs.push({
          type: 'custom',
          label: `自定义 ${index + 1}`,
          ...customConfig,
        } as PresetTabConfig);
      }
    });
  }

  return createPresetTabs(recommendedTabs);
}
