// 导出主组件
export { default, default as GeneralTabs } from './index.vue';

// 导出预设配置
export {
  COMMON_PRESETS,
  createDynamicTabs,
  createPresetTab,
  createPresetTabs,
  getRecommendedTabs,
  PRESET_COMPONENTS,
} from './presets';

// 导出配置函数
export {
  DEFAULT_TAB_CONFIGS,
  getTabConfig,
  registerTabConfig,
  registerTabConfigs,
  transformBackendTabsToItems,
} from './tab-configs';

// 导出类型
export type {
  BackendTabItem,
  ComponentMap,
  PresetComponentType,
  PresetTabConfig,
  TabComponentConfig,
  TabConfigMap,
  TabItem,
  TabsConfig,
  TabsEvents,
} from './types';
