import { ref } from 'vue';

import { message } from 'ant-design-vue';
import { defineStore } from 'pinia';

interface UploadFieldState {
  isUploading: boolean;
  uploadedCount: number;
  totalCount: number;
  failedFiles: string[];
}

export const useUploadStore = defineStore('upload', () => {
  // 存储每个字段的上传状态
  const fieldStates = ref<Map<string, UploadFieldState>>(new Map());

  // 全局上传状态
  const isAnyUploading = ref(false);

  /**
   * 设置字段上传状态
   */
  function setFieldState(fieldName: string, state: Partial<UploadFieldState>) {
    const currentState = fieldStates.value.get(fieldName) || {
      isUploading: false,
      uploadedCount: 0,
      totalCount: 0,
      failedFiles: [],
    };

    const newState = { ...currentState, ...state };
    fieldStates.value.set(fieldName, newState);

    // 更新全局上传状态
    updateGlobalState();

    // 管理消息显示
    manageMessage(fieldName, newState);
  }

  /**
   * 更新全局上传状态
   */
  function updateGlobalState() {
    const hasUploading = [...fieldStates.value.values()].some(
      (state) => state.isUploading,
    );
    isAnyUploading.value = hasUploading;
  }

  /**
   * 管理消息显示
   */
  function manageMessage(fieldName: string, state: UploadFieldState) {
    const messageKey = `uploading-${fieldName}`;

    if (state.isUploading) {
      // 显示上传中消息
      message.loading({
        content: '文件上传中...',
        duration: 0,
        key: messageKey,
      });
    } else {
      // 隐藏上传消息
      message.destroy(messageKey);

      // 上传完成后的消息提示
      if (state.uploadedCount >= state.totalCount && state.totalCount > 0) {
        const successCount = state.uploadedCount - state.failedFiles.length;

        if (state.failedFiles.length > 0) {
          // 有失败文件的情况
          if (successCount > 0) {
            message.warning(
              `上传完成！成功：${successCount}个，失败：${state.failedFiles.length}个`,
            );
          } else {
            message.error(
              `上传完成，有 ${state.failedFiles.length} 个文件上传失败`,
            );
          }
        } else {
          // 全部成功的情况
          if (successCount > 1) {
            message.success(`上传完成！成功上传 ${successCount} 个文件`);
          } else if (successCount === 1) {
            message.success('文件上传成功');
          }
        }
      }
    }
  }

  /**
   * 获取字段状态
   */
  function getFieldState(fieldName: string): UploadFieldState {
    return (
      fieldStates.value.get(fieldName) || {
        isUploading: false,
        uploadedCount: 0,
        totalCount: 0,
        failedFiles: [],
      }
    );
  }

  /**
   * 清除字段状态
   */
  function clearFieldState(fieldName: string) {
    const messageKey = `uploading-${fieldName}`;
    message.destroy(messageKey);
    fieldStates.value.delete(fieldName);
    updateGlobalState();
  }

  /**
   * 清除所有状态
   */
  function clearAllStates() {
    fieldStates.value.forEach((_, fieldName) => {
      const messageKey = `uploading-${fieldName}`;
      message.destroy(messageKey);
    });
    fieldStates.value.clear();
    isAnyUploading.value = false;
  }

  return {
    fieldStates,
    isAnyUploading,
    setFieldState,
    getFieldState,
    clearFieldState,
    clearAllStates,
  };
});
