<script setup lang="ts">
import type { OptionConfig, OptionItem } from '../types';

import { computed } from 'vue';

interface Props {
  value: any;
  options?: OptionConfig;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  options: undefined,
  placeholder: '-',
});

const badgeOption = computed<null | OptionItem>(() => {
  if (!props.options || props.value === null || props.value === undefined) {
    return null;
  }

  // 从映射中查找
  if (props.options.mapping) {
    return props.options.mapping[props.value] || null;
  }

  // 从列表中查找
  if (props.options.list) {
    return (
      props.options.list.find((item) => item.value === props.value) || null
    );
  }

  return null;
});

const displayValue = computed(() => {
  if (props.value === null || props.value === undefined || props.value === '') {
    return props.placeholder;
  }
  return String(props.value);
});
</script>

<template>
  <div class="badge-renderer">
    <a-badge
      v-if="badgeOption"
      :status="badgeOption.status"
      :color="badgeOption.color"
      :text="badgeOption.label"
    />
    <span v-else class="badge-fallback">{{ displayValue }}</span>
  </div>
</template>

<style scoped>
.badge-renderer {
  display: inline-block;
}

.badge-fallback {
  color: #999;
}
</style>
