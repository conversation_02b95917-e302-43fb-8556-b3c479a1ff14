# 数据转换方法详细文档

本文档详细介绍项目中三个核心的数据转换方法，这些方法是前后端数据交互的关键桥梁，负责将后端返回的配置数据转换为前端组件所需的标准格式。

## 目录

- [1. transformColumns - 表格列转换](#1-transformcolumns)
- [2. transformBackendSearchToSchema - 表单Schema转换](#2-transformbackendsearchtoshema)
  - [2.1 基础功能](#21-基础功能)
  - [2.2 API组件支持](#22-api组件支持)
  - [2.3 自定义标签格式化 (labelFormatter)](#自定义标签格式化)
  - [2.4 字段联动系统](#24-字段联动系统)
- [3. transformToGroupedDescriptions - 详情描述转换](#3-transformtogroupeddescriptions)
- [4. 完整示例](#4-完整示例)
- [5. 常见问题](#5-常见问题)

## 1. transformColumns

### 功能概述

`transformColumns` 是表格数据转换的核心方法，负责将后端返回的列配置数据转换为 VXE Table 组件所需的列定义格式。该方法不仅处理基础的列属性转换，还智能处理操作按钮、特殊列类型等复杂场景。

### 文件位置

```
apps/web-antd/src/utils/search-schema/transform.ts
```

### 方法签名

```typescript
function transformColumns(columns: any[]): VxeTableDefines.ColumnOptions[];
```

### 详细功能说明

#### 1.1 基础列属性转换

- **字段映射**: `field` → `field`（字段名保持不变）
- **标题映射**: `title` → `title`（列标题）
- **宽度处理**: `width` → `width`（支持数字和字符串格式）
- **固定列**: `fixed` → `fixed`（支持 left/right 固定）
- **排序**: `sortable` → `sortable`（是否可排序）

#### 1.2 特殊列类型处理

支持以下特殊列类型的自动转换：

| 后端类型   | 前端处理   | 说明                   |
| ---------- | ---------- | ---------------------- |
| `person`   | 人员选择列 | 显示人员信息，支持头像 |
| `dept`     | 部门选择列 | 显示部门层级结构       |
| `status`   | 状态列     | 支持状态标签和颜色     |
| `date`     | 日期列     | 格式化日期显示         |
| `currency` | 货币列     | 格式化金额显示         |

#### 1.3 操作列智能处理

- **自动检测**: 如果配置中没有操作列但存在 `actionBtnList`，自动添加操作列
- **按钮转换**: 将 `actionBtnList` 中的按钮配置转换为标准格式
- **固定位置**: 操作列默认固定在表格右侧
- **按钮分离**: `type` 字段转换为 `code`，`key` 字段保持独立

#### 1.4 选项数据处理

对于包含选项的列（如状态列），自动处理选项映射：

```typescript
// 后端配置
{
  field: 'status',
  title: '状态',
  options: [
    { label: '启用', value: 1, color: 'green' },
    { label: '禁用', value: 0, color: 'red' }
  ]
}
// 转换后自动添加渲染逻辑
```

### 使用示例

```typescript
import { transformColumns } from '#/utils/search-schema';

// 后端数据
const backendColumns = [
  {
    field: 'name',
    title: '姓名',
    width: 120,
    type: 'text',
  },
  {
    field: 'department',
    title: '部门',
    width: 150,
    type: 'dept',
  },
];

// 转换
const tableColumns = transformColumns(backendColumns);
```

### 后端数据格式详细说明

#### 1.5 完整的后端列配置格式

```typescript
interface BackendColumn {
  field: string; // 必填：字段名
  title: string; // 必填：列标题
  width?: number; // 可选：列宽度（像素）
  type?: string; // 可选：列类型（text/person/dept/status等）
  fixed?: boolean | 'left' | 'right'; // 可选：是否固定列
  sortable?: boolean; // 可选：是否可排序
  align?: 'left' | 'center' | 'right'; // 可选：对齐方式
  minWidth?: number; // 可选：最小宽度
  maxWidth?: number; // 可选：最大宽度
  resizable?: boolean; // 可选：是否可调整大小
  visible?: boolean; // 可选：是否显示（默认true）

  // 选项配置（用于状态列、枚举列等）
  options?: Array<{
    label: string; // 显示文本
    value: any; // 实际值
    color?: string; // 颜色（用于状态标签）
    icon?: string; // 图标
  }>;

  // 操作按钮配置
  actionBtnList?: Array<{
    title: string; // 按钮文本
    type: string; // 按钮类型（edit/delete/view等）
    key?: string; // 按钮标识（可选）
    icon?: string; // 按钮图标
    color?: string; // 按钮颜色
    permission?: string; // 权限控制
  }>;

  // 格式化配置
  formatter?: {
    type: 'date' | 'currency' | 'number' | 'percent';
    format?: string; // 格式化模板
    precision?: number; // 精度（用于数字）
    currency?: string; // 货币符号
  };
}
```

#### 1.6 详细使用示例

```typescript
// 复杂表格配置示例
const complexBackendColumns = [
  {
    field: 'id',
    title: 'ID',
    width: 80,
    type: 'text',
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'avatar',
    title: '头像',
    width: 80,
    type: 'avatar',
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    type: 'text',
    sortable: true,
  },
  {
    field: 'department',
    title: '部门',
    width: 150,
    type: 'dept',
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    type: 'status',
    options: [
      { label: '在职', value: 1, color: 'green' },
      { label: '离职', value: 0, color: 'red' },
      { label: '试用', value: 2, color: 'orange' },
    ],
  },
  {
    field: 'salary',
    title: '薪资',
    width: 120,
    type: 'currency',
    formatter: {
      type: 'currency',
      currency: '¥',
      precision: 2,
    },
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 160,
    type: 'date',
    formatter: {
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];
```

### 特殊处理机制

#### 1.7 操作列自动处理

- **自动检测**: 如果配置中没有操作列但数据行包含 `actionBtnList`，会自动添加操作列
- **按钮转换**: 将 `actionBtnList` 中的按钮配置转换为标准格式
- **按钮分离**: `type` 字段转换为 `code`，`key` 字段保持独立
- **固定位置**: 操作列默认固定在表格右侧
- **权限控制**: 支持按钮级别的权限控制
- **智能分组**: 当操作按钮超过3个时，自动将后续按钮放入下拉菜单中显示
- **动态支持**: 支持任意按钮类型，无需预先注册

#### 1.8 按钮数量智能控制

当操作列的按钮数量较多时，系统会自动进行智能分组：

- **主要按钮**: 前3个按钮直接显示在操作列中
- **次要按钮**: 超过3个的按钮会自动放入下拉菜单（⋯）中
- **可配置**: 可以通过 `maxPrimaryButtons` 参数自定义主要按钮的最大数量

```typescript
// 使用示例
const tableColumns = transformColumns(backendColumns, tableData, {
  actionColumnConfig: {
    width: 300,
    title: '操作',
    fixed: 'right',
    maxPrimaryButtons: 2, // 自定义最多显示2个主要按钮
  },
});
```

#### 1.8 列类型智能识别

```typescript
// 系统支持的列类型及其处理方式
const columnTypeHandlers = {
  text: '普通文本列',
  number: '数字列（支持格式化）',
  currency: '货币列（自动添加货币符号）',
  percent: '百分比列（自动添加%符号）',
  date: '日期列（支持多种日期格式）',
  datetime: '日期时间列',
  time: '时间列',
  status: '状态列（支持标签和颜色）',
  tag: '标签列（支持多标签显示）',
  avatar: '头像列（支持图片显示）',
  image: '图片列（支持图片预览）',
  link: '链接列（可点击跳转）',
  person: '人员列（显示人员信息和头像）',
  dept: '部门列（显示部门层级结构）',
  file: '文件列（支持文件下载）',
  progress: '进度条列',
  rating: '评分列',
  switch: '开关列',
  actions: '操作列（自动生成操作按钮）',
};
```

## 2. transformBackendSearchToSchema

### 功能概述

`transformBackendSearchToSchema` 是表单配置转换的核心方法，负责将后端返回的表单字段配置转换为 Vben 表单系统所需的 Schema 格式。该方法支持复杂的表单组件、字段联动、API 数据源等高级功能。

### 文件位置

```
apps/web-antd/src/utils/search-schema/transform.ts
```

### 方法签名

```typescript
function transformBackendSearchToSchema(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options?: {
    enableGrouping?: boolean; // 是否启用分组功能，默认为 true
    formMode?: 'add' | 'edit'; // 表单模式，用于控制字段编辑权限
  },
): VbenFormSchema[];

// 其他相关方法
function transformBackendSearchToSchemaWithGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[];

function transformBackendSearchToSchemaWithoutGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[];

function quickTransformSearch(
  backendItems: BackendSearchItem[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[];
```

### 新增功能：分组数据处理

该方法现在支持处理分组数据，特别适用于编辑新增页面的表格展示：

- **分组数据支持**：可以处理包含多个分组的数据结构
- **第二组数据提取**：编辑新增页面的表格会根据分组的第二组参数进行展示
- **灵活分组控制**：通过 `enableGrouping` 选项控制是否显示分组标题
- **插槽组件支持**：新增 `edittable` 类型，支持在表单中嵌入自定义组件

### 详细功能说明

#### 2.1 支持的表单组件类型

系统支持丰富的表单组件类型，每种类型都有特定的转换逻辑：

| 后端类型        | 前端组件      | 说明        | 特殊功能                  |
| --------------- | ------------- | ----------- | ------------------------- |
| `input`         | Input         | 文本输入框  | 支持前缀、后缀、密码模式  |
| `textarea`      | Textarea      | 多行文本    | 支持行数配置、字数限制    |
| `select`        | Select        | 下拉选择    | 支持多选、搜索、分组      |
| `apiselect`     | ApiSelect     | API下拉选择 | 支持搜索、分页、动态加载  |
| `apitreeselect` | ApiTreeSelect | API树形选择 | 支持层级数据、懒加载      |
| `radio`         | Radio         | 单选框      | 支持按钮样式、垂直布局    |
| `checkbox`      | Checkbox      | 复选框      | 支持全选、半选状态        |
| `switch`        | Switch        | 开关        | 支持自定义文案、颜色      |
| `date`          | DatePicker    | 日期选择    | 支持多种日期格式          |
| `daterange`     | RangePicker   | 日期范围    | 支持快捷选择、禁用日期    |
| `time`          | TimePicker    | 时间选择    | 支持时分秒、12/24小时制   |
| `number`        | InputNumber   | 数字输入    | 支持最值限制、精度控制    |
| `slider`        | Slider        | 滑块        | 支持范围选择、步长控制    |
| `rate`          | Rate          | 评分        | 支持半星、自定义图标      |
| `upload`        | Upload        | 文件上传    | 支持多文件、拖拽、预览    |
| `cascader`      | Cascader      | 级联选择    | 支持动态加载、搜索        |
| `treeselect`    | TreeSelect    | 树形选择    | 支持多选、搜索、异步加载  |
| `mention`       | Mention       | 提及        | 支持@用户、自定义触发字符 |
| `hidden`        | -             | 隐藏字段    | 不显示但参与表单提交      |
| `edittable`     | Slot          | 插槽组件    | 支持自定义渲染、表格嵌入  |
| `Upload`        | Upload        | 文件上传    | 支持多文件、拖拽、预览    |

#### 2.2 edittable 类型详细说明

`edittable` 类型是一个特殊的插槽组件类型，允许在表单中嵌入自定义内容（如表格、图表等）。

**后端数据格式：**

```php
[
    'field' => 'order_details',
    'type' => 'edittable',
    'title' => '订单明细',
    'config' => [
        'columns' => [
            [
                'field' => 'product_name',
                'title' => '商品名称',
                'type' => 'text',
                'width' => 200
            ],
            // ... 更多列配置
        ],
        'tabList' => [
            [
                'id' => 1,
                'product_name' => '商品A',
                'quantity' => 2
            ],
            // ... 更多数据
        ],
        'height' => '400px',
        'editable' => true
        // ... 其他自定义配置
    ]
]
```

**前端使用方式：**

```vue
<GeneralEditingDrawer>
  <!-- 插槽名称对应后端的 field -->
  <template #order_details="{ value, params }">
    {{ value }}{{ params }}
    <!-- 您可以在这里渲染任何内容：表格、图表、自定义组件等 -->
  </template>
</GeneralEditingDrawer>
```

**插槽参数说明：**

- `value`: 表单字段的值
- `params`: 包含 config 中所有配置的对象
  - `params.columns`: 表格列配置
  - `params.tabList`: 表格数据
  - `params.height`: 表格高度
  - `params.editable`: 是否可编辑
  - 以及 config 中的其他所有配置

**重要特性：**

- ✅ 插槽名称直接对应后端的 `field` 字段
- ✅ 与以前的转换方式保持一致，只是新增了 `edittable` 类型支持
- ✅ 支持动态插槽生成，可以有多个 `edittable` 字段
- ✅ 完全自定义渲染，可以放置任何 Vue 组件
- ✅ 操作列完全由后端配置控制，前端不会自动添加
- ✅ 如果需要操作列，请在后端的 `columns` 配置中包含操作列定义

**使用场景：**

- 主从表编辑：主表单 + 明细表格
- 复杂数据展示：表单字段 + 数据图表
- 分步骤表单：每个步骤包含表单和表格
- 审批流程：表单信息 + 审批历史表格

#### 2.3 Upload 类型详细说明

`Upload` 类型用于文件上传功能，支持多种文件类型和上传样式。

**后端数据格式：**

```php
[
    'field' => 'avatar',
    'type' => 'Upload',
    'title' => '头像',
    'required' => true,
    'config' => [
        'accept' => '.png,.jpg,.jpeg',      // 接受的文件类型
        'maxCount' => 1,                    // 最大文件数量
        'multiple' => false,                // 是否支持多选
        'showUploadList' => true,           // 是否显示文件列表
        'listType' => 'picture-card',       // 上传列表样式
        'uploadText' => '上传头像',         // 上传按钮文本
        'disabled' => false                 // 是否禁用
        // 注意：上传地址固定为 'oss/putFile'，无需配置
    ]
]
```

**支持的配置项：**

- `accept`: 接受上传的文件类型，默认 `.png,.jpg,.jpeg`
- `maxCount`: 最大文件数量，默认 `1`
- `multiple`: 是否支持多选文件，默认 `false`
- `showUploadList`: 是否展示文件列表，默认 `true`
- `listType`: 上传列表样式，支持 `text`、`picture`、`picture-card`、`picture-circle`
- `uploadText`: 上传按钮的文本，默认 `上传{title}`
- `disabled`: 是否禁用，默认 `false`

**注意**：上传地址固定为 `oss/putFile`，无需在配置中指定。

**上传功能特性：**

- 支持大批量文件逐个上传，避免并发上传导致的服务器压力
- 上传过程中自动禁用表单提交按钮，防止用户提前提交表单
- 上传失败时显示简洁的错误提示，避免信息过载
- 上传完成后自动启用表单提交按钮
- 支持多个上传组件同时存在，互不干扰

**常用样式类型：**

- `text`: 文本样式，适合文档类文件
- `picture`: 图片样式，显示缩略图
- `picture-card`: 卡片样式，适合图片上传
- `picture-circle`: 圆形样式，适合头像上传

#### 2.4 API 组件智能处理

对于 `apiselect` 和 `apitreeselect` 组件，系统会自动生成对应的 API 请求函数：

```typescript
// 后端配置
{
  field: 'userId',
  type: 'apiselect',
  title: '用户',
  config: {
    url: '/api/users',
    labelField: 'name',
    valueField: 'id',
    showSearch: true,
    searchFieldName: 'keyword'
  }
}

// 自动生成的 API 函数
componentProps: {
  api: createApiSelectPaginatedFunction('/api/users', {}, {
    pageSize: 20,
    searchParamName: 'keyword'
  }),
  labelField: 'name',
  valueField: 'id'
}
```

**自定义标签格式化：**

对于需要自定义显示格式的场景（如汇率、价格等），可以使用 `labelFormatter` 功能：

```typescript
// 汇率字段配置示例
{
  field: 'currency_rate',
  type: 'apiSelect',
  title: '汇率',
  config: {
    url: '/api/currency/rates',
    labelField: 'name',
    valueField: 'id',

    // 自定义标签格式化函数
    labelFormatter: (item) => {
      const name = item.name || '';
      const rate = item.rate || 0;
      const formattedRate = rate >= 0 ? `+${rate}` : `${rate}`;
      return `${name} ${formattedRate}`;
    }
  }
}

// API 返回数据格式
{
  "items": [
    { "id": 1, "name": "南非兰特", "rate": -0.00212 },
    { "id": 2, "name": "韩国元", "rate": 0.00156 }
  ]
}

// 显示效果：
// - "南非兰特 -0.00212"
// - "韩国元 +0.00156"
```

#### 2.3 字段联动系统

支持复杂的字段间联动关系，包括显示/隐藏、必填控制、选项过滤等：

```typescript
// 后端联动配置
{
  field: 'city',
  type: 'select',
  title: '城市',
  linkage: {
    trigger: 'province',        // 触发字段
    condition: { type: 'value', value: 'guangdong' }, // 触发条件
    action: 'show',            // 动作类型
    options: {                 // 动态选项
      mapping: {
        'guangdong': [
          { label: '广州', value: 'guangzhou' },
          { label: '深圳', value: 'shenzhen' }
        ],
        'beijing': [
          { label: '朝阳区', value: 'chaoyang' },
          { label: '海淀区', value: 'haidian' }
        ]
      }
    }
  }
}

// 转换后的联动配置
dependencies: {
  triggerFields: ['province'],
  if: (values) => values.province === 'guangdong',
  show: true,
  componentProps: (values) => ({
    options: linkageOptionsMapping[values.province] || []
  })
}
```

#### 2.4 完整的后端表单字段配置格式

```typescript
interface BackendFormField {
  field: string; // 必填：字段名
  type: string; // 必填：组件类型
  title: string; // 必填：字段标题
  required?: boolean; // 可选：是否必填
  default?: any; // 可选：默认值
  placeholder?: string; // 可选：占位符文本
  helpText?: string; // 可选：帮助文本

  // 组件特定配置
  config?: {
    // 通用配置
    disabled?: boolean; // 是否禁用
    readonly?: boolean; // 是否只读
    size?: 'small' | 'middle' | 'large'; // 组件尺寸

    // API 组件配置
    url?: string; // API 地址
    params?: Record<string, any>; // 默认参数
    labelField?: string; // 标签字段名
    valueField?: string; // 值字段名
    showSearch?: boolean; // 是否显示搜索
    searchFieldName?: string; // 搜索字段名
    immediate?: boolean; // 是否立即加载

    // 选择类组件配置
    options?: Array<{
      // 静态选项
      label: string;
      value: any;
      disabled?: boolean;
      children?: any[]; // 用于级联选择
    }>;
    multiple?: boolean; // 是否多选
    allowClear?: boolean; // 是否显示清除按钮

    // 输入类组件配置
    maxLength?: number; // 最大长度
    minLength?: number; // 最小长度
    pattern?: string; // 正则表达式

    // 数字输入配置
    min?: number; // 最小值
    max?: number; // 最大值
    step?: number; // 步长
    precision?: number; // 精度

    // 日期组件配置
    format?: string; // 日期格式
    showTime?: boolean; // 是否显示时间
    disabledDate?: string; // 禁用日期规则

    // 上传组件配置
    accept?: string; // 接受的文件类型
    maxSize?: number; // 最大文件大小
    maxCount?: number; // 最大文件数量
    uploadUrl?: string; // 上传地址
  };

  // 验证规则
  rules?: Array<{
    required?: boolean;
    message?: string;
    pattern?: string;
    min?: number;
    max?: number;
    type?: 'string' | 'number' | 'email' | 'url';
    validator?: string; // 自定义验证器名称
  }>;

  // 联动配置
  linkage?: {
    trigger: string | string[]; // 触发字段
    condition: {
      // 触发条件
      type: 'value' | 'empty' | 'notempty' | 'in' | 'notin';
      value?: any;
      values?: any[];
    };
    action: 'show' | 'hide' | 'required' | 'optional' | 'enable' | 'disable';
    options?: {
      // 动态选项配置
      mapping?: Record<string, any[]>;
      url?: string; // 动态获取选项的 URL
      params?: Record<string, any>;
    };
  };

  // 布局配置
  layout?: {
    span?: number; // 栅格占位格数
    offset?: number; // 栅格左侧的间隔格数
    order?: number; // 栅格顺序
    labelCol?: {
      // 标签布局
      span?: number;
      offset?: number;
    };
    wrapperCol?: {
      // 控件布局
      span?: number;
      offset?: number;
    };
  };
}
```

#### 2.5 详细使用示例

```typescript
// 复杂表单配置示例
const complexFormConfig = [
  {
    field: 'username',
    type: 'input',
    title: '用户名',
    required: true,
    placeholder: '请输入用户名',
    config: {
      maxLength: 20,
      minLength: 3,
    },
    rules: [
      { required: true, message: '请输入用户名' },
      { min: 3, message: '用户名至少3个字符' },
      { pattern: '^[a-zA-Z0-9_]+$', message: '只能包含字母、数字和下划线' },
    ],
  },
  {
    field: 'email',
    type: 'input',
    title: '邮箱',
    required: true,
    placeholder: '请输入邮箱地址',
    rules: [
      { required: true, message: '请输入邮箱' },
      { type: 'email', message: '请输入有效的邮箱地址' },
    ],
  },
  {
    field: 'department',
    type: 'apiselect',
    title: '部门',
    required: true,
    config: {
      url: '/api/departments',
      labelField: 'name',
      valueField: 'id',
      showSearch: true,
      searchFieldName: 'keyword',
      immediate: true,
    },
  },
  {
    field: 'position',
    type: 'select',
    title: '职位',
    required: true,
    linkage: {
      trigger: 'department',
      condition: { type: 'notempty' },
      action: 'show',
      options: {
        url: '/api/positions',
        params: { departmentId: '${department}' },
      },
    },
  },
  {
    field: 'salary',
    type: 'number',
    title: '薪资',
    config: {
      min: 0,
      max: 1000000,
      precision: 2,
      step: 100,
    },
  },
  {
    field: 'birthday',
    type: 'date',
    title: '生日',
    config: {
      format: 'YYYY-MM-DD',
      disabledDate: 'future', // 禁用未来日期
    },
  },
  {
    field: 'skills',
    type: 'checkbox',
    title: '技能',
    config: {
      options: [
        { label: 'JavaScript', value: 'js' },
        { label: 'TypeScript', value: 'ts' },
        { label: 'Vue.js', value: 'vue' },
        { label: 'React', value: 'react' },
      ],
    },
  },
  {
    field: 'avatar',
    type: 'upload',
    title: '头像',
    config: {
      accept: 'image/*',
      maxSize: 2, // 2MB
      maxCount: 1,
      uploadUrl: '/api/upload/avatar',
    },
  },
];
```

### 使用示例

#### 基础数组格式

```typescript
import { transformBackendSearchToSchema } from '#/utils/search-schema';

// 后端数据
const backendFormData = [
  {
    field: 'username',
    type: 'input',
    title: '用户名',
    required: true,
    config: {
      placeholder: '请输入用户名',
    },
  },
  {
    field: 'department',
    type: 'apiselect',
    title: '部门',
    config: {
      url: '/api/departments',
      labelField: 'name',
      valueField: 'id',
    },
  },
];

// 基础转换
const formSchema = transformBackendSearchToSchema(backendFormData);

// 带表单模式的转换（新功能）
const addFormSchema = transformBackendSearchToSchema(backendFormData, {
  formMode: 'add', // 新增模式
});

const editFormSchema = transformBackendSearchToSchema(backendFormData, {
  formMode: 'edit', // 编辑模式
});
```

#### 编辑权限控制示例

```typescript
// 支持编辑权限控制的字段配置
const fieldsWithEditPermission = [
  {
    field: 'user_code',
    type: 'input',
    title: '用户编码',
    config: {
      placeholder: '请输入用户编码',
      editPermission: 'add-only', // 只在新增时可编辑
    },
  },
  {
    field: 'created_at',
    type: 'date',
    title: '创建时间',
    config: {
      editPermission: 'edit-only', // 只在编辑时显示
    },
  },
  {
    field: 'system_field',
    type: 'input',
    title: '系统字段',
    config: {
      editPermission: 'none', // 完全只读
    },
  },
];

// 新增模式：user_code可编辑，created_at不显示，system_field只读
const addSchema = transformBackendSearchToSchema(fieldsWithEditPermission, {
  formMode: 'add',
});

// 编辑模式：user_code只读，created_at显示，system_field只读
const editSchema = transformBackendSearchToSchema(fieldsWithEditPermission, {
  formMode: 'edit',
});
```

#### 联动赋值配置示例

```typescript
// 支持联动赋值的字段配置
const fieldsWithLinkageAssignment = [
  {
    field: 'department_id',
    type: 'apiSelect',
    title: '部门',
    config: {
      url: '/api/departments/list',
      labelField: 'name',
      valueField: 'id',

      // 联动赋值配置
      linkageAssignment: {
        targetFields: [
          {
            field: 'manager_id',
            valueMapping: (selectedValue, selectedOption) => {
              // 根据选中的部门，自动设置部门经理
              return selectedOption?.manager_id || null;
            },
            clearOnEmpty: true,
          },
          {
            field: 'department_code',
            valueMapping: (selectedValue, selectedOption) => {
              // 根据选中的部门，自动设置部门编码
              return selectedOption?.code || null;
            },
            clearOnEmpty: true,
          },
          {
            field: 'location',
            valueMapping: '总部大楼', // 固定值
            clearOnEmpty: false,
          },
        ],
      },
    },
  },
  {
    field: 'manager_id',
    type: 'input',
    title: '部门经理ID',
    config: {
      disabled: true, // 通常设为只读，由联动赋值自动填充
    },
  },
  {
    field: 'department_code',
    type: 'input',
    title: '部门编码',
    config: {
      disabled: true,
    },
  },
  {
    field: 'location',
    type: 'input',
    title: '办公地点',
  },
];

// 转换后，选择部门时会自动填充相关字段
const schemaWithAssignment = transformBackendSearchToSchema(
  fieldsWithLinkageAssignment,
);
```

#### 分组数据格式（编辑新增页面）

```typescript
// 分组数据格式 - 编辑新增页面会使用第二组数据
const groupedFormData = [
  {
    label: '搜索条件',
    dataItem: [
      {
        field: 'searchName',
        type: 'input',
        title: '搜索姓名',
        config: { placeholder: '请输入搜索姓名' },
      },
    ],
  },
  {
    label: '编辑表单', // 第二组 - 用于编辑新增页面
    dataItem: [
      {
        field: 'username',
        type: 'input',
        title: '用户名',
        required: true,
        config: { placeholder: '请输入用户名' },
      },
      {
        field: 'email',
        type: 'input',
        title: '邮箱',
        required: true,
        config: { placeholder: '请输入邮箱' },
      },
      {
        field: 'department',
        type: 'apiselect',
        title: '部门',
        config: {
          url: '/api/departments',
          labelField: 'name',
          valueField: 'id',
        },
      },
    ],
  },
];

// 转换所有分组（包含分组标题）
const allFormSchema = transformBackendSearchToSchema(groupedFormData, {
  enableGrouping: true,
});

// 转换所有分组（不包含分组标题）
const flatFormSchema = transformBackendSearchToSchema(groupedFormData, {
  enableGrouping: false,
});

// 编辑新增页面通常使用第二组数据
// 在实际应用中，general-editing-drawer 组件会自动提取第二组数据用于表单展示
```

#### 包装格式的分组数据

```typescript
// 包装在 schema 属性中的分组格式
const wrappedGroupedData = {
  schema: [
    {
      label: '基础信息',
      dataItem: [
        {
          field: 'name',
          type: 'input',
          title: '姓名',
          required: true,
        },
      ],
    },
    {
      label: '详细信息', // 第二组 - 用于编辑新增页面
      dataItem: [
        {
          field: 'bio',
          type: 'textarea',
          title: '个人简介',
          config: { rows: 4 },
        },
        {
          field: 'skills',
          type: 'select',
          title: '技能',
          config: {
            mode: 'multiple',
            options: [
              { label: 'JavaScript', value: 'js' },
              { label: 'TypeScript', value: 'ts' },
              { label: 'Vue', value: 'vue' },
            ],
          },
        },
      ],
    },
  ],
};

// 转换
const wrappedFormSchema = transformBackendSearchToSchema(wrappedGroupedData);
```

### 特殊功能

- **API 函数生成**: 自动为 API 组件生成请求函数
- **搜索支持**: ApiSelect 支持搜索和滚动加载
- **联动支持**: 支持字段间的依赖关系
- **默认值处理**: 正确处理各种默认值格式
- **编辑权限控制**: 根据表单模式（新增/编辑）控制字段的可编辑性
- **联动赋值**: 支持选择某个字段后自动给其他字段赋值

### 新功能详解

#### 编辑权限控制 (editPermission)

通过在字段配置的 `config.editPermission` 中设置权限类型，可以控制字段在不同表单模式下的行为：

- `'both'`（默认）：新增和编辑都可以填写
- `'add-only'`：只有新增时可以填写，编辑时不可填写
- `'edit-only'`：只有编辑时可以填写，新增时不可填写
- `'none'`：新增和编辑都不可填写（只读显示）

**使用场景：**

- 用户编码：新增时可填写，编辑时不可修改
- 创建时间：只在编辑时显示，新增时不显示
- 系统字段：完全只读，不允许用户修改

#### 联动赋值 (linkageAssignment)

通过在字段配置的 `config.linkageAssignment` 中设置目标字段映射，可以实现选择某个字段后自动给其他字段赋值：

**配置结构：**

```typescript
linkageAssignment: {
  targetFields: [
    {
      field: string;  // 目标字段名
      valueMapping: Function | any;  // 值映射函数或固定值
      clearOnEmpty?: boolean;  // 当前字段清空时是否清空目标字段
    }
  ]
}
```

**支持的组件类型：**

- `select`：下拉选择
- `apiSelect`：API下拉选择
- `apiselect`：API下拉选择（别名）
- `tree`：树形选择
- `apiTree`：API树形选择
- `radio`：单选按钮组

**详细配置说明：**

1. **valueMapping 函数形式**：

   ```typescript
   valueMapping: (selectedValue, selectedOption) => {
     // selectedValue: 当前选中的值
     // selectedOption: 当前选中的选项对象（包含 label、value 等信息）
     return selectedOption?.manager_id || null;
   };
   ```

2. **valueMapping 固定值形式**：

   ```typescript
   valueMapping: '总部大楼'; // 直接设置固定值
   ```

3. **clearOnEmpty 配置**：
   - `true`（默认）：当前字段清空时，清空目标字段
   - `false`：当前字段清空时，保持目标字段的值不变

**重要提醒：PHP 函数序列化问题**

⚠️ **注意**：如果你使用 PHP 后端，PHP 函数无法直接序列化为 JSON。当后端返回包含函数的 `linkageAssignment` 配置时，前端接收到的 `valueMapping` 会是空对象 `{}`。

**解决方案：**

1. **使用字符串表示法**（推荐）：

   ```php
   'valueMapping' => 'selectedOption.manager_id'  // 使用属性路径字符串
   ```

2. **使用映射配置**：

   ```php
   'valueMapping' => [
       'type' => 'property',
       'path' => 'manager_id'  // 从 selectedOption 中提取的属性名
   ]
   ```

3. **在前端处理**：

   ```php
   // 后端只返回配置信息
   'valueMapping' => [
       'source' => 'selectedOption',
       'property' => 'manager_id'
   ]
   ```

   ```javascript
   // 前端根据配置生成函数
   const valueMapping = (selectedValue, selectedOption) => {
     return selectedOption?.[config.property] || null;
   };
   ```

4. **使用固定值映射**：
   ```php
   'valueMapping' => [
       '1' => 'manager_001',  // 部门ID为1时，设置经理ID为manager_001
       '2' => 'manager_002',  // 部门ID为2时，设置经理ID为manager_002
       'default' => null      // 默认值
   ]
   ```

**前端实现原理：**

转换方法会自动为配置了 `linkageAssignment` 的字段创建 `dependencies` 配置，在字段值变化时触发联动赋值逻辑：

```typescript
// 转换后的 schema 结构
{
  field: 'department_id',
  component: 'ApiSelect',
  dependencies: {
    triggerFields: ['department_id'],
    trigger: (values, formApi) => {
      // 自动执行联动赋值逻辑
      const currentValue = values.department_id;
      const targetFields = linkageAssignment.targetFields;

      targetFields.forEach(targetField => {
        if (currentValue) {
          // 计算目标字段值
          const targetValue = typeof targetField.valueMapping === 'function'
            ? targetField.valueMapping(currentValue, selectedOption)
            : targetField.valueMapping;

          // 设置目标字段值
          formApi.setFieldValue(targetField.field, targetValue);
        } else if (targetField.clearOnEmpty !== false) {
          // 清空目标字段
          formApi.setFieldValue(targetField.field, undefined);
        }
      });
    }
  }
}
```

**使用场景：**

- 选择部门后自动填充部门经理、部门编码等信息
- 选择商品后自动填充商品价格、规格等信息
- 选择地区后自动填充邮政编码、区号等信息
- 选择客户后自动填充客户联系人、地址等信息

## 3. transformToGroupedDescriptions

### 功能概述

`transformToGroupedDescriptions` 是详情页面数据转换的核心方法，负责将后端返回的详情字段配置转换为分组描述列表格式。该方法专门用于详情页面的数据展示，支持字段分组、特殊字段渲染、布局控制等功能。

### 文件位置

```
apps/web-antd/src/utils/search-schema/transform.ts
```

### 方法签名

```typescript
function transformToGroupedDescriptions(data: any[]): GroupedDescriptions[];
```

### 详细功能说明

#### 3.1 分组机制

系统支持灵活的字段分组显示，可以根据业务逻辑将相关字段组织在一起：

```typescript
// 后端配置示例
const backendDetailData = [
  {
    field: 'name',
    title: '姓名',
    group: 'basic', // 分组标识
    groupTitle: '基本信息', // 分组标题
  },
  {
    field: 'email',
    title: '邮箱',
    group: 'basic',
  },
  {
    field: 'department',
    title: '部门',
    group: 'work',
    groupTitle: '工作信息',
  },
  {
    field: 'position',
    title: '职位',
    group: 'work',
  },
];

// 转换后的分组结构
[
  {
    title: '基本信息',
    column: 2,
    items: [
      { field: 'name', label: '姓名' },
      { field: 'email', label: '邮箱' },
    ],
  },
  {
    title: '工作信息',
    column: 2,
    items: [
      { field: 'department', label: '部门' },
      { field: 'position', label: '职位' },
    ],
  },
];
```

#### 3.2 特殊字段类型处理

支持多种特殊字段类型的智能渲染：

| 字段类型      | 处理方式   | 说明                  |
| ------------- | ---------- | --------------------- |
| `text`        | 普通文本   | 直接显示文本内容      |
| `multitext`   | 多值文本   | 使用 div 元素避免截断 |
| `select`      | 选项映射   | 将值转换为对应标签    |
| `multiselect` | 多选项映射 | 显示多个标签          |
| `date`        | 日期格式化 | 格式化日期显示        |
| `currency`    | 货币格式化 | 添加货币符号和格式化  |
| `status`      | 状态标签   | 带颜色的状态标签      |
| `image`       | 图片显示   | 支持图片预览          |
| `file`        | 文件链接   | 支持文件下载          |
| `link`        | 超链接     | 可点击的链接          |
| `tag`         | 标签列表   | 多个标签的列表显示    |

#### 3.3 布局控制

支持灵活的布局配置，包括列数控制、跨列显示等：

```typescript
// 布局配置示例
{
  field: 'description',
  title: '详细描述',
  type: 'multitext',
  span: 2,           // 跨2列显示
  group: 'detail',
  layout: {
    column: 1        // 该分组每行显示1列
  }
}
```

#### 3.4 完整的后端详情字段配置格式

```typescript
interface BackendDetailField {
  field: string; // 必填：字段名
  title: string; // 必填：字段标题
  type?: string; // 可选：字段类型
  group?: string; // 可选：分组标识
  groupTitle?: string; // 可选：分组标题
  span?: number; // 可选：跨列数（1-4）
  order?: number; // 可选：显示顺序

  // 选项配置（用于选择类字段）
  options?: Array<{
    label: string; // 显示文本
    value: any; // 实际值
    color?: string; // 颜色（用于状态标签）
    icon?: string; // 图标
  }>;

  // 格式化配置
  formatter?: {
    type: 'date' | 'currency' | 'number' | 'percent';
    format?: string; // 格式化模板
    precision?: number; // 精度
    currency?: string; // 货币符号
    dateFormat?: string; // 日期格式
  };

  // 布局配置
  layout?: {
    column?: number; // 该分组每行显示列数
    labelWidth?: string; // 标签宽度
    contentWidth?: string; // 内容宽度
  };

  // 渲染配置
  render?: {
    type: 'text' | 'html' | 'component';
    component?: string; // 自定义组件名
    props?: Record<string, any>; // 组件属性
  };

  // 条件显示
  condition?: {
    field: string; // 依赖字段
    value: any; // 条件值
    operator: '=' | '!=' | '>' | '<' | 'in' | 'notin';
  };
}
```

### 数据结构

```typescript
interface GroupedDescriptions {
  title?: string; // 分组标题
  column?: number; // 每行显示列数
  items: DescriptionItem[]; // 描述项列表
}

interface DescriptionItem {
  field: string; // 字段名
  label: string; // 显示标签
  span?: number; // 跨列数
  // ... 其他属性
}
```

### 使用示例

```typescript
import { transformToGroupedDescriptions } from '#/utils/search-schema';

// 后端数据
const backendDetailData = [
  {
    field: 'name',
    title: '姓名',
    type: 'text',
  },
  {
    field: 'department',
    title: '部门',
    type: 'dept',
    options: [
      { label: '技术部', value: 1 },
      { label: '销售部', value: 2 },
    ],
  },
];

// 转换
const groupedDescriptions = transformToGroupedDescriptions(backendDetailData);
```

### 特殊处理机制

- **多值字段**: 使用 div 元素显示，避免文本截断
- **选项映射**: 自动将值转换为对应的标签显示
- **空值处理**: 统一处理空值的显示方式
- **分组标题**: 支持自定义分组标题和布局

## 4. 完整示例

### 4.1 综合使用示例

以下是一个完整的用户管理页面示例，展示了三个转换方法的综合使用：

```typescript
import {
  transformColumns,
  transformBackendSearchToSchema,
  transformToGroupedDescriptions,
} from '#/utils/search-schema';

// 1. 表格列配置
const userTableColumns = [
  {
    field: 'id',
    title: 'ID',
    width: 80,
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'avatar',
    title: '头像',
    width: 80,
    type: 'avatar',
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    sortable: true,
  },
  {
    field: 'email',
    title: '邮箱',
    width: 200,
  },
  {
    field: 'department',
    title: '部门',
    width: 150,
    type: 'dept',
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    type: 'status',
    options: [
      { label: '在职', value: 1, color: 'green' },
      { label: '离职', value: 0, color: 'red' },
    ],
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 160,
    type: 'date',
  },
];

// 2. 搜索表单配置
const userSearchForm = [
  {
    field: 'keyword',
    type: 'input',
    title: '关键词',
    placeholder: '请输入姓名或邮箱',
  },
  {
    field: 'departmentId',
    type: 'apiselect',
    title: '部门',
    config: {
      url: '/api/departments',
      labelField: 'name',
      valueField: 'id',
      showSearch: true,
    },
  },
  {
    field: 'status',
    type: 'select',
    title: '状态',
    config: {
      options: [
        { label: '全部', value: '' },
        { label: '在职', value: 1 },
        { label: '离职', value: 0 },
      ],
    },
  },
  {
    field: 'dateRange',
    type: 'daterange',
    title: '创建时间',
    config: {
      format: 'YYYY-MM-DD',
    },
  },
];

// 3. 详情页配置
const userDetailConfig = [
  // 基本信息
  {
    field: 'id',
    title: 'ID',
    group: 'basic',
    groupTitle: '基本信息',
  },
  {
    field: 'avatar',
    title: '头像',
    type: 'image',
    group: 'basic',
  },
  {
    field: 'name',
    title: '姓名',
    group: 'basic',
  },
  {
    field: 'email',
    title: '邮箱',
    type: 'link',
    group: 'basic',
  },

  // 工作信息
  {
    field: 'department',
    title: '部门',
    type: 'dept',
    group: 'work',
    groupTitle: '工作信息',
  },
  {
    field: 'position',
    title: '职位',
    group: 'work',
  },
  {
    field: 'salary',
    title: '薪资',
    type: 'currency',
    group: 'work',
  },
  {
    field: 'status',
    title: '状态',
    type: 'status',
    group: 'work',
    options: [
      { label: '在职', value: 1, color: 'green' },
      { label: '离职', value: 0, color: 'red' },
    ],
  },

  // 详细信息
  {
    field: 'bio',
    title: '个人简介',
    type: 'multitext',
    group: 'detail',
    groupTitle: '详细信息',
    span: 2,
  },
];

// 转换配置
const tableColumns = transformColumns(userTableColumns);
const searchSchema = transformBackendSearchToSchema(userSearchForm);
const detailDescriptions = transformToGroupedDescriptions(userDetailConfig);

// 在 Vue 组件中使用
export default {
  setup() {
    return {
      tableColumns,
      searchSchema,
      detailDescriptions,
    };
  },
};
```

## 5. 常见问题

### 5.1 表格相关问题

**Q: 操作列没有自动添加怎么办？** A: 确保数据行中包含 `actionBtnList` 字段，系统会自动检测并添加操作列。

**Q: 如何自定义列的渲染方式？** A: 可以在转换后的列配置中添加 `slots` 属性来指定自定义插槽。

**Q: 表格列宽度不生效？** A: 检查是否设置了 `minWidth` 或 `maxWidth`，这些属性可能会覆盖 `width` 设置。

### 5.2 表单相关问题

**Q: API 组件没有数据怎么办？** A: 检查 `url` 配置是否正确，以及 `immediate` 是否设置为 `true`。

**Q: 表单联动不生效？** A: 确保 `linkage.trigger` 字段名正确，并且触发条件配置正确。

**Q: 选项数据格式不对？** A: 系统支持多种选项格式，确保使用 `label` 和 `value` 字段，或者配置正确的 `labelField` 和 `valueField`。

### 5.3 详情页相关问题

**Q: 分组标题不显示？** A: 确保在第一个字段中设置了 `groupTitle` 属性。

**Q: 字段显示顺序不对？** A: 可以使用 `order` 属性来控制字段的显示顺序。

**Q: 多值字段显示被截断？** A: 使用 `type: 'multitext'` 来避免文本截断问题。

### 5.4 性能优化建议

1. **缓存转换结果**: 对于不经常变化的配置，建议缓存转换结果
2. **按需加载**: 大量数据时考虑分页或虚拟滚动
3. **减少重复转换**: 避免在组件更新时重复执行转换
4. **合理使用联动**: 过多的联动关系可能影响性能

### 5.5 调试技巧

1. **开启调试模式**: 在开发环境下可以打印转换前后的数据对比
2. **检查数据格式**: 确保后端返回的数据格式符合预期
3. **逐步测试**: 先测试简单配置，再逐步添加复杂功能
4. **使用浏览器开发工具**: 检查生成的 DOM 结构和样式

## 6. 注意事项

1. **数据格式**: 确保后端返回的数据格式符合接口定义
2. **版本兼容**: 注意不同版本间的兼容性问题
3. **性能考虑**: 大量数据转换时注意性能影响
4. **内存管理**: 及时清理不需要的缓存数据
5. **类型检查**: 建议在 TypeScript 环境下使用以获得更好的类型安全
6. **错误处理**: 在生产环境中添加适当的错误处理和降级方案

## 通用特性

### 1. 错误处理

所有转换方法都包含完善的错误处理机制：

- 输入验证
- 异常捕获
- 降级处理
- 用户友好的错误提示

### 2. 类型安全

- 完整的 TypeScript 类型定义
- 运行时类型检查
- 类型推导支持

### 3. 扩展性

- 支持自定义转换规则
- 插件化架构
- 向后兼容

### 4. 性能优化

- 缓存机制
- 懒加载
- 防抖处理

## 最佳实践

### 1. 数据验证

在使用转换方法前，建议先验证后端数据格式：

```typescript
if (!Array.isArray(backendData) || backendData.length === 0) {
  return [];
}
```

### 2. 错误边界

在组件中使用时，建议添加错误边界：

```typescript
try {
  const schema = transformBackendSearchToSchema(backendData);
  // 使用 schema
} catch (error) {
  // 降级处理
}
```

### 3. 缓存优化

对于不经常变化的数据，可以考虑缓存转换结果：

```typescript
const cacheKey = JSON.stringify(backendData);
if (cache.has(cacheKey)) {
  return cache.get(cacheKey);
}
const result = transformColumns(backendData);
cache.set(cacheKey, result);
return result;
```

## API 详细说明

### transformColumns 详细配置

#### 输入数据格式

```typescript
interface BackendColumn {
  field: string; // 字段名
  title: string; // 列标题
  width?: number; // 列宽度
  type?: string; // 列类型
  fixed?: boolean; // 是否固定
  sortable?: boolean; // 是否可排序
  options?: any[]; // 选项数据（用于选择类型）
  actionBtnList?: Array<{
    // 操作按钮列表
    title: string; // 按钮标题
    type: string; // 按钮类型
    key?: string; // 按钮标识
  }>;
}
```

#### 输出数据格式

```typescript
interface VxeColumn {
  field: string;
  title: string;
  width?: number;
  fixed?: 'left' | 'right';
  sortable?: boolean;
  slots?: {
    default?: string;
  };
  // ... 其他 VXE 表格列属性
}
```

### transformBackendSearchToSchema 详细配置

#### 输入数据格式

```typescript
interface BackendFormField {
  field: string; // 字段名
  type: string; // 组件类型
  title: string; // 字段标题
  required?: boolean; // 是否必填
  default?: any; // 默认值
  config?: {
    // 组件配置
    placeholder?: string;
    url?: string; // API 地址（用于 API 组件）
    labelField?: string; // 标签字段名
    valueField?: string; // 值字段名
    options?: any[]; // 选项数据
    showSearch?: boolean; // 是否显示搜索
    multiple?: boolean; // 是否多选
    // ... 其他配置
  };
  linkage?: {
    // 联动配置
    trigger: string; // 触发字段
    condition: any; // 触发条件
    action: 'show' | 'hide' | 'required'; // 动作
  };
}
```

#### 输出数据格式

```typescript
interface FormSchema {
  field: string;
  label: string;
  component: string;
  required?: boolean;
  defaultValue?: any;
  componentProps?: any;
  dependencies?: {
    triggerFields: string[];
    if: (values: any) => boolean;
    show?: boolean;
    required?: boolean;
  };
}
```

### transformToGroupedDescriptions 详细配置

#### 输入数据格式

```typescript
interface BackendDetailField {
  field: string; // 字段名
  title: string; // 字段标题
  type?: string; // 字段类型
  span?: number; // 跨列数
  group?: string; // 分组名称
  options?: Array<{
    // 选项数据
    label: string;
    value: any;
  }>;
}
```

#### 输出数据格式

```typescript
interface GroupedDescriptions {
  title?: string; // 分组标题
  column?: number; // 每行列数
  items: Array<{
    field: string;
    label: string;
    span?: number;
    render?: (value: any) => string; // 自定义渲染函数
  }>;
}
```

## 高级用法示例

### 1. 自定义列渲染

```typescript
const columns = transformColumns(backendColumns);
// 添加自定义渲染
columns.forEach((col) => {
  if (col.field === 'status') {
    col.slots = { default: 'status_slot' };
  }
});
```

### 2. 动态表单联动

```typescript
const schema = transformBackendSearchToSchema(backendFormData);
// 表单会自动处理联动逻辑
```

### 3. 分组详情展示

```typescript
const descriptions = transformToGroupedDescriptions(backendDetailData);
// 可以直接用于 DetailDescriptions 组件
```

## 注意事项

1. **数据格式**: 确保后端返回的数据格式符合预期
2. **版本兼容**: 注意不同版本间的兼容性问题
3. **性能考虑**: 大量数据转换时注意性能影响
4. **内存管理**: 及时清理不需要的缓存数据
5. **类型检查**: 建议在 TypeScript 环境下使用以获得更好的类型安全
