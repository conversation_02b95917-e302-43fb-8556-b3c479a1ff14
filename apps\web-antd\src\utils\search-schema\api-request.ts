import { requestClient } from '#/api/request';

/**
 * 通用的 API 请求函数
 * 根据传入的 URL 和参数进行请求
 * @param url API 请求地址
 * @param params 请求参数
 * @returns Promise<any> 请求结果
 */
export async function commonApiRequest(
  url: string,
  params: Record<string, any> = {},
): Promise<any> {
  // 过滤掉空值参数，避免发送 id= 这样的无效参数
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_key, value]) => {
      // 保留有效值：不是 undefined、null、空字符串或只有空格的字符串
      return (
        value !== undefined &&
        value !== null &&
        value !== '' &&
        !(typeof value === 'string' && value.trim() === '')
      );
    }),
  );

  const response = await requestClient.get(`${url}`, {
    params: filteredParams,
  });
  // 返回 response.items，如果不存在则返回整个 response
  return response.items === undefined ? response : response.items;
}

/**
 * 创建类似 listAction 的 API 请求函数
 * 生成一个与您选中代码相同格式的请求函数
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @returns API 请求函数，格式类似 listAction
 */
export function createListActionFunction(
  url: string,
  defaultParams: Record<string, any> = {},
) {
  return async (params: any = {}) => {
    const mergedParams = { ...defaultParams, ...params };
    const response = await requestClient.get(`${url}`, {
      params: mergedParams,
    });
    // 返回 response.items，如果不存在则返回整个 response
    return response.items === undefined ? response : response.items;
  };
}

/**
 * 创建支持搜索的类似 listAction 的 API 请求函数
 * 生成一个与您选中代码相同格式的搜索请求函数
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @param searchParamName 搜索参数名，默认为 'search'
 * @returns 支持搜索的 API 请求函数
 */
export function createSearchableListActionFunction(
  url: string,
  defaultParams: Record<string, any> = {},
  searchParamName: string = 'search',
) {
  return async (params: any = {}) => {
    const mergedParams = { ...defaultParams, ...params };

    // 处理搜索参数
    const possibleSearchParams = [
      searchParamName,
      'search',
      'keyword',
      'query',
      'searchValue',
      'inputValue',
    ];

    for (const paramName of possibleSearchParams) {
      if (params[paramName] && typeof params[paramName] === 'string') {
        mergedParams[searchParamName] = params[paramName];
        break;
      }
    }

    const response = await requestClient.get(`${url}`, {
      params: mergedParams,
    });
    // 返回 response.items，如果不存在则返回整个 response
    return response.items === undefined ? response : response.items;
  };
}

/**
 * 创建 API 请求函数
 * 返回一个可以被 ApiSelect 或 ApiTreeSelect 使用的函数
 * 支持搜索和动态参数传递
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @returns API 请求函数
 */
export function createApiFunction(
  url: string,
  defaultParams: Record<string, any> = {},
) {
  return async (params: Record<string, any> = {}) => {
    const mergedParams = { ...defaultParams, ...params };
    return await commonApiRequest(url, mergedParams);
  };
}

/**
 * 创建支持搜索的 API 请求函数
 * 专门为 ApiSelect 和 ApiTreeSelect 的搜索功能设计
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @param searchParamName 搜索参数名，默认为 'search'
 * @param _debounceMs 防抖延迟时间，默认为 300ms（暂未使用）
 * @returns 支持搜索的 API 请求函数
 */
export function createSearchableApiFunction(
  url: string,
  defaultParams: Record<string, any> = {},
  searchParamName: string = 'search',
  _debounceMs: number = 300,
) {
  return async (params: Record<string, any> = {}) => {
    const mergedParams = { ...defaultParams, ...params };

    // 处理搜索参数
    // ApiSelect 和 ApiTreeSelect 组件在搜索时会传递不同的参数
    // 我们需要检查多种可能的搜索参数名
    const possibleSearchParams = [
      searchParamName,
      'search',
      'keyword',
      'query',
      'searchValue',
      'inputValue',
    ];

    for (const paramName of possibleSearchParams) {
      if (params[paramName] && typeof params[paramName] === 'string') {
        mergedParams[searchParamName] = params[paramName];
        break;
      }
    }

    return await commonApiRequest(url, mergedParams);
  };
}

/**
 * 创建支持分页的 API 请求函数
 * 支持分页加载和搜索功能
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @param options 分页和搜索配置
 * @param options.loadMore 是否支持加载更多
 * @param options.pageParamName 页码参数名
 * @param options.pageSize 每页大小
 * @param options.pageSizeParamName 每页大小参数名
 * @param options.searchParamName 搜索参数名
 * @returns 支持分页的 API 请求函数
 */
export function createPaginatedApiFunction(
  url: string,
  defaultParams: Record<string, any> = {},
  options: {
    loadMore?: boolean;
    pageParamName?: string;
    pageSize?: number;
    pageSizeParamName?: string;
    searchParamName?: string;
  } = {},
) {
  const {
    searchParamName = 'search',
    pageParamName = 'page',
    pageSizeParamName = 'pageSize',
    pageSize = 20,
    loadMore = true,
  } = options;

  // 缓存已加载的数据
  let cachedData: any[] = [];
  let currentPage = 1;
  let hasMore = true;
  let lastSearchValue = '';

  return async (params: Record<string, any> = {}) => {
    const mergedParams = { ...defaultParams, ...params };

    // 处理搜索参数
    const possibleSearchParams = [
      searchParamName,
      'search',
      'keyword',
      'query',
      'searchValue',
      'inputValue',
    ];

    let searchValue = '';
    for (const paramName of possibleSearchParams) {
      if (params[paramName] && typeof params[paramName] === 'string') {
        searchValue = params[paramName];
        mergedParams[searchParamName] = searchValue;
        break;
      }
    }

    // 检查是否是新的搜索，如果是则重置分页状态
    if (searchValue !== lastSearchValue) {
      cachedData = [];
      currentPage = 1;
      hasMore = true;
      lastSearchValue = searchValue;
    }

    // 处理分页参数
    const requestPage = params[pageParamName] || currentPage;
    mergedParams[pageParamName] = requestPage;
    mergedParams[pageSizeParamName] = params[pageSizeParamName] || pageSize;

    const response = await commonApiRequest(url, mergedParams);

    // 处理响应数据
    let responseData = response;
    let totalCount = 0;
    let currentPageData: any[] = [];

    // 尝试从不同的响应格式中提取数据
    if (response && typeof response === 'object') {
      // 格式1: { data: { list: [], total: 100 } }
      if (response.data && response.data.list) {
        currentPageData = response.data.list;
        totalCount = response.data.total || 0;
      }
      // 格式2: { list: [], total: 100 }
      else if (response.list) {
        currentPageData = response.list;
        totalCount = response.total || 0;
      }
      // 格式3: { data: [] }
      else if (response.data && Array.isArray(response.data)) {
        currentPageData = response.data;
        totalCount = response.total || Math.max(currentPageData.length, 0);
      }
      // 格式4: 直接是数组
      else if (Array.isArray(response)) {
        currentPageData = response;
        totalCount = response.length;
      }
    }

    // 更新分页状态
    if (loadMore && requestPage === currentPage) {
      // 加载更多模式：累积数据
      cachedData =
        requestPage === 1
          ? [...currentPageData]
          : [...cachedData, ...currentPageData];

      // 检查是否还有更多数据
      hasMore =
        currentPageData.length >= pageSize &&
        (totalCount === 0 || cachedData.length < totalCount);

      if (hasMore) {
        currentPage++;
      }

      // 返回累积的数据
      responseData = Array.isArray(response)
        ? cachedData
        : {
            ...response,
            data: response.data
              ? { ...response.data, list: cachedData }
              : cachedData,
            list: cachedData,
            hasMore,
            total: totalCount,
          };
    } else {
      // 普通分页模式：返回当前页数据
      hasMore =
        currentPageData.length >= pageSize &&
        (totalCount === 0 || requestPage * pageSize < totalCount);

      responseData = Array.isArray(response)
        ? currentPageData
        : {
            ...response,
            hasMore,
            total: totalCount,
          };
    }

    return responseData;
  };
}

/**
 * 创建支持搜索和分页的 API 请求函数
 * 结合搜索和分页功能的完整解决方案
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @param config 配置选项
 * @param config.loadMore 是否支持加载更多
 * @param config.pageParamName 页码参数名
 * @param config.pageSize 每页大小
 * @param config.pageSizeParamName 每页大小参数名
 * @param config.pagination 分页配置
 * @param config.searchable 是否支持搜索
 * @param config.searchParamName 搜索参数名
 * @returns 完整的 API 请求函数
 */
export function createAdvancedApiFunction(
  url: string,
  defaultParams: Record<string, any> = {},
  config: {
    loadMore?: boolean;
    pageParamName?: string;

    pageSize?: number;
    pageSizeParamName?: string;
    // 分页配置
    pagination?: boolean;
    // 搜索配置
    searchable?: boolean;
    searchParamName?: string;
  } = {},
) {
  const {
    searchable = false,
    pagination = false,
    loadMore = true,
    ...restConfig
  } = config;

  // 如果启用分页，使用分页函数
  if (pagination) {
    return createPaginatedApiFunction(url, defaultParams, {
      ...restConfig,
      loadMore,
    });
  }

  // 如果只启用搜索，使用搜索函数
  if (searchable) {
    return createSearchableApiFunction(
      url,
      defaultParams,
      config.searchParamName,
    );
  }

  // 否则使用基础函数
  return createApiFunction(url, defaultParams);
}

/**
 * 默认的树节点过滤函数
 * @param inputValue 输入的搜索值
 * @param treeNode 树节点
 * @returns 是否匹配
 */
export function defaultFilterTreeNode(
  inputValue: string,
  treeNode: any,
): boolean {
  if (!inputValue) return true;

  const searchValue = inputValue.toLowerCase();
  const nodeTitle = (treeNode.title || treeNode.label || '').toLowerCase();

  return nodeTitle.includes(searchValue);
}

/**
 * 创建支持 ApiSelect 滚动加载的 API 请求函数
 * 专门为 ApiSelect 组件设计的分页加载功能
 * @param url API 请求地址
 * @param defaultParams 默认参数
 * @param config 配置选项
 * @param config.pageSize 每页大小，默认20
 * @param config.pageParamName 页码参数名，默认'page'
 * @param config.pageSizeParamName 每页大小参数名，默认'pageSize'
 * @param config.searchParamName 搜索参数名，默认'search'
 * @param config.returnParamsField 回显数据时用于传递参数的字段名
 * @returns 支持滚动加载的 API 请求函数
 */
export function createApiSelectPaginatedFunction(
  url: string,
  defaultParams: Record<string, any> = {},
  config: {
    pageParamName?: string;
    pageSize?: number;
    pageSizeParamName?: string;
    returnParamsField?: string;
    searchParamName?: string;
  } = {},
) {
  const {
    pageSize = 20,
    pageParamName = 'page',
    pageSizeParamName = 'pageSize',
    searchParamName = 'search',
  } = config;

  let allData: any[] = []; // 存储所有已加载的数据
  let currentPage = 1;
  let hasMore = true;
  let lastSearchValue = '';
  let lastParams = ''; // 存储上次的参数字符串，用于检测参数变化
  let isLoading = false;

  const apiFunction = async (params: Record<string, any> = {}) => {
    if (isLoading) return allData;

    const mergedParams = { ...defaultParams, ...params };

    // 处理搜索参数
    const possibleSearchParams = [
      searchParamName,
      'search',
      'keyword',
      'query',
      'searchValue',
      'inputValue',
    ];

    let searchValue = '';
    for (const paramName of possibleSearchParams) {
      if (params[paramName] && typeof params[paramName] === 'string') {
        searchValue = params[paramName];
        mergedParams[searchParamName] = searchValue;
        break;
      }
    }

    // 创建当前参数的字符串表示，用于检测参数变化
    const currentParamsString = JSON.stringify({
      ...mergedParams,
      [pageParamName]: 1, // 排除页码参数，因为页码变化不应该重置数据
      [pageSizeParamName]: pageSize, // 排除页大小参数
    });

    // 检查是否是新的搜索或参数变化，如果是则重置状态
    if (searchValue !== lastSearchValue || currentParamsString !== lastParams) {
      allData = [];
      currentPage = 1;
      hasMore = true;
      lastSearchValue = searchValue;
      lastParams = currentParamsString;
    }

    // 如果没有更多数据，直接返回已有数据
    if (!hasMore) {
      return allData;
    }

    isLoading = true;

    try {
      // 设置分页参数
      mergedParams[pageParamName] = currentPage;
      mergedParams[pageSizeParamName] = pageSize;

      // 过滤掉空值参数，避免发送 id= 这样的无效参数
      const filteredParams = Object.fromEntries(
        Object.entries(mergedParams).filter(([_key, value]) => {
          // 保留有效值：不是 undefined、null、空字符串或只有空格的字符串
          return (
            value !== undefined &&
            value !== null &&
            value !== '' &&
            !(typeof value === 'string' && value.trim() === '')
          );
        }),
      );

      const response = await requestClient.get(`${url}`, {
        params: filteredParams,
      });

      const newData = response.items === undefined ? response : response.items;

      // 如果是数组，追加到现有数据
      if (Array.isArray(newData)) {
        allData = [...allData, ...newData];

        // 如果返回的数据少于页面大小，说明没有更多数据了
        if (newData.length < pageSize) {
          hasMore = false;
        } else {
          currentPage++;
        }
      } else {
        // 如果不是数组，直接返回
        allData = newData;
        hasMore = false;
      }

      return allData;
    } finally {
      isLoading = false;
    }
  };

  // 添加 loadMore 方法
  apiFunction.loadMore = async () => {
    if (hasMore && !isLoading) {
      return await apiFunction({ [searchParamName]: lastSearchValue });
    }
    return allData;
  };

  // 添加状态查询方法
  apiFunction.hasMore = () => hasMore;
  apiFunction.isLoading = () => isLoading;

  return apiFunction;
}
