<script setup lang="ts">
import { nextTick, ref } from 'vue';

// import { BasicModal, useModalInner } from '/@/components/Modal';
import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const {
        url,
        fileType,
        openType = 'blank',
      } = modalApi.getData<Record<string, any>>();
      modalApi.setState({ loading: true });
      if (openType === 'blank') {
        window.open(
          ['pdf'].includes(fileType)
            ? url
            : `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`,
          '_blank',
        );
        modalApi.close();
        modalApi.setState({ loading: false });
        return;
      }
      originUrl.value = url;
      attachmentSrc.value = ['pdf'].includes(fileType)
        ? url
        : `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`;
      nextTick(() => {
        iframeRef.value?.addEventListener('load', () => {
          modalApi.setState({ loading: false });
        });
      });
    }
  },
});
const attachmentSrc = ref('');
const originUrl = ref<string>('');
const iframeRef = ref<HTMLElement | null>(null);
// const [registerModal, { changeLoading, closeModal }] = useModalInner(
//   async ({
//     url,
//     fileType,
//     openType = 'blank',
//   }: {
//     fileType: string;
//     openType: 'blank' | 'iframe';
//     url: string;
//   }) => {
//     changeLoading(true);
//     if (openType === 'blank') {
//       window.open(
//         ['pdf'].includes(fileType)
//           ? url
//           : `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`,
//         '_blank',
//       );
//       closeModal();
//       changeLoading(false);
//       return;
//     }
//     originUrl.value = url;
//     attachmentSrc.value = ['pdf'].includes(fileType)
//       ? url
//       : `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`;
//     nextTick(() => {
//       iframeRef.value?.addEventListener('load', () => {
//         changeLoading(false);
//       });
//     });
//   },
// );

function handleDownload() {
  window.open(originUrl.value, '_block');
}
</script>
<template>
  <Modal title="预览Office文件" destroy-on-close>
    <iframe
      v-if="attachmentSrc"
      ref="iframeRef"
      :src="attachmentSrc"
      width="100%"
      style="height: 750px"
    ></iframe>
    <template #footer>
      <Button @click="modalApi.close()">关闭</Button>
      <Button type="primary" @click="handleDownload">下载</Button>
    </template>
  </Modal>
  <!-- <BasicModal
    @register="registerModal"
    title="预览Office文件"
    default-fullscreen
    destroy-on-close
  >
    <iframe
      v-if="attachmentSrc"
      ref="iframeRef"
      :src="attachmentSrc"
      width="100%"
      style="height: 750px"
    ></iframe>
    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
      <a-button type="primary" @click="handleDownload">下载</a-button>
    </template>
  </BasicModal> -->
</template>

<style lang="less">
#tabeller {
  tr {
    background: #0000;
    border-top: 1px solid #dadde1;
  }
  td {
    border: 1px solid #dadde1 !important;
    padding: 0.75rem;
  }
}
</style>
