import { requestClient } from '#/api/request';

enum Api {
  create = '/erp/rolesPermissionType/create',
  detail = '/erp/rolesPermissionType/detail',
  getPageList = '/erp/rolesPermissionType/getPageList',
}

// 暴露 Api enum 供中间件使用
export { Api };
/**
 * 列表
 */
export async function listAction(params: any) {
  return requestClient.get(Api.getPageList, {
    params,
  });
}
/**
 * 创建和编辑
 */
export const create = (params: any) => {
  return requestClient.post(Api.create, params);
};

/**
 * 详情
 */
export const detail = (params: any) => {
  return requestClient.get(Api.detail, { params });
};
